# arm-gcc-toolchain.cmake
# Toolchain file for building bare-metal ARM Cortex-M projects in C

# Tell CMake this is a cross-compilation
set(CMAKE_SYSTEM_NAME Generic)
set(CMAKE_SYSTEM_PROCESSOR arm)

# Avoid building test executables during configuration
set(CMAKE_TRY_COMPILE_TARGET_TYPE STATIC_LIBRARY)

# Toolchain prefix
set(TOOLCHAIN_PREFIX arm-none-eabi-)

# Compilers and tools
set(CMAKE_C_COMPILER   ${TOOLCHAIN_PREFIX}gcc)
set(CMAKE_ASM_COMPILER ${TOOLCHAIN_PREFIX}gcc)
set(CMAKE_AR           ${TOOLCHAIN_PREFIX}ar)
set(CMAKE_LINKER       ${TOOLCHAIN_PREFIX}ld)
set(CMAKE_OBJCOPY      ${TOOLCHAIN_PREFIX}objcopy)
set(CMAKE_OBJDUMP      ${TOOLCHAIN_PREFIX}objdump)
set(CMAKE_SIZE         ${TOOLCHAIN_PREFIX}size)

# MCU and FPU options
set(MCU cortex-m4)
set(FPU fpv4-sp-d16)
set(FLOAT_ABI hard)

# Common architecture flags
set(ARCH_FLAGS "-mthumb -mcpu=${MCU} -mfpu=${FPU} -mfloat-abi=${FLOAT_ABI}")

# Compiler flags
set(CMAKE_C_FLAGS_INIT "${ARCH_FLAGS} -ffunction-sections -fdata-sections -MMD -MP -Wall")
set(CMAKE_C_FLAGS_DEBUG "-g -O0" CACHE STRING "C Debug Flags" FORCE)
set(CMAKE_C_FLAGS_RELEASE "-fomit-frame-pointer -O2" CACHE STRING "C Release Flags" FORCE)
set(CMAKE_ASM_FLAGS_INIT "${ARCH_FLAGS}")

# Linker flags
set(CMAKE_EXE_LINKER_FLAGS_INIT
    "${ARCH_FLAGS} -nostartfiles -static" 
    # todo: can we need these?
    # "-specs=nosys.specs -specs=nano.specs"
)

# Output extension
set(CMAKE_EXECUTABLE_SUFFIX ".elf")

