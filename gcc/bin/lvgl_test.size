   text	   data	    bss	    dec	    hex	filename
   4222	     72	     38	   4332	   10ec	bin/am_devices_display_generic.o
    132	     64	      0	    196	     c4	bin/am_resources.o
     94	      0	      0	     94	     5e	bin/am_util_delay.o
   1940	      0	    512	   2452	    994	bin/am_util_faultisr.o
    494	      0	      0	    494	    1ee	bin/am_util_id.o
   3971	      0	   1029	   5000	   1388	bin/am_util_stdio.o
      0	      0	      0	      0	      0	bin/am_util_syscalls.o
      0	      0	      0	      0	      0	bin/display_task_cpu_only.o
      0	      0	      0	      0	      0	bin/display_task_fake.o
      0	      0	      0	      0	      0	bin/display_task_one_and_partial_fb.o
      0	      0	      0	      0	      0	bin/display_task_one_fb.o
      0	      0	      0	      0	      0	bin/display_task_two_fb.o
   1366	      0	      0	   1366	    556	bin/event_groups.o
      0	      0	      0	      0	      0	bin/gifdec.o
    539	     44	     20	    603	    25b	bin/gui_task.o
   1138	      0	  65564	  66702	  1048e	bin/heap_4.o
    310	      0	      0	    310	    136	bin/list.o
      0	      0	      0	      0	      0	bin/lodepng.o
    448	      0	      0	    448	    1c0	bin/lv_ambiq_decoder.o
    800	      0	      0	    800	    320	bin/lv_ambiq_font_align.o
      0	      0	      0	      0	      0	bin/lv_ambiq_fs.o
    902	      0	      0	    902	    386	bin/lv_ambiq_misc.o
    667	      0	      1	    668	    29c	bin/lv_ambiq_nema_hal.o
      0	      0	      0	      0	      0	bin/lv_ambiq_touch.o
   2662	      0	     10	   2672	    a70	bin/lv_anim.o
   1842	      0	      0	   1842	    732	bin/lv_anim_timeline.o
    540	      0	      0	    540	    21c	bin/lv_animimg.o
   6468	      0	      0	   6468	   1944	bin/lv_arc.o
   3678	      0	      0	   3678	    e5e	bin/lv_area.o
    126	      0	      0	    126	     7e	bin/lv_async.o
   4402	      0	      0	   4402	   1132	bin/lv_bar.o
      0	      0	      0	      0	      0	bin/lv_bidi.o
      0	      0	      0	      0	      0	bin/lv_bmp.o
     98	      0	      0	     98	     62	bin/lv_btn.o
   9466	     28	      0	   9494	   2516	bin/lv_btnmatrix.o
   2731	     28	      0	   2759	    ac7	bin/lv_calendar.o
    806	     48	      0	    854	    356	bin/lv_calendar_header_arrow.o
   1161	      8	      0	   1169	    491	bin/lv_calendar_header_dropdown.o
   8158	      0	      0	   8158	   1fde	bin/lv_canvas.o
  17478	      0	      0	  17478	   4446	bin/lv_chart.o
   2214	      0	      0	   2214	    8a6	bin/lv_checkbox.o
   2778	      0	      0	   2778	    ada	bin/lv_color.o
   5378	      1	      5	   5384	   1508	bin/lv_colorwheel.o
   2286	      0	      0	   2286	    8ee	bin/lv_disp.o
   8494	      0	      0	   8494	   212e	bin/lv_draw_arc.o
   6706	      0	      0	   6706	   1a32	bin/lv_draw_blend.o
   4190	      0	      0	   4190	   105e	bin/lv_draw_img.o
   4870	      0	    261	   5131	   140b	bin/lv_draw_label.o
   4655	      0	      0	   4655	   122f	bin/lv_draw_line.o
  12030	      0	      0	  12030	   2efe	bin/lv_draw_mask.o
  13492	      0	      0	  13492	   34b4	bin/lv_draw_rect.o
   1490	      0	      0	   1490	    5d2	bin/lv_draw_triangle.o
   7657	      0	      0	   7657	   1de9	bin/lv_dropdown.o
   2775	      4	      4	   2783	    adf	bin/lv_event.o
   6373	      0	     14	   6387	   18f3	bin/lv_flex.o
    488	      0	      0	    488	    1e8	bin/lv_font.o
      0	      0	      0	      0	      0	bin/lv_font_dejavu_16_persian_hebrew.o
   1550	      0	      0	   1550	    60e	bin/lv_font_fmt_txt.o
   3789	      0	      0	   3789	    ecd	bin/lv_font_loader.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_10.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_12.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_12_subpx.o
   3485	  10144	      8	  13637	   3545	bin/lv_font_montserrat_14.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_16.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_18.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_20.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_22.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_24.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_26.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_28.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_28_compressed.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_30.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_32.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_34.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_36.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_38.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_40.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_42.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_44.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_46.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_48.o
      0	      0	      0	      0	      0	bin/lv_font_montserrat_8.o
      0	      0	      0	      0	      0	bin/lv_font_simsun_16_cjk.o
      0	      0	      0	      0	      0	bin/lv_font_unscii_16.o
      0	      0	      0	      0	      0	bin/lv_font_unscii_8.o
      0	      0	      0	      0	      0	bin/lv_freetype.o
   1832	      0	      0	   1832	    728	bin/lv_fs.o
      0	      0	      0	      0	      0	bin/lv_fs_fatfs.o
      0	      0	      0	      0	      0	bin/lv_fs_posix.o
      0	      0	      0	      0	      0	bin/lv_fs_stdio.o
      0	      0	      0	      0	      0	bin/lv_fs_win32.o
    188	      0	    520	    708	    2c4	bin/lv_gc.o
      0	      0	      0	      0	      0	bin/lv_gif.o
  10567	      0	     20	  10587	   295b	bin/lv_gpu_ambiq_nema.o
   6792	      0	     24	   6816	   1aa0	bin/lv_grid.o
   2764	      0	      4	   2768	    ad0	bin/lv_group.o
   4284	      0	     19	   4303	   10cf	bin/lv_hal_disp.o
    564	      0	      0	    564	    234	bin/lv_hal_indev.o
     90	      0	      0	     90	     5a	bin/lv_hal_tick.o
   6513	      0	      0	   6513	   1971	bin/lv_img.o
   7584	      0	      0	   7584	   1da0	bin/lv_img_buf.o
    148	      0	      0	    148	     94	bin/lv_img_cache.o
   4820	      0	      0	   4820	   12d4	bin/lv_img_decoder.o
   2120	      0	      0	   2120	    848	bin/lv_imgbtn.o
   6986	      0	      9	   6995	   1b53	bin/lv_indev.o
   5378	      0	      0	   5378	   1502	bin/lv_indev_scroll.o
   2894	     72	      0	   2966	    b96	bin/lv_keyboard.o
   8993	      0	      0	   8993	   2321	bin/lv_label.o
   1176	      0	      0	   1176	    498	bin/lv_led.o
   1072	      0	      0	   1072	    430	bin/lv_line.o
    405	      0	      0	    405	    195	bin/lv_list.o
   1252	      0	      0	   1252	    4e4	bin/lv_ll.o
    473	     20	      8	    501	    1f5	bin/lv_log.o
   1366	      4	      0	   1370	    55a	bin/lv_math.o
   5103	      4	     12	   5119	   13ff	bin/lv_mem.o
   6970	      0	      0	   6970	   1b3a	bin/lv_meter.o
   1643	      0	      0	   1643	    66b	bin/lv_msgbox.o
   7049	      0	      5	   7054	   1b8e	bin/lv_obj.o
    844	      0	      0	    844	    34c	bin/lv_obj_class.o
   4290	      0	      0	   4290	   10c2	bin/lv_obj_draw.o
  10761	      0	      5	  10766	   2a0e	bin/lv_obj_pos.o
   8674	      0	      0	   8674	   21e2	bin/lv_obj_scroll.o
   7672	      1	      0	   7673	   1df9	bin/lv_obj_style.o
   3504	      0	      0	   3504	    db0	bin/lv_obj_style_gen.o
   2258	      0	      0	   2258	    8d2	bin/lv_obj_tree.o
      0	      0	      0	      0	      0	bin/lv_objx_templ.o
      0	      0	      0	      0	      0	bin/lv_png.o
   3332	      0	      0	   3332	    d04	bin/lv_printf.o
      0	      0	      0	      0	      0	bin/lv_qrcode.o
   6486	      0	     32	   6518	   1976	bin/lv_refr.o
      0	      0	      0	      0	      0	bin/lv_rlottie.o
   5223	      0	      0	   5223	   1467	bin/lv_roller.o
      0	      0	      0	      0	      0	bin/lv_sjpg.o
   4060	      0	      0	   4060	    fdc	bin/lv_slider.o
   1246	      0	      0	   1246	    4de	bin/lv_snapshot.o
   7314	      0	   1284	   8598	   2196	bin/lv_span.o
   2508	      0	      0	   2508	    9cc	bin/lv_spinbox.o
    468	      0	      8	    476	    1dc	bin/lv_spinner.o
   2298	      2	      0	   2300	    8fc	bin/lv_style.o
   3148	      0	      0	   3148	    c4c	bin/lv_style_gen.o
   1830	      0	      0	   1830	    726	bin/lv_switch.o
   8809	      0	      0	   8809	   2269	bin/lv_table.o
   1946	      0	      3	   1949	    79d	bin/lv_tabview.o
      0	      0	      0	      0	      0	bin/lv_templ.o
   9243	      0	      4	   9247	   241f	bin/lv_textarea.o
    412	      0	      0	    412	    19c	bin/lv_theme.o
   4210	      0	     61	   4271	   10af	bin/lv_theme_basic.o
  12806	      0	    118	  12924	   327c	bin/lv_theme_default.o
   4806	      0	     45	   4851	   12f3	bin/lv_theme_mono.o
   1030	      0	      9	   1039	    40f	bin/lv_tileview.o
   1222	      0	     17	   1239	    4d7	bin/lv_timer.o
   8862	      0	      0	   8862	   229e	bin/lv_tlsf.o
   3750	     32	      0	   3782	    ec6	bin/lv_txt.o
      0	      0	      0	      0	      0	bin/lv_txt_ap.o
    126	      0	      0	    126	     7e	bin/lv_utils.o
    410	      0	      2	    412	    19c	bin/lv_win.o
    119	      0	  10248	  10367	   287f	bin/lvgl_test.o
   3918	      0	     29	   3947	    f6b	bin/nema_dc_hal.o
   1227	      4	     32	   1263	    4ef	bin/nema_event.o
   1084	      4	     36	   1124	    464	bin/nema_hal.o
    933	      8	     53	    994	    3e2	bin/nema_utils.o
   1008	      4	      5	   1017	    3f9	bin/port.o
  11357	      4	      0	  11361	   2c61	bin/qrcodegen.o
   4310	      0	      0	   4310	   10d6	bin/queue.o
    624	      0	     16	    640	    280	bin/rtos.o
  10175	      0	    488	  10663	   29a7	bin/tasks.o
   2114	      0	     60	   2174	    87e	bin/timers.o
      0	      0	      0	      0	      0	bin/tjpgd.o
    628	  11008	      0	  11636	   2d74	bin/startup_gcc.o
  16598	      0	   6732	  23330	   5b22	bin/am_hal_mspi.o
   4067	      0	    314	   4381	   111d	bin/board_init.o
 233702	      0	      4	 233706	  390ea	bin/AMOLED.o
   6833	    158	     36	   7027	   1b73	bin/aps12808l.o
  35372	      0	    196	  35568	   8af0	bin/hynitron_core.o
  17380	      0	      0	  17380	   43e4	bin/hynitron_cst92xx.o
   1683	     20	     20	   1723	    6bb	bin/I2C.o
   5438	     30	 165200	 170668	  29aac	bin/sh8601_apollo4.o
   1150	      4	      2	   1156	    484	bin/sh8601_example.o
    721	      0	    463	   1184	    4a0	bin/UART.o
    651	      0	    112	    763	    2fb	bin/minimal_display_task.o
    469	      0	      4	    473	    1d9	bin/minimal_mem.o
     46	      0	      0	     46	     2e	arm_abs_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_abs_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     64	      0	      0	     64	     40	arm_abs_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     40	      0	      0	     40	     28	arm_abs_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     38	      0	      0	     38	     26	arm_abs_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     40	      0	      0	     40	     28	arm_abs_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     58	      0	      0	     58	     3a	arm_add_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_add_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     68	      0	      0	     68	     44	arm_add_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_add_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_add_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     36	      0	      0	     36	     24	arm_add_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_and_u16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_and_u32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_and_u8.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    116	      0	      0	    116	     74	arm_clip_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     62	      0	      0	     62	     3e	arm_clip_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     60	      0	      0	     60	     3c	arm_clip_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     60	      0	      0	     60	     3c	arm_clip_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     60	      0	      0	     60	     3c	arm_clip_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     76	      0	      0	     76	     4c	arm_dot_prod_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     48	      0	      0	     48	     30	arm_dot_prod_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    104	      0	      0	    104	     68	arm_dot_prod_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     42	      0	      0	     42	     2a	arm_dot_prod_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     58	      0	      0	     58	     3a	arm_dot_prod_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     38	      0	      0	     38	     26	arm_dot_prod_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     58	      0	      0	     58	     3a	arm_mult_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_mult_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     68	      0	      0	     68	     44	arm_mult_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     38	      0	      0	     38	     26	arm_mult_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     42	      0	      0	     42	     2a	arm_mult_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     38	      0	      0	     38	     26	arm_mult_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     46	      0	      0	     46	     2e	arm_negate_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_negate_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     48	      0	      0	     48	     30	arm_negate_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     40	      0	      0	     40	     28	arm_negate_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_negate_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_negate_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_not_u16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_not_u32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_not_u8.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     58	      0	      0	     58	     3a	arm_offset_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_offset_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     62	      0	      0	     62	     3e	arm_offset_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_offset_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_offset_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     32	      0	      0	     32	     20	arm_offset_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_or_u16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_or_u32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_or_u8.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     58	      0	      0	     58	     3a	arm_scale_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_scale_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     62	      0	      0	     62	     3e	arm_scale_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     60	      0	      0	     60	     3c	arm_scale_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    104	      0	      0	    104	     68	arm_scale_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     60	      0	      0	     60	     3c	arm_scale_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     62	      0	      0	     62	     3e	arm_shift_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    168	      0	      0	    168	     a8	arm_shift_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     62	      0	      0	     62	     3e	arm_shift_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     58	      0	      0	     58	     3a	arm_sub_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_sub_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     68	      0	      0	     68	     44	arm_sub_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_sub_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_sub_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     38	      0	      0	     38	     26	arm_sub_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_xor_u16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_xor_u32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_xor_u8.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    180	      0	      0	    180	     b4	arm_barycenter_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    132	      0	      0	    132	     84	arm_barycenter_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    252	      0	      0	    252	     fc	arm_bitonic_sort_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    118	      0	      0	    118	     76	arm_bubble_sort_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     26	      0	      0	     26	     1a	arm_copy_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     26	      0	      0	     26	     1a	arm_copy_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     26	      0	      0	     26	     1a	arm_copy_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     26	      0	      0	     26	     1a	arm_copy_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     26	      0	      0	     26	     1a	arm_copy_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     26	      0	      0	     26	     1a	arm_copy_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     54	      0	      0	     54	     36	arm_f16_to_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_f16_to_float.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     80	      0	      0	     80	     50	arm_f16_to_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     44	      0	      0	     44	     2c	arm_f64_to_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     44	      0	      0	     44	     2c	arm_f64_to_float.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     80	      0	      0	     80	     50	arm_f64_to_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    136	      0	      0	    136	     88	arm_f64_to_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     80	      0	      0	     80	     50	arm_f64_to_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_fill_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     22	      0	      0	     22	     16	arm_fill_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     22	      0	      0	     22	     16	arm_fill_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     22	      0	      0	     22	     16	arm_fill_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     22	      0	      0	     22	     16	arm_fill_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     28	      0	      0	     28	     1c	arm_fill_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_float_to_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     42	      0	      0	     42	     2a	arm_float_to_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     52	      0	      0	     52	     34	arm_float_to_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     48	      0	      0	     48	     30	arm_float_to_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     52	      0	      0	     52	     34	arm_float_to_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    232	      0	      0	    232	     e8	arm_heap_sort_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    114	      0	      0	    114	     72	arm_insertion_sort_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    236	      0	      0	    236	     ec	arm_merge_sort_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     14	      0	      0	     14	      e	arm_merge_sort_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     64	      0	      0	     64	     40	arm_q15_to_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     72	      0	      0	     72	     48	arm_q15_to_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     48	      0	      0	     48	     30	arm_q15_to_float.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_q15_to_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     26	      0	      0	     26	     1a	arm_q15_to_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     72	      0	      0	     72	     48	arm_q31_to_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     48	      0	      0	     48	     30	arm_q31_to_float.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     26	      0	      0	     26	     1a	arm_q31_to_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     26	      0	      0	     26	     1a	arm_q31_to_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     72	      0	      0	     72	     48	arm_q7_to_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     48	      0	      0	     48	     30	arm_q7_to_float.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_q7_to_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_q7_to_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    258	      0	      0	    258	    102	arm_quick_sort_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    138	      0	      0	    138	     8a	arm_selection_sort_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     80	      0	      0	     80	     50	arm_sort_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     14	      0	      0	     14	      e	arm_sort_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    128	      0	      0	    128	     80	arm_weighted_sum_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     64	      0	      0	     64	     40	arm_weighted_sum_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
 919138	      0	      0	 919138	  e0662	arm_common_tables.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
  49024	      0	      0	  49024	   bf80	arm_common_tables_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1392	      0	      0	   1392	    570	arm_const_structs.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    144	      0	      0	    144	     90	arm_const_structs_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
      0	      0	      0	      0	      0	arm_mve_tables.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
      0	      0	      0	      0	      0	arm_mve_tables_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    240	      0	      0	    240	     f0	arm_bilinear_interp_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    164	      0	      0	    164	     a4	arm_bilinear_interp_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    216	      0	      0	    216	     d8	arm_bilinear_interp_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    152	      0	      0	    152	     98	arm_bilinear_interp_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    144	      0	      0	    144	     90	arm_bilinear_interp_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    224	      0	      0	    224	     e0	arm_linear_interp_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    140	      0	      0	    140	     8c	arm_linear_interp_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     78	      0	      0	     78	     4e	arm_linear_interp_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     74	      0	      0	     74	     4a	arm_linear_interp_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     66	      0	      0	     66	     42	arm_linear_interp_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    340	      0	      0	    340	    154	arm_spline_interp_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    488	      0	      0	    488	    1e8	arm_spline_interp_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    682	      0	      0	    682	    2aa	arm_boolean_distance.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    140	      0	      0	    140	     8c	arm_braycurtis_distance_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     76	      0	      0	     76	     4c	arm_braycurtis_distance_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    160	      0	      0	    160	     a0	arm_canberra_distance_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    116	      0	      0	    116	     74	arm_canberra_distance_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    118	      0	      0	    118	     76	arm_chebyshev_distance_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     72	      0	      0	     72	     48	arm_chebyshev_distance_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    152	      0	      0	    152	     98	arm_chebyshev_distance_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     84	      0	      0	     84	     54	arm_cityblock_distance_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     52	      0	      0	     52	     34	arm_cityblock_distance_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    120	      0	      0	    120	     78	arm_cityblock_distance_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    308	      0	      0	    308	    134	arm_correlation_distance_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    196	      0	      0	    196	     c4	arm_correlation_distance_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    152	      0	      0	    152	     98	arm_cosine_distance_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    104	      0	      0	    104	     68	arm_cosine_distance_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    128	      0	      0	    128	     80	arm_cosine_distance_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    140	      0	      0	    140	     8c	arm_dice_distance.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    476	      0	      0	    476	    1dc	arm_dtw_distance_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    226	      0	      0	    226	     e2	arm_dtw_init_window_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    368	      0	      0	    368	    170	arm_dtw_path_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    120	      0	      0	    120	     78	arm_euclidean_distance_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     68	      0	      0	     68	     44	arm_euclidean_distance_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    120	      0	      0	    120	     78	arm_euclidean_distance_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     76	      0	      0	     76	     4c	arm_hamming_distance.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     82	      0	      0	     82	     52	arm_jaccard_distance.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    232	      0	      0	    232	     e8	arm_jensenshannon_distance_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    140	      0	      0	    140	     8c	arm_jensenshannon_distance_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     88	      0	      0	     88	     58	arm_kulsinski_distance.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    148	      0	      0	    148	     94	arm_minkowski_distance_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    104	      0	      0	    104	     68	arm_minkowski_distance_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     94	      0	      0	     94	     5e	arm_rogerstanimoto_distance.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     54	      0	      0	     54	     36	arm_russellrao_distance.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     98	      0	      0	     98	     62	arm_sokalmichener_distance.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     84	      0	      0	     84	     54	arm_sokalsneath_distance.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    128	      0	      0	    128	     80	arm_yule_distance.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    488	      0	      0	    488	    1e8	arm_bitreversal.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    294	      0	      0	    294	    126	arm_bitreversal2.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    188	      0	      0	    188	     bc	arm_bitreversal_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    274	      0	      0	    274	    112	arm_cfft_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1938	      0	      0	   1938	    792	arm_cfft_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1582	      0	      0	   1582	    62e	arm_cfft_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    540	      0	      0	    540	    21c	arm_cfft_init_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    540	      0	      0	    540	    21c	arm_cfft_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    540	      0	      0	    540	    21c	arm_cfft_init_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    540	      0	      0	    540	    21c	arm_cfft_init_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    540	      0	      0	    540	    21c	arm_cfft_init_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    630	      0	      0	    630	    276	arm_cfft_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    784	      0	      0	    784	    310	arm_cfft_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1326	      0	      0	   1326	    52e	arm_cfft_radix2_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    852	      0	      0	    852	    354	arm_cfft_radix2_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    244	      0	      0	    244	     f4	arm_cfft_radix2_init_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    236	      0	      0	    236	     ec	arm_cfft_radix2_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    160	      0	      0	    160	     a0	arm_cfft_radix2_init_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    160	      0	      0	    160	     a0	arm_cfft_radix2_init_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    992	      0	      0	    992	    3e0	arm_cfft_radix2_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1072	      0	      0	   1072	    430	arm_cfft_radix2_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   3156	      0	      0	   3156	    c54	arm_cfft_radix4_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1122	      0	      0	   1122	    462	arm_cfft_radix4_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    160	      0	      0	    160	     a0	arm_cfft_radix4_init_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    156	      0	      0	    156	     9c	arm_cfft_radix4_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    110	      0	      0	    110	     6e	arm_cfft_radix4_init_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    110	      0	      0	    110	     6e	arm_cfft_radix4_init_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1228	      0	      0	   1228	    4cc	arm_cfft_radix4_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1982	      0	      0	   1982	    7be	arm_cfft_radix4_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   2088	      0	      0	   2088	    828	arm_cfft_radix8_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1412	      0	      0	   1412	    584	arm_cfft_radix8_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    210	      0	      0	    210	     d2	arm_dct4_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    160	      0	      0	    160	     a0	arm_dct4_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    150	      0	      0	    150	     96	arm_dct4_init_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    150	      0	      0	    150	     96	arm_dct4_init_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    204	      0	      0	    204	     cc	arm_dct4_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    220	      0	      0	    220	     dc	arm_dct4_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    252	      0	      0	    252	     fc	arm_mfcc_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    240	      0	      0	    240	     f0	arm_mfcc_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    500	      0	      0	    500	    1f4	arm_mfcc_init_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    500	      0	      0	    500	    1f4	arm_mfcc_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    550	      0	      0	    550	    226	arm_mfcc_init_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    550	      0	      0	    550	    226	arm_mfcc_init_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    332	      0	      0	    332	    14c	arm_mfcc_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    308	      0	      0	    308	    134	arm_mfcc_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    540	      0	      0	    540	    21c	arm_rfft_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    638	      0	      0	    638	    27e	arm_rfft_fast_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    442	      0	      0	    442	    1ba	arm_rfft_fast_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1004	      0	      0	   1004	    3ec	arm_rfft_fast_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    618	      0	      0	    618	    26a	arm_rfft_fast_init_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    618	      0	      0	    618	    26a	arm_rfft_fast_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    898	      0	      0	    898	    382	arm_rfft_fast_init_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    124	      0	      0	    124	     7c	arm_rfft_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    774	      0	      0	    774	    306	arm_rfft_init_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    774	      0	      0	    774	    306	arm_rfft_init_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    378	      0	      0	    378	    17a	arm_rfft_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    538	      0	      0	    538	    21a	arm_rfft_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_svm_linear_init_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     32	      0	      0	     32	     20	arm_svm_linear_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    200	      0	      0	    200	     c8	arm_svm_linear_predict_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    140	      0	      0	    140	     8c	arm_svm_linear_predict_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     52	      0	      0	     52	     34	arm_svm_polynomial_init_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     48	      0	      0	     48	     30	arm_svm_polynomial_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    324	      0	      0	    324	    144	arm_svm_polynomial_predict_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    192	      0	      0	    192	     c0	arm_svm_polynomial_predict_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     42	      0	      0	     42	     2a	arm_svm_rbf_init_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     36	      0	      0	     36	     24	arm_svm_rbf_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    240	      0	      0	    240	     f0	arm_svm_rbf_predict_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    164	      0	      0	    164	     a4	arm_svm_rbf_predict_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     48	      0	      0	     48	     30	arm_svm_sigmoid_init_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     40	      0	      0	     40	     28	arm_svm_sigmoid_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    252	      0	      0	    252	     fc	arm_svm_sigmoid_predict_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    168	      0	      0	    168	     a8	arm_svm_sigmoid_predict_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_biquad_cascade_df1_32x64_init_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    276	      0	      0	    276	    114	arm_biquad_cascade_df1_32x64_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    352	      0	      0	    352	    160	arm_biquad_cascade_df1_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    168	      0	      0	    168	     a8	arm_biquad_cascade_df1_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    138	      0	      0	    138	     8a	arm_biquad_cascade_df1_fast_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    160	      0	      0	    160	     a0	arm_biquad_cascade_df1_fast_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_biquad_cascade_df1_init_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_biquad_cascade_df1_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_biquad_cascade_df1_init_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_biquad_cascade_df1_init_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    298	      0	      0	    298	    12a	arm_biquad_cascade_df1_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    194	      0	      0	    194	     c2	arm_biquad_cascade_df1_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    268	      0	      0	    268	    10c	arm_biquad_cascade_df2t_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    126	      0	      0	    126	     7e	arm_biquad_cascade_df2t_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    240	      0	      0	    240	     f0	arm_biquad_cascade_df2t_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_biquad_cascade_df2t_init_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_biquad_cascade_df2t_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_biquad_cascade_df2t_init_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    404	      0	      0	    404	    194	arm_biquad_cascade_stereo_df2t_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    194	      0	      0	    194	     c2	arm_biquad_cascade_stereo_df2t_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_biquad_cascade_stereo_df2t_init_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_biquad_cascade_stereo_df2t_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    348	      0	      0	    348	    15c	arm_conv_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    232	      0	      0	    232	     e8	arm_conv_fast_opt_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1056	      0	      0	   1056	    420	arm_conv_fast_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    862	      0	      0	    862	    35e	arm_conv_fast_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    244	      0	      0	    244	     f4	arm_conv_opt_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    634	      0	      0	    634	    27a	arm_conv_opt_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    492	      0	      0	    492	    1ec	arm_conv_partial_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    254	      0	      0	    254	     fe	arm_conv_partial_fast_opt_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1204	      0	      0	   1204	    4b4	arm_conv_partial_fast_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    434	      0	      0	    434	    1b2	arm_conv_partial_fast_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    266	      0	      0	    266	    10a	arm_conv_partial_opt_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    634	      0	      0	    634	    27a	arm_conv_partial_opt_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1414	      0	      0	   1414	    586	arm_conv_partial_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    476	      0	      0	    476	    1dc	arm_conv_partial_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    432	      0	      0	    432	    1b0	arm_conv_partial_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1300	      0	      0	   1300	    514	arm_conv_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    348	      0	      0	    348	    15c	arm_conv_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    304	      0	      0	    304	    130	arm_conv_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    460	      0	      0	    460	    1cc	arm_correlate_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    364	      0	      0	    364	    16c	arm_correlate_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    512	      0	      0	    512	    200	arm_correlate_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    236	      0	      0	    236	     ec	arm_correlate_fast_opt_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    978	      0	      0	    978	    3d2	arm_correlate_fast_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    944	      0	      0	    944	    3b0	arm_correlate_fast_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    276	      0	      0	    276	    114	arm_correlate_opt_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    678	      0	      0	    678	    2a6	arm_correlate_opt_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1078	      0	      0	   1078	    436	arm_correlate_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    338	      0	      0	    338	    152	arm_correlate_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    272	      0	      0	    272	    110	arm_correlate_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    164	      0	      0	    164	     a4	arm_fir_decimate_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    306	      0	      0	    306	    132	arm_fir_decimate_fast_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    142	      0	      0	    142	     8e	arm_fir_decimate_fast_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     66	      0	      0	     66	     42	arm_fir_decimate_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     66	      0	      0	     66	     42	arm_fir_decimate_init_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     66	      0	      0	     66	     42	arm_fir_decimate_init_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    334	      0	      0	    334	    14e	arm_fir_decimate_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    152	      0	      0	    152	     98	arm_fir_decimate_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    164	      0	      0	    164	     a4	arm_fir_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    144	      0	      0	    144	     90	arm_fir_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    200	      0	      0	    200	     c8	arm_fir_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    138	      0	      0	    138	     8a	arm_fir_fast_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    118	      0	      0	    118	     76	arm_fir_fast_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     40	      0	      0	     40	     28	arm_fir_init_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     40	      0	      0	     40	     28	arm_fir_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     40	      0	      0	     40	     28	arm_fir_init_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     46	      0	      0	     46	     2e	arm_fir_init_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     40	      0	      0	     40	     28	arm_fir_init_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_fir_init_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    192	      0	      0	    192	     c0	arm_fir_interpolate_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     62	      0	      0	     62	     3e	arm_fir_interpolate_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     62	      0	      0	     62	     3e	arm_fir_interpolate_init_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     62	      0	      0	     62	     3e	arm_fir_interpolate_init_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    174	      0	      0	    174	     ae	arm_fir_interpolate_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    170	      0	      0	    170	     aa	arm_fir_interpolate_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    134	      0	      0	    134	     86	arm_fir_lattice_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_fir_lattice_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_fir_lattice_init_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     30	      0	      0	     30	     1e	arm_fir_lattice_init_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    160	      0	      0	    160	     a0	arm_fir_lattice_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    140	      0	      0	    140	     8c	arm_fir_lattice_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    164	      0	      0	    164	     a4	arm_fir_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    122	      0	      0	    122	     7a	arm_fir_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    130	      0	      0	    130	     82	arm_fir_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    386	      0	      0	    386	    182	arm_fir_sparse_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     50	      0	      0	     50	     32	arm_fir_sparse_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     50	      0	      0	     50	     32	arm_fir_sparse_init_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     50	      0	      0	     50	     32	arm_fir_sparse_init_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     48	      0	      0	     48	     30	arm_fir_sparse_init_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    406	      0	      0	    406	    196	arm_fir_sparse_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    398	      0	      0	    398	    18e	arm_fir_sparse_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    406	      0	      0	    406	    196	arm_fir_sparse_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    204	      0	      0	    204	     cc	arm_iir_lattice_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     36	      0	      0	     36	     24	arm_iir_lattice_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     36	      0	      0	     36	     24	arm_iir_lattice_init_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     36	      0	      0	     36	     24	arm_iir_lattice_init_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    266	      0	      0	    266	    10a	arm_iir_lattice_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    304	      0	      0	    304	    130	arm_iir_lattice_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    524	      0	      0	    524	    20c	arm_levinson_durbin_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    340	      0	      0	    340	    154	arm_levinson_durbin_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    522	      0	      0	    522	    20a	arm_levinson_durbin_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    228	      0	      0	    228	     e4	arm_lms_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     56	      0	      0	     56	     38	arm_lms_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     52	      0	      0	     52	     34	arm_lms_init_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     52	      0	      0	     52	     34	arm_lms_init_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    296	      0	      0	    296	    128	arm_lms_norm_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     62	      0	      0	     62	     3e	arm_lms_norm_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     66	      0	      0	     66	     42	arm_lms_norm_init_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     68	      0	      0	     68	     44	arm_lms_norm_init_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    418	      0	      0	    418	    1a2	arm_lms_norm_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    618	      0	      0	    618	    26a	arm_lms_norm_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    262	      0	      0	    262	    106	arm_lms_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    294	      0	      0	    294	    126	arm_lms_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    202	      0	      0	    202	     ca	arm_quaternion2rotation_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     74	      0	      0	     74	     4a	arm_quaternion_conjugate_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    130	      0	      0	    130	     82	arm_quaternion_inverse_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    156	      0	      0	    156	     9c	arm_quaternion_normalize_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    104	      0	      0	    104	     68	arm_quaternion_norm_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     50	      0	      0	     50	     32	arm_quaternion_product_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    266	      0	      0	    266	    10a	arm_quaternion_product_single_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    546	      0	      0	    546	    222	arm_rotation2quaternion_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    320	      0	      0	    320	    140	arm_gaussian_naive_bayes_predict_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    224	      0	      0	    224	     e0	arm_gaussian_naive_bayes_predict_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     64	      0	      0	     64	     40	arm_pid_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     58	      0	      0	     58	     3a	arm_pid_init_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     48	      0	      0	     48	     30	arm_pid_init_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     18	      0	      0	     18	     12	arm_pid_reset_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     16	      0	      0	     16	     10	arm_pid_reset_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     18	      0	      0	     18	     12	arm_pid_reset_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    312	      0	      0	    312	    138	arm_sin_cos_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    618	      0	      0	    618	    26a	arm_sin_cos_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    108	      0	      0	    108	     6c	arm_absmax_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     78	      0	      0	     78	     4e	arm_absmax_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    184	      0	      0	    184	     b8	arm_absmax_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     82	      0	      0	     82	     52	arm_absmax_no_idx_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     56	      0	      0	     56	     38	arm_absmax_no_idx_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    120	      0	      0	    120	     78	arm_absmax_no_idx_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    186	      0	      0	    186	     ba	arm_absmax_no_idx_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    154	      0	      0	    154	     9a	arm_absmax_no_idx_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    190	      0	      0	    190	     be	arm_absmax_no_idx_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    258	      0	      0	    258	    102	arm_absmax_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    206	      0	      0	    206	     ce	arm_absmax_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    258	      0	      0	    258	    102	arm_absmax_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    108	      0	      0	    108	     6c	arm_absmin_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     78	      0	      0	     78	     4e	arm_absmin_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    184	      0	      0	    184	     b8	arm_absmin_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     82	      0	      0	     82	     52	arm_absmin_no_idx_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     56	      0	      0	     56	     38	arm_absmin_no_idx_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    120	      0	      0	    120	     78	arm_absmin_no_idx_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    186	      0	      0	    186	     ba	arm_absmin_no_idx_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    154	      0	      0	    154	     9a	arm_absmin_no_idx_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    190	      0	      0	    190	     be	arm_absmin_no_idx_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    258	      0	      0	    258	    102	arm_absmin_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    206	      0	      0	    206	     ce	arm_absmin_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    258	      0	      0	    258	    102	arm_absmin_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     60	      0	      0	     60	     3c	arm_accumulate_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     40	      0	      0	     40	     28	arm_accumulate_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     72	      0	      0	     72	     48	arm_accumulate_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    112	      0	      0	    112	     70	arm_entropy_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     72	      0	      0	     72	     48	arm_entropy_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    120	      0	      0	    120	     78	arm_entropy_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    128	      0	      0	    128	     80	arm_kullback_leibler_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     80	      0	      0	     80	     50	arm_kullback_leibler_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    144	      0	      0	    144	     90	arm_kullback_leibler_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_logsumexp_dot_prod_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_logsumexp_dot_prod_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    184	      0	      0	    184	     b8	arm_logsumexp_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    112	      0	      0	    112	     70	arm_logsumexp_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    100	      0	      0	    100	     64	arm_max_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     70	      0	      0	     70	     46	arm_max_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    144	      0	      0	    144	     90	arm_max_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     72	      0	      0	     72	     48	arm_max_no_idx_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     48	      0	      0	     48	     30	arm_max_no_idx_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     96	      0	      0	     96	     60	arm_max_no_idx_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     48	      0	      0	     48	     30	arm_max_no_idx_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_max_no_idx_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     48	      0	      0	     48	     30	arm_max_no_idx_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     62	      0	      0	     62	     3e	arm_max_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     54	      0	      0	     54	     36	arm_max_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     62	      0	      0	     62	     3e	arm_max_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     92	      0	      0	     92	     5c	arm_mean_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     52	      0	      0	     52	     34	arm_mean_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    104	      0	      0	    104	     68	arm_mean_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     38	      0	      0	     38	     26	arm_mean_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     56	      0	      0	     56	     38	arm_mean_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     38	      0	      0	     38	     26	arm_mean_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    100	      0	      0	    100	     64	arm_min_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     70	      0	      0	     70	     46	arm_min_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    144	      0	      0	    144	     90	arm_min_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     72	      0	      0	     72	     48	arm_min_no_idx_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     48	      0	      0	     48	     30	arm_min_no_idx_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     96	      0	      0	     96	     60	arm_min_no_idx_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     48	      0	      0	     48	     30	arm_min_no_idx_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_min_no_idx_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     48	      0	      0	     48	     30	arm_min_no_idx_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     62	      0	      0	     62	     3e	arm_min_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     54	      0	      0	     54	     36	arm_min_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     62	      0	      0	     62	     3e	arm_min_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    116	      0	      0	    116	     74	arm_mse_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     64	      0	      0	     64	     40	arm_mse_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    128	      0	      0	    128	     80	arm_mse_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     82	      0	      0	     82	     52	arm_mse_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     88	      0	      0	     88	     58	arm_mse_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     64	      0	      0	     64	     40	arm_mse_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     64	      0	      0	     64	     40	arm_power_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     44	      0	      0	     44	     2c	arm_power_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     88	      0	      0	     88	     58	arm_power_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     44	      0	      0	     44	     2c	arm_power_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     54	      0	      0	     54	     36	arm_power_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     28	      0	      0	     28	     1c	arm_power_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    124	      0	      0	    124	     7c	arm_rms_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     80	      0	      0	     80	     50	arm_rms_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     76	      0	      0	     76	     4c	arm_rms_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     76	      0	      0	     76	     4c	arm_rms_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     80	      0	      0	     80	     50	arm_std_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     56	      0	      0	     56	     38	arm_std_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     36	      0	      0	     36	     24	arm_std_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    106	      0	      0	    106	     6a	arm_std_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    136	      0	      0	    136	     88	arm_std_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    180	      0	      0	    180	     b4	arm_var_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    100	      0	      0	    100	     64	arm_var_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    168	      0	      0	    168	     a8	arm_var_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     88	      0	      0	     88	     58	arm_var_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    126	      0	      0	    126	     7e	arm_var_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     54	      0	      0	     54	     36	arm_cmplx_conj_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     42	      0	      0	     42	     2a	arm_cmplx_conj_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     42	      0	      0	     42	     2a	arm_cmplx_conj_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     42	      0	      0	     42	     2a	arm_cmplx_conj_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    164	      0	      0	    164	     a4	arm_cmplx_dot_prod_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    108	      0	      0	    108	     6c	arm_cmplx_dot_prod_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    102	      0	      0	    102	     66	arm_cmplx_dot_prod_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    162	      0	      0	    162	     a2	arm_cmplx_dot_prod_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     70	      0	      0	     70	     46	arm_cmplx_mag_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     50	      0	      0	     50	     32	arm_cmplx_mag_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     96	      0	      0	     96	     60	arm_cmplx_mag_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     40	      0	      0	     40	     28	arm_cmplx_mag_fast_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     56	      0	      0	     56	     38	arm_cmplx_mag_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     48	      0	      0	     48	     30	arm_cmplx_mag_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     66	      0	      0	     66	     42	arm_cmplx_mag_squared_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     46	      0	      0	     46	     2e	arm_cmplx_mag_squared_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     92	      0	      0	     92	     5c	arm_cmplx_mag_squared_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     34	      0	      0	     34	     22	arm_cmplx_mag_squared_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     42	      0	      0	     42	     2a	arm_cmplx_mag_squared_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    114	      0	      0	    114	     72	arm_cmplx_mult_cmplx_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     78	      0	      0	     78	     4e	arm_cmplx_mult_cmplx_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    184	      0	      0	    184	     b8	arm_cmplx_mult_cmplx_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     70	      0	      0	     70	     46	arm_cmplx_mult_cmplx_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     62	      0	      0	     62	     3e	arm_cmplx_mult_cmplx_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     86	      0	      0	     86	     56	arm_cmplx_mult_real_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     54	      0	      0	     54	     36	arm_cmplx_mult_real_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     54	      0	      0	     54	     36	arm_cmplx_mult_real_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     70	      0	      0	     70	     46	arm_cmplx_mult_real_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    280	      0	      0	    280	    118	arm_householder_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    192	      0	      0	    192	     c0	arm_householder_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    328	      0	      0	    328	    148	arm_householder_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     82	      0	      0	     82	     52	arm_mat_add_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     52	      0	      0	     52	     34	arm_mat_add_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     58	      0	      0	     58	     3a	arm_mat_add_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     58	      0	      0	     58	     3a	arm_mat_add_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    334	      0	      0	    334	    14e	arm_mat_cholesky_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    254	      0	      0	    254	     fe	arm_mat_cholesky_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    336	      0	      0	    336	    150	arm_mat_cholesky_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    268	      0	      0	    268	    10c	arm_mat_cmplx_mult_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    196	      0	      0	    196	     c4	arm_mat_cmplx_mult_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    374	      0	      0	    374	    176	arm_mat_cmplx_mult_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    352	      0	      0	    352	    160	arm_mat_cmplx_mult_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     76	      0	      0	     76	     4c	arm_mat_cmplx_trans_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     84	      0	      0	     84	     54	arm_mat_cmplx_trans_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     80	      0	      0	     80	     50	arm_mat_cmplx_trans_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     84	      0	      0	     84	     54	arm_mat_cmplx_trans_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     16	      0	      0	     16	     10	arm_mat_init_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     16	      0	      0	     16	     10	arm_mat_init_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     16	      0	      0	     16	     10	arm_mat_init_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     16	      0	      0	     16	     10	arm_mat_init_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     16	      0	      0	     16	     10	arm_mat_init_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1060	      0	      0	   1060	    424	arm_mat_inverse_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    824	      0	      0	    824	    338	arm_mat_inverse_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1208	      0	      0	   1208	    4b8	arm_mat_inverse_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    564	      0	      0	    564	    234	arm_mat_ldlt_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    760	      0	      0	    760	    2f8	arm_mat_ldlt_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    160	      0	      0	    160	     a0	arm_mat_mult_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    136	      0	      0	    136	     88	arm_mat_mult_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    240	      0	      0	    240	     f0	arm_mat_mult_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    892	      0	      0	    892	    37c	arm_mat_mult_fast_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    472	      0	      0	    472	    1d8	arm_mat_mult_fast_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    120	      0	      0	    120	     78	arm_mat_mult_opt_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    258	      0	      0	    258	    102	arm_mat_mult_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    120	      0	      0	    120	     78	arm_mat_mult_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    134	      0	      0	    134	     86	arm_mat_mult_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1790	      0	      0	   1790	    6fe	arm_mat_qr_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1508	      0	      0	   1508	    5e4	arm_mat_qr_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1996	      0	      0	   1996	    7cc	arm_mat_qr_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     68	      0	      0	     68	     44	arm_mat_scale_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     40	      0	      0	     40	     28	arm_mat_scale_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     66	      0	      0	     66	     42	arm_mat_scale_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     78	      0	      0	     78	     4e	arm_mat_scale_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    250	      0	      0	    250	     fa	arm_mat_solve_lower_triangular_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    186	      0	      0	    186	     ba	arm_mat_solve_lower_triangular_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    288	      0	      0	    288	    120	arm_mat_solve_lower_triangular_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    280	      0	      0	    280	    118	arm_mat_solve_upper_triangular_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    222	      0	      0	    222	     de	arm_mat_solve_upper_triangular_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    312	      0	      0	    312	    138	arm_mat_solve_upper_triangular_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     82	      0	      0	     82	     52	arm_mat_sub_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     52	      0	      0	     52	     34	arm_mat_sub_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     90	      0	      0	     90	     5a	arm_mat_sub_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     58	      0	      0	     58	     3a	arm_mat_sub_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     58	      0	      0	     58	     3a	arm_mat_sub_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     66	      0	      0	     66	     42	arm_mat_trans_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     66	      0	      0	     66	     42	arm_mat_trans_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    114	      0	      0	    114	     72	arm_mat_trans_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     66	      0	      0	     66	     42	arm_mat_trans_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     66	      0	      0	     66	     42	arm_mat_trans_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     68	      0	      0	     68	     44	arm_mat_trans_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    500	      0	      0	    500	    1f4	arm_mat_vec_mult_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    364	      0	      0	    364	    16c	arm_mat_vec_mult_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    566	      0	      0	    566	    236	arm_mat_vec_mult_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    380	      0	      0	    380	    17c	arm_mat_vec_mult_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    546	      0	      0	    546	    222	arm_mat_vec_mult_q7.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    914	      0	      0	    914	    392	arm_atan2_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    612	      0	      0	    612	    264	arm_atan2_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    666	      0	      0	    666	    29a	arm_atan2_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
   1398	      0	      0	   1398	    576	arm_atan2_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    156	      0	      0	    156	     9c	arm_cos_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     84	      0	      0	     84	     54	arm_cos_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     74	      0	      0	     74	     4a	arm_cos_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    116	      0	      0	    116	     74	arm_divide_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    148	      0	      0	    148	     94	arm_divide_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    148	      0	      0	    148	     94	arm_sin_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     70	      0	      0	     70	     46	arm_sin_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     66	      0	      0	     66	     42	arm_sin_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    180	      0	      0	    180	     b4	arm_sqrt_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    198	      0	      0	    198	     c6	arm_sqrt_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     54	      0	      0	     54	     36	arm_vexp_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     38	      0	      0	     38	     26	arm_vexp_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     40	      0	      0	     40	     28	arm_vexp_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     50	      0	      0	     50	     32	arm_vinverse_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    310	      0	      0	    310	    136	arm_vlog_f16.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     38	      0	      0	     38	     26	arm_vlog_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     40	      0	      0	     40	     28	arm_vlog_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    114	      0	      0	    114	     72	arm_vlog_q15.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    114	      0	      0	    114	     72	arm_vlog_q31.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     76	      0	      0	     76	     4c	arm_bartlett_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    200	      0	      0	    200	     c8	arm_bartlett_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    164	      0	      0	    164	     a4	arm_blackman_harris_92db_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    416	      0	      0	    416	    1a0	arm_blackman_harris_92db_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    108	      0	      0	    108	     6c	arm_hamming_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    232	      0	      0	    232	     e8	arm_hamming_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    100	      0	      0	    100	     64	arm_hanning_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    216	      0	      0	    216	     d8	arm_hanning_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    216	      0	      0	    216	     d8	arm_hft116d_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    520	      0	      0	    520	    208	arm_hft116d_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    244	      0	      0	    244	     f4	arm_hft144d_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    584	      0	      0	    584	    248	arm_hft144d_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    272	      0	      0	    272	    110	arm_hft169d_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    656	      0	      0	    656	    290	arm_hft169d_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    300	      0	      0	    300	    12c	arm_hft196d_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    720	      0	      0	    720	    2d0	arm_hft196d_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    332	      0	      0	    332	    14c	arm_hft223d_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    792	      0	      0	    792	    318	arm_hft223d_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    360	      0	      0	    360	    168	arm_hft248d_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    856	      0	      0	    856	    358	arm_hft248d_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    188	      0	      0	    188	     bc	arm_hft90d_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    448	      0	      0	    448	    1c0	arm_hft90d_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    188	      0	      0	    188	     bc	arm_hft95_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    448	      0	      0	    448	    1c0	arm_hft95_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    132	      0	      0	    132	     84	arm_nuttall3a_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    312	      0	      0	    312	    138	arm_nuttall3a_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    136	      0	      0	    136	     88	arm_nuttall3b_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    312	      0	      0	    312	    138	arm_nuttall3b_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    124	      0	      0	    124	     7c	arm_nuttall3_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    312	      0	      0	    312	    138	arm_nuttall3_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    164	      0	      0	    164	     a4	arm_nuttall4a_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    416	      0	      0	    416	    1a0	arm_nuttall4a_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    164	      0	      0	    164	     a4	arm_nuttall4b_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    416	      0	      0	    416	    1a0	arm_nuttall4b_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    164	      0	      0	    164	     a4	arm_nuttall4c_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    416	      0	      0	    416	    1a0	arm_nuttall4c_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    152	      0	      0	    152	     98	arm_nuttall4_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    416	      0	      0	    416	    1a0	arm_nuttall4_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
     72	      0	      0	     72	     48	arm_welch_f32.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    184	      0	      0	    184	     b8	arm_welch_f64.o (ex ../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a)
    192	      0	      0	    192	     c0	am_hal_access.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
   2736	      0	    105	   2841	    b19	am_hal_adc.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
   4312	      0	    104	   4416	   1140	am_hal_audadc.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
     40	      0	      0	     40	     28	am_hal_bootrom_helper.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    852	      5	      0	    857	    359	am_hal_cachectrl.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
  16384	      0	      0	  16384	   4000	am_hal_card.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    260	    160	      0	    420	    1a4	am_hal_card_host.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
   1400	      0	      5	   1405	    57d	am_hal_clkgen.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
   2048	      0	    484	   2532	    9e4	am_hal_cmdq.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    588	     32	      4	    624	    270	am_hal_dcu.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
   1056	      0	      4	   1060	    424	am_hal_dsi.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    112	      0	      0	    112	     70	am_hal_fault.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
     52	      0	      0	     52	     34	am_hal_global.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
   1904	      0	   2048	   3952	    f70	am_hal_gpio.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
   2532	      0	    152	   2684	    a7c	am_hal_i2s.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
     24	      0	      0	     24	     18	am_hal_interrupt.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
  10004	      0	  17728	  27732	   6c54	am_hal_iom.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
   2196	      0	     72	   2268	    8dc	am_hal_ios.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    500	      0	      0	    500	    1f4	am_hal_itm.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
   1236	      8	     12	   1256	    4e8	am_hal_mcuctrl.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    256	      0	      0	    256	    100	am_hal_mpu.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    376	      0	      0	    376	    178	am_hal_mram.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
   8372	      0	   6732	  15104	   3b00	am_hal_mspi.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    156	      0	      0	    156	     9c	am_hal_otp.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
   1672	      0	     96	   1768	    6e8	am_hal_pdm.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
   4096	      0	      0	   4096	   1000	am_hal_pin.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
   6273	      9	     47	   6329	   18b9	am_hal_pwrctrl.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    276	      0	      0	    276	    114	am_hal_queue.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    391	      0	      4	    395	    18b	am_hal_reset.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
   1252	      0	      0	   1252	    4e4	am_hal_rtc.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
   5680	      0	    212	   5892	   1704	am_hal_sdhc.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    288	      0	     12	    300	    12c	am_hal_secure_ota.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    404	      0	      0	    404	    194	am_hal_security.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    992	     32	      1	   1025	    401	am_hal_stimer.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    408	      0	      0	    408	    198	am_hal_sysctrl.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    144	      0	      0	    144	     90	am_hal_systick.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
   2716	      0	      0	   2716	    a9c	am_hal_timer.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    144	      0	      0	    144	     90	am_hal_tpiu.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
   3756	      0	    736	   4492	   118c	am_hal_uart.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
   6288	      4	    268	   6560	   19a0	am_hal_usb.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    284	      0	      0	    284	    11c	am_hal_utils.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    796	      0	      0	    796	    31c	am_hal_wdt.o (ex ../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a)
    672	      0	      0	    672	    2a0	NemaSDK_patch.o (ex ../../../../../../third_party/LVGL/ambiq_support/gpu_lib_apollo4/gcc/bin/lvgl_ambiq_porting.a)
   5352	      0	      0	   5352	   14e8	_lv_gpu_ambiq_nema.o (ex ../../../../../../third_party/LVGL/ambiq_support/gpu_lib_apollo4/gcc/bin/lvgl_ambiq_porting.a)
   2616	      4	      2	   2622	    a3e	nema_blender.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   3024	      0	      4	   3028	    bd4	nema_cmdlist.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   7176	      0	     40	   7216	   1c30	nema_dc.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
    140	      0	      0	    140	     8c	nema_dc_dsi.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   1752	      0	     76	   1828	    724	nema_dc_jdi.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   4784	      0	     12	   4796	   12bc	nema_dc_mipi.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   2536	      0	      0	   2536	    9e8	nema_easing.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   4188	      0	      4	   4192	   1060	nema_font.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   7342	      0	      0	   7342	   1cae	nema_graphics.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   1788	      0	      0	   1788	    6fc	nema_interpolators.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   2008	      0	      0	   2008	    7d8	nema_math.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   2912	      0	      0	   2912	    b60	nema_matrix3x3.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   3396	      0	      0	   3396	    d44	nema_matrix4x4.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
    496	      0	      0	    496	    1f0	nema_ocd.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   7024	    216	      4	   7244	   1c4c	nema_programHW.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   5660	      0	      0	   5660	   161c	nema_provisional.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
      0	      0	      0	      0	      0	nema_rasterizer.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
    880	      0	     20	    900	    384	nema_ringbuffer.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   4792	      0	      0	   4792	   12b8	nema_shaderSpecific.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   3684	      0	      0	   3684	    e64	nema_transitions.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
  32780	      0	      0	  32780	   800c	nema_vg.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   7424	      0	      0	   7424	   1d00	nema_vg_aabb.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
  11784	      0	     92	  11876	   2e64	nema_vg_clipped_path.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   1336	      0	    668	   2004	    7d4	nema_vg_context.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   4076	      4	      4	   4084	    ff4	nema_vg_font.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   3376	      0	      0	   3376	    d30	nema_vg_paint.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
    768	      0	      0	    768	    300	nema_vg_path.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
  12321	      0	      0	  12321	   3021	nema_vg_shapes.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   4300	      0	     20	   4320	   10e0	nema_vg_tsvg.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   1324	      0	    192	   1516	    5ec	tsi_malloc.o (ex ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a)
   7272	    114	   2053	   9439	   24df	am_bsp.o (ex ../../../../bsp/gcc/bin/libam_bsp.a)
      0	    740	     60	    800	    320	am_bsp_pins.o (ex ../../../../bsp/gcc/bin/libam_bsp.a)
 431608	  10972	 114556	 557136	  88050	bin/lvgl_test.axf
