bin/lv_bidi.o: ../../../../../../third_party/LVGL/lvgl/src/misc/lv_bidi.c \
 ../../../../../../third_party/LVGL/lvgl/src/misc/lv_bidi.h \
 ../../../../../../third_party/LVGL/lvgl/src/misc/../lv_conf_internal.h \
 ../../../../../../third_party/LVGL/lvgl/src/misc/../lv_conf_kconfig.h \
 ../src/lv_conf.h \
 ../../../../../../third_party/LVGL/lvgl/src/misc/lv_txt.h \
 ../../../../../../third_party/LVGL/lvgl/src/misc/lv_area.h \
 ../../../../../../third_party/LVGL/lvgl/src/misc/../font/lv_font.h \
 ../../../../../../third_party/LVGL/lvgl/src/misc/../font/../lv_conf_internal.h \
 ../../../../../../third_party/LVGL/lvgl/src/misc/../font/lv_symbol_def.h \
 ../../../../../../third_party/LVGL/lvgl/src/misc/../font/../misc/lv_area.h \
 ../../../../../../third_party/LVGL/lvgl/src/misc/lv_printf.h \
 ../../../../../../third_party/LVGL/lvgl/src/misc/lv_types.h \
 ../../../../../../third_party/LVGL/lvgl/src/misc/../misc/lv_mem.h \
 ../../../../../../third_party/LVGL/lvgl/src/misc/../misc/../lv_conf_internal.h \
 ../../../../../../third_party/LVGL/lvgl/src/misc/../misc/lv_types.h
../../../../../../third_party/LVGL/lvgl/src/misc/lv_bidi.h:
../../../../../../third_party/LVGL/lvgl/src/misc/../lv_conf_internal.h:
../../../../../../third_party/LVGL/lvgl/src/misc/../lv_conf_kconfig.h:
../src/lv_conf.h:
../../../../../../third_party/LVGL/lvgl/src/misc/lv_txt.h:
../../../../../../third_party/LVGL/lvgl/src/misc/lv_area.h:
../../../../../../third_party/LVGL/lvgl/src/misc/../font/lv_font.h:
../../../../../../third_party/LVGL/lvgl/src/misc/../font/../lv_conf_internal.h:
../../../../../../third_party/LVGL/lvgl/src/misc/../font/lv_symbol_def.h:
../../../../../../third_party/LVGL/lvgl/src/misc/../font/../misc/lv_area.h:
../../../../../../third_party/LVGL/lvgl/src/misc/lv_printf.h:
../../../../../../third_party/LVGL/lvgl/src/misc/lv_types.h:
../../../../../../third_party/LVGL/lvgl/src/misc/../misc/lv_mem.h:
../../../../../../third_party/LVGL/lvgl/src/misc/../misc/../lv_conf_internal.h:
../../../../../../third_party/LVGL/lvgl/src/misc/../misc/lv_types.h:
