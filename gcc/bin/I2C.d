bin/I2C.o: ../src/I2C.c ../src/I2C.h \
 ../../../../../../mcu/apollo4p/am_mcu_apollo.h \
 ../../../../../../CMSIS/AmbiqMicro/Include/apollo4p.h \
 ../../../../../../CMSIS/ARM/Include/core_cm4.h \
 ../../../../../../CMSIS/ARM/Include/cmsis_version.h \
 ../../../../../../CMSIS/ARM/Include/cmsis_compiler.h \
 ../../../../../../CMSIS/ARM/Include/cmsis_gcc.h \
 ../../../../../../CMSIS/ARM/Include/mpu_armv7.h \
 ../../../../../../CMSIS/AmbiqMicro/Include/system_apollo4p.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_usbregs.h \
 ../../../../../../mcu/apollo4p/regs/am_reg_base_addresses.h \
 ../../../../../../mcu/apollo4p/regs/am_reg_macros.h \
 ../../../../../../mcu/apollo4p/regs/am_reg.h \
 ../../../../../../mcu/apollo4p/regs/am_reg_mcu.h \
 ../../../../../../mcu/apollo4p/regs/am_reg_itm.h \
 ../../../../../../mcu/apollo4p/regs/am_reg_jedec.h \
 ../../../../../../mcu/apollo4p/regs/am_reg_m4.h \
 ../../../../../../mcu/apollo4p/regs/am_reg_sysctrl.h \
 ../../../../../../mcu/apollo4p/regs/am_reg_tpiu.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_global.h \
 ../../../../../../mcu/apollo4p/hal/../../am_sdk_version.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_status.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_mram.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_cmdq.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_cachectrl.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_dsi.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_fault.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_itm.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_sysctrl.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_iom.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_ios.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_mcu.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_mcu_sysctrl.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_mcu_interrupt.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_mcuctrl.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_mspi.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_reset.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_mcu_sysctrl.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_systick.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_tpiu.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_uart.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_clkgen.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_mcuctrl.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_rtc.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_secure_ota.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_card_host.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_card.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_sdhc.h \
 ../../../../../../mcu/apollo4p/hal/mcu/am_hal_mpu.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_access.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_adc.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_i2s.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_pin.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_gpio.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_audadc.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_pdm.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_pwrctrl.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_queue.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_stimer.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_system.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_timer.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_utils.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_security.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_wdt.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_usbcharger.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_usb.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_otp.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_dcu.h \
 ../../../../../../mcu/apollo4p/regs/am_mcu_apollo4p_info0.h \
 ../../../../../../mcu/apollo4p/regs/am_mcu_apollo4p_info1.h \
 ../../../../../../mcu/apollo4p/regs/am_mcu_apollo4p_otp.h \
 ../../../../bsp/am_bsp.h ../../../../bsp/am_bsp_pins.h \
 ../../../../../../devices/am_devices_led.h \
 ../../../../../../devices/am_devices_button.h \
 ../../../../../../devices/am_devices_display_generic.h \
 ../../../../../../devices/am_devices_display_types.h \
 ../../../../../../third_party/ThinkSi/NemaGFX_SDK/include/tsi/NemaDC/nema_dc.h \
 ../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/nema_sys_defs.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_status.h \
 ../../../../../../third_party/ThinkSi/NemaGFX_SDK/include/tsi/NemaDC/nema_dc_hal.h \
 ../../../../../../devices/am_devices_dc_xspi_raydium.h \
 ../../../../../../third_party/ThinkSi/NemaGFX_SDK/include/tsi/NemaDC/nema_dc_hal.h \
 ../../../../../../third_party/ThinkSi/NemaGFX_SDK/NemaDC/nema_dc_mipi.h \
 ../../../../../../third_party/ThinkSi/NemaGFX_SDK/NemaDC/nema_dc_regs.h \
 ../../../../../../third_party/ThinkSi/NemaGFX_SDK/NemaDC/nema_dc_intern.h \
 ../../../../../../devices/am_devices_dc_dsi_raydium.h \
 ../../../../../../third_party/ThinkSi/NemaGFX_SDK/NemaDC/nema_dc_dsi.h \
 ../../../../../../utils/am_util.h \
 ../../../../../../utils/am_util_debug.h \
 ../../../../../../utils/am_util_delay.h \
 ../../../../../../utils/am_util_id.h \
 ../../../../../../utils/am_util_stdio.h \
 ../../../../../../utils/am_util_string.h \
 ../../../../../../utils/am_util_time.h \
 ../../../../../../utils/am_util_ble_cooper.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_global.h \
 ../../../../../../mcu/apollo4p/hal/am_hal_gpio.h
../src/I2C.h:
../../../../../../mcu/apollo4p/am_mcu_apollo.h:
../../../../../../CMSIS/AmbiqMicro/Include/apollo4p.h:
../../../../../../CMSIS/ARM/Include/core_cm4.h:
../../../../../../CMSIS/ARM/Include/cmsis_version.h:
../../../../../../CMSIS/ARM/Include/cmsis_compiler.h:
../../../../../../CMSIS/ARM/Include/cmsis_gcc.h:
../../../../../../CMSIS/ARM/Include/mpu_armv7.h:
../../../../../../CMSIS/AmbiqMicro/Include/system_apollo4p.h:
../../../../../../mcu/apollo4p/hal/am_hal_usbregs.h:
../../../../../../mcu/apollo4p/regs/am_reg_base_addresses.h:
../../../../../../mcu/apollo4p/regs/am_reg_macros.h:
../../../../../../mcu/apollo4p/regs/am_reg.h:
../../../../../../mcu/apollo4p/regs/am_reg_mcu.h:
../../../../../../mcu/apollo4p/regs/am_reg_itm.h:
../../../../../../mcu/apollo4p/regs/am_reg_jedec.h:
../../../../../../mcu/apollo4p/regs/am_reg_m4.h:
../../../../../../mcu/apollo4p/regs/am_reg_sysctrl.h:
../../../../../../mcu/apollo4p/regs/am_reg_tpiu.h:
../../../../../../mcu/apollo4p/hal/am_hal_global.h:
../../../../../../mcu/apollo4p/hal/../../am_sdk_version.h:
../../../../../../mcu/apollo4p/hal/am_hal_status.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_mram.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_cmdq.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_cachectrl.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_dsi.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_fault.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_itm.h:
../../../../../../mcu/apollo4p/hal/am_hal_sysctrl.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_iom.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_ios.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_mcu.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_mcu_sysctrl.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_mcu_interrupt.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_mcuctrl.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_mspi.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_reset.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_mcu_sysctrl.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_systick.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_tpiu.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_uart.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_clkgen.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_mcuctrl.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_rtc.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_secure_ota.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_card_host.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_card.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_sdhc.h:
../../../../../../mcu/apollo4p/hal/mcu/am_hal_mpu.h:
../../../../../../mcu/apollo4p/hal/am_hal_access.h:
../../../../../../mcu/apollo4p/hal/am_hal_adc.h:
../../../../../../mcu/apollo4p/hal/am_hal_i2s.h:
../../../../../../mcu/apollo4p/hal/am_hal_pin.h:
../../../../../../mcu/apollo4p/hal/am_hal_gpio.h:
../../../../../../mcu/apollo4p/hal/am_hal_audadc.h:
../../../../../../mcu/apollo4p/hal/am_hal_pdm.h:
../../../../../../mcu/apollo4p/hal/am_hal_pwrctrl.h:
../../../../../../mcu/apollo4p/hal/am_hal_queue.h:
../../../../../../mcu/apollo4p/hal/am_hal_stimer.h:
../../../../../../mcu/apollo4p/hal/am_hal_system.h:
../../../../../../mcu/apollo4p/hal/am_hal_timer.h:
../../../../../../mcu/apollo4p/hal/am_hal_utils.h:
../../../../../../mcu/apollo4p/hal/am_hal_security.h:
../../../../../../mcu/apollo4p/hal/am_hal_wdt.h:
../../../../../../mcu/apollo4p/hal/am_hal_usbcharger.h:
../../../../../../mcu/apollo4p/hal/am_hal_usb.h:
../../../../../../mcu/apollo4p/hal/am_hal_otp.h:
../../../../../../mcu/apollo4p/hal/am_hal_dcu.h:
../../../../../../mcu/apollo4p/regs/am_mcu_apollo4p_info0.h:
../../../../../../mcu/apollo4p/regs/am_mcu_apollo4p_info1.h:
../../../../../../mcu/apollo4p/regs/am_mcu_apollo4p_otp.h:
../../../../bsp/am_bsp.h:
../../../../bsp/am_bsp_pins.h:
../../../../../../devices/am_devices_led.h:
../../../../../../devices/am_devices_button.h:
../../../../../../devices/am_devices_display_generic.h:
../../../../../../devices/am_devices_display_types.h:
../../../../../../third_party/ThinkSi/NemaGFX_SDK/include/tsi/NemaDC/nema_dc.h:
../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/nema_sys_defs.h:
../../../../../../mcu/apollo4p/hal/am_hal_status.h:
../../../../../../third_party/ThinkSi/NemaGFX_SDK/include/tsi/NemaDC/nema_dc_hal.h:
../../../../../../devices/am_devices_dc_xspi_raydium.h:
../../../../../../third_party/ThinkSi/NemaGFX_SDK/include/tsi/NemaDC/nema_dc_hal.h:
../../../../../../third_party/ThinkSi/NemaGFX_SDK/NemaDC/nema_dc_mipi.h:
../../../../../../third_party/ThinkSi/NemaGFX_SDK/NemaDC/nema_dc_regs.h:
../../../../../../third_party/ThinkSi/NemaGFX_SDK/NemaDC/nema_dc_intern.h:
../../../../../../devices/am_devices_dc_dsi_raydium.h:
../../../../../../third_party/ThinkSi/NemaGFX_SDK/NemaDC/nema_dc_dsi.h:
../../../../../../utils/am_util.h:
../../../../../../utils/am_util_debug.h:
../../../../../../utils/am_util_delay.h:
../../../../../../utils/am_util_id.h:
../../../../../../utils/am_util_stdio.h:
../../../../../../utils/am_util_string.h:
../../../../../../utils/am_util_time.h:
../../../../../../utils/am_util_ble_cooper.h:
../../../../../../mcu/apollo4p/hal/am_hal_global.h:
../../../../../../mcu/apollo4p/hal/am_hal_gpio.h:
