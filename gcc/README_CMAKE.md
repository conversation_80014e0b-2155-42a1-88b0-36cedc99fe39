# CMake Build System for LVGL Test Happystone Example

This directory contains the CMake build system for the LVGL Test Happystone example, converted from the original Makefile-based build system.

## Files

- `CMakeLists.txt` - Main CMake configuration file
- `arm-gcc-toolchain.cmake` - ARM GCC cross-compilation toolchain file
- `build.sh` - Convenient build script
- `README_CMAKE.md` - This documentation file

## Prerequisites

- CMake 3.16 or later
- ARM GCC toolchain (`arm-none-eabi-gcc`)
- Make or Ninja build system

## Quick Start

### Using the Build Script (Recommended)

```bash
# Build in Debug mode (default)
./build.sh

# Build in Release mode
./build.sh --release

# Clean build
./build.sh --clean

# Verbose output
./build.sh --verbose

# Show help
./build.sh --help
```

### Using CMake Directly

```bash
# Create build directory
mkdir build
cd build

# Configure
cmake -DCMAKE_TOOLCHAIN_FILE=../arm-gcc-toolchain.cmake -DCMAKE_BUILD_TYPE=Debug ..

# Build
make -j$(nproc)

# Or using Ninja (faster)
cmake -DCMAKE_TOOLCHAIN_FILE=../arm-gcc-toolchain.cmake -DCMAKE_BUILD_TYPE=Debug -G Ninja ..
ninja
```

## Build Output

The build system generates the following files in the `build/bin/` directory:

- `lvgl_test.axf` - ELF executable file
- `lvgl_test.bin` - Binary file for flashing
- `lvgl_test.lst` - Assembly listing
- `lvgl_test.map` - Memory map
- `lvgl_test.size` - Size information

## Configuration

### Build Types

- **Debug** (default): Optimized for debugging with `-g -O0`
- **Release**: Optimized for performance with `-O2`

### Compiler Flags

The CMake configuration includes all the original Makefile compiler flags:

- Target: ARM Cortex-M4 with FPU
- Standard: C99
- Optimization: Configurable per build type
- Warnings: All enabled
- Debug info: Included in Debug builds

### Preprocessor Definitions

All original preprocessor definitions are preserved:

- `PART_apollo4p`
- `AM_FREERTOS`
- `AM_PART_APOLLO4P`
- `LV_AMBIQ_FB_RESX=390`
- `LV_AMBIQ_FB_RESY=390`
- And many more...

### Include Directories

All include paths from the original Makefile are maintained:

- AmbiqSuite core libraries
- CMSIS headers
- FreeRTOS headers
- LVGL headers
- ThinkSi NemaGFX headers
- Project-specific headers

## Dependencies

The build system automatically handles the following dependencies:

1. **Ambiq HAL Library** (`libam_hal.a`)
2. **Ambiq BSP Library** (`libam_bsp.a`)
3. **LVGL Ambiq Porting Library** (`lvgl_ambiq_porting.a`)
4. **ThinkSi NemaGFX Library** (`lib_nema_apollo4p_nemagfx.a`)
5. **ARM Math Library** (`libarm_cortexM4lf_math.a`)

These libraries are built automatically as part of the build process.

## Source Organization

The CMake configuration organizes sources into logical groups:

- **Device Sources**: Ambiq device drivers and utilities
- **FreeRTOS Sources**: Real-time operating system components
- **LVGL Core Sources**: LVGL core functionality
- **LVGL Widget Sources**: UI widgets and components
- **LVGL Ambiq Support**: Ambiq-specific LVGL adaptations
- **ThinkSi Sources**: Graphics acceleration library
- **Project Sources**: Application-specific code

## Memory Layout

The original linker script (`linker_script.ld`) is preserved and used as-is:

- **MCU_MRAM**: 0x00018000, 1998848 bytes (code and read-only data)
- **MCU_TCM**: 0x10000000, 393216 bytes (data, stack, heap)
- **SHARED_SRAM**: 0x10060000, 1048576 bytes (shared memory)

## Differences from Makefile

### Advantages of CMake Version

1. **Better IDE Support**: Works with modern IDEs like VS Code, CLion, etc.
2. **Cross-Platform**: Can be used on Windows, Linux, macOS
3. **Dependency Management**: Better handling of build dependencies
4. **Configuration Options**: Easy to add new build configurations
5. **Debugging**: Better integration with debugging tools
6. **Documentation**: Self-documenting build configuration

### Preserved Features

1. **All Compiler Flags**: Identical compilation settings
2. **All Include Paths**: Same header search paths
3. **All Preprocessor Definitions**: Same macro definitions
4. **Linker Script**: Same memory layout and linking
5. **Static Libraries**: Same library dependencies
6. **Build Output**: Same generated files

## Troubleshooting

### Common Issues

1. **Toolchain Not Found**: Ensure `arm-none-eabi-gcc` is in your PATH
2. **CMake Version**: Requires CMake 3.16 or later
3. **Missing Dependencies**: Ensure all AmbiqSuite libraries are present
4. **Permission Issues**: Make sure build script is executable (`chmod +x build.sh`)

### Debug Build

To debug build issues, use verbose output:

```bash
./build.sh --verbose
```

This will show all compiler commands and flags used.

### Clean Build

If you encounter build issues, try a clean build:

```bash
./build.sh --clean
```

## Migration from Makefile

If you're migrating from the Makefile build system:

1. Use `./build.sh` instead of `make`
2. Build output is in `build/bin/` instead of `bin/`
3. All other aspects remain the same

## Contributing

When modifying the build system:

1. Update `CMakeLists.txt` for source changes
2. Update `arm-gcc-toolchain.cmake` for toolchain changes
3. Update this README for documentation changes
4. Test with both Debug and Release builds
