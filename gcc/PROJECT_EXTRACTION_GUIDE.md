# Project Extraction Guide: Creating an Independent Repository

This guide will walk you through extracting your LVGL Test Happystone project from the AmbiqSuite SDK to create a standalone, independent repository.

## Overview

Your current project is embedded within the AmbiqSuite SDK structure and depends on various SDK components. This guide will help you:

1. Identify all dependencies
2. Extract necessary files
3. Set up a standalone project structure
4. Configure external dependencies
5. Create a portable build system

## Prerequisites

- Git installed
- CMake 3.16+ installed
- ARM GCC toolchain (`arm-none-eabi-gcc`)
- Access to the original AmbiqSuite SDK (for reference)

## Step 1: Analyze Current Dependencies

### 1.1 Identify External Dependencies

Your project currently depends on these external components:

#### **AmbiqSuite Core Components:**
- **CMSIS**: ARM Cortex-M4 core support
- **Ambiq HAL**: Hardware abstraction layer
- **Ambiq BSP**: Board support package
- **Ambiq Utils**: Utility functions

#### **Third-Party Libraries:**
- **FreeRTOS v10.5.1**: Real-time operating system
- **LVGL**: Graphics library with Ambiq support
- **ThinkSi NemaGFX SDK**: Graphics acceleration
- **ARM Math Library**: Floating-point math functions

#### **Project-Specific Files:**
- **Source files**: All files in `src/` directory
- **Build files**: CMakeLists.txt, toolchain file, build script
- **Configuration files**: Linker script, startup code
- **Assets**: Texture files in `src/texture/`

### 1.2 Map File Dependencies

Create a dependency map by analyzing your CMakeLists.txt:

```
Your Project
├── AmbiqSuite SDK (external)
│   ├── CMSIS/
│   ├── mcu/apollo4p/
│   ├── devices/
│   ├── utils/
│   └── boards/apollo4p_evb_disp_shield_rev2/bsp/
├── Third-Party Libraries (external)
│   ├── FreeRTOSv10.5.1/
│   ├── LVGL/
│   └── ThinkSi/
└── Project Files (your code)
    ├── src/
    ├── gcc/
    └── assets/
```

## Step 2: Create New Repository Structure

### 2.1 Initialize New Repository

```bash
# Create new directory for your independent project
mkdir lvgl-happystone-project
cd lvgl-happystone-project

# Initialize Git repository
git init
git remote add origin <your-repo-url>
```

### 2.2 Design Project Structure

Create this directory structure:

```
lvgl-happystone-project/
├── README.md
├── LICENSE
├── CMakeLists.txt                    # Root CMake file
├── cmake/                           # CMake modules
│   ├── arm-gcc-toolchain.cmake
│   └── FindAmbiqSuite.cmake
├── external/                        # External dependencies
│   ├── ambiqsuite/                  # AmbiqSuite components
│   ├── freertos/                    # FreeRTOS
│   ├── lvgl/                        # LVGL library
│   └── thinksi/                     # ThinkSi NemaGFX
├── src/                            # Your source code
│   ├── main/
│   ├── drivers/
│   ├── gui/
│   ├── rtos/
│   └── assets/
├── include/                        # Public headers
├── scripts/                        # Build and utility scripts
│   └── build.sh
├── docs/                          # Documentation
├── tests/                         # Unit tests
└── tools/                         # Development tools
```

## Step 3: Extract and Organize Source Files

### 3.1 Copy Project-Specific Files

```bash
# Copy your source files
cp -r /path/to/ambiqsuite/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/* ./src/

# Copy build files
cp /path/to/ambiqsuite/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/CMakeLists.txt ./
cp /path/to/ambiqsuite/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/arm-gcc-toolchain.cmake ./cmake/
cp /path/to/ambiqsuite/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build.sh ./scripts/
cp /path/to/ambiqsuite/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/linker_script.ld ./src/
cp /path/to/ambiqsuite/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/startup_gcc.c ./src/
```

### 3.2 Organize Source Files

Reorganize your source files into logical modules:

```bash
# Create module directories
mkdir -p src/{main,drivers,gui,rtos,assets}

# Move files to appropriate modules
mv src/lvgl_test.c src/main/
mv src/gui_task.c src/gui/
mv src/rtos.c src/rtos/
mv src/board_init.c src/drivers/
mv src/am_hal_mspi.c src/drivers/
mv src/AMOLED.c src/drivers/
mv src/aps12808l.c src/drivers/
mv src/hynitron_*.c src/drivers/
mv src/I2C.c src/drivers/
mv src/sh8601_*.c src/drivers/
mv src/UART.c src/drivers/
mv src/minimal_*.c src/rtos/
mv src/texture/ src/assets/
```

## Step 4: Set Up External Dependencies

### 4.1 Create Dependency Management

Create `external/CMakeLists.txt`:

```cmake
# External dependencies management
include(ExternalProject)

# AmbiqSuite SDK
ExternalProject_Add(
    ambiqsuite
    SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/external/ambiqsuite
    CONFIGURE_COMMAND ""
    BUILD_COMMAND ""
    INSTALL_COMMAND ""
)

# FreeRTOS
ExternalProject_Add(
    freertos
    SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/external/freertos
    CONFIGURE_COMMAND ""
    BUILD_COMMAND ""
    INSTALL_COMMAND ""
)

# LVGL
ExternalProject_Add(
    lvgl
    SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/external/lvgl
    CONFIGURE_COMMAND ""
    BUILD_COMMAND ""
    INSTALL_COMMAND ""
)

# ThinkSi NemaGFX
ExternalProject_Add(
    thinksi
    SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/external/thinksi
    CONFIGURE_COMMAND ""
    BUILD_COMMAND ""
    INSTALL_COMMAND ""
)
```

### 4.2 Create Dependency Setup Script

Create `scripts/setup-dependencies.sh`:

```bash
#!/bin/bash

# Setup script for external dependencies
set -e

EXTERNAL_DIR="external"
AMBISUITE_SOURCE="/path/to/your/ambiqsuite"

echo "Setting up external dependencies..."

# Create external directory
mkdir -p $EXTERNAL_DIR

# Copy AmbiqSuite components
echo "Copying AmbiqSuite components..."
cp -r $AMBISUITE_SOURCE/CMSIS $EXTERNAL_DIR/ambiqsuite/
cp -r $AMBISUITE_SOURCE/mcu $EXTERNAL_DIR/ambiqsuite/
cp -r $AMBISUITE_SOURCE/devices $EXTERNAL_DIR/ambiqsuite/
cp -r $AMBISUITE_SOURCE/utils $EXTERNAL_DIR/ambiqsuite/
cp -r $AMBISUITE_SOURCE/boards/apollo4p_evb_disp_shield_rev2/bsp $EXTERNAL_DIR/ambiqsuite/

# Copy FreeRTOS
echo "Copying FreeRTOS..."
cp -r $AMBISUITE_SOURCE/third_party/FreeRTOSv10.5.1 $EXTERNAL_DIR/freertos/

# Copy LVGL
echo "Copying LVGL..."
cp -r $AMBISUITE_SOURCE/third_party/LVGL $EXTERNAL_DIR/lvgl/

# Copy ThinkSi
echo "Copying ThinkSi NemaGFX..."
cp -r $AMBISUITE_SOURCE/third_party/ThinkSi $EXTERNAL_DIR/thinksi/

echo "Dependencies setup complete!"
```

## Step 5: Update CMake Configuration

### 5.1 Create Root CMakeLists.txt

Create a new root `CMakeLists.txt`:

```cmake
cmake_minimum_required(VERSION 3.16)

project(lvgl_happystone VERSION 1.0.0 LANGUAGES C ASM)

# Set C standard
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Set output directory
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Include external dependencies
add_subdirectory(external)

# Include project modules
add_subdirectory(src)

# Set target
set(TARGET_NAME lvgl_happystone)

# Create executable
add_executable(${TARGET_NAME} "")

# Link all modules
target_link_libraries(${TARGET_NAME}
    main_module
    drivers_module
    gui_module
    rtos_module
    ambiqsuite_libs
    freertos_libs
    lvgl_libs
    thinksi_libs
)
```

### 5.2 Create Module CMakeLists.txt Files

Create `src/CMakeLists.txt`:

```cmake
# Source modules
add_subdirectory(main)
add_subdirectory(drivers)
add_subdirectory(gui)
add_subdirectory(rtos)
```

Create `src/main/CMakeLists.txt`:

```cmake
# Main application module
set(MAIN_SOURCES
    lvgl_test.c
    startup_gcc.c
)

add_library(main_module STATIC ${MAIN_SOURCES})

target_include_directories(main_module PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
)

target_compile_definitions(main_module PRIVATE
    PART_apollo4p
    AM_FREERTOS
    AM_PART_APOLLO4P
    # ... other definitions
)
```

## Step 6: Create Project Documentation

### 6.1 Create Main README.md

```markdown
# LVGL Happystone Project

A standalone LVGL-based graphics application for Apollo4P EVB Display Shield Rev2.

## Features

- LVGL graphics library integration
- FreeRTOS real-time operating system
- Touch screen support
- Graphics acceleration via ThinkSi NemaGFX
- Minimal memory footprint

## Prerequisites

- CMake 3.16+
- ARM GCC toolchain (`arm-none-eabi-gcc`)
- AmbiqSuite SDK (for dependency setup)

## Quick Start

1. Clone the repository
2. Setup dependencies: `./scripts/setup-dependencies.sh`
3. Build: `./scripts/build.sh`

## Project Structure

- `src/` - Source code organized by modules
- `external/` - External dependencies
- `cmake/` - CMake configuration files
- `scripts/` - Build and utility scripts
- `docs/` - Documentation

## License

[Your chosen license]
```

### 6.2 Create Build Instructions

Create `docs/BUILD_INSTRUCTIONS.md`:

```markdown
# Build Instructions

## Prerequisites

1. Install CMake 3.16 or later
2. Install ARM GCC toolchain
3. Obtain AmbiqSuite SDK

## Setup

1. Run dependency setup: `./scripts/setup-dependencies.sh`
2. Configure build: `cmake -B build -DCMAKE_TOOLCHAIN_FILE=cmake/arm-gcc-toolchain.cmake`
3. Build: `cmake --build build`

## Build Options

- Debug: `cmake -B build -DCMAKE_BUILD_TYPE=Debug`
- Release: `cmake -B build -DCMAKE_BUILD_TYPE=Release`
- Verbose: `cmake --build build --verbose`
```

## Step 7: Create Development Tools

### 7.1 Create Development Scripts

Create `scripts/dev-setup.sh`:

```bash
#!/bin/bash
# Development environment setup

echo "Setting up development environment..."

# Install development tools
sudo apt update
sudo apt install -y cmake gcc-arm-none-eabi git

# Setup git hooks
cp tools/git-hooks/* .git/hooks/
chmod +x .git/hooks/*

echo "Development environment ready!"
```

### 7.2 Create CI/CD Configuration

Create `.github/workflows/build.yml`:

```yaml
name: Build and Test

on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Install dependencies
      run: |
        sudo apt update
        sudo apt install -y cmake gcc-arm-none-eabi
    
    - name: Setup dependencies
      run: ./scripts/setup-dependencies.sh
    
    - name: Configure
      run: cmake -B build -DCMAKE_TOOLCHAIN_FILE=cmake/arm-gcc-toolchain.cmake
    
    - name: Build
      run: cmake --build build
```

## Step 8: Create License and Legal Files

### 8.1 Create LICENSE File

Choose an appropriate license (MIT, Apache 2.0, etc.) and create `LICENSE`:

```text
MIT License

Copyright (c) 2024 [Your Name]

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

[License text continues...]
```

### 8.2 Create Third-Party Licenses

Create `docs/THIRD_PARTY_LICENSES.md`:

```markdown
# Third-Party Licenses

This project uses the following third-party libraries:

## AmbiqSuite SDK
- License: [Check AmbiqSuite license]
- Source: Ambiq Micro, Inc.

## FreeRTOS
- License: MIT
- Source: https://www.freertos.org/

## LVGL
- License: MIT
- Source: https://lvgl.io/

## ThinkSi NemaGFX
- License: [Check ThinkSi license]
- Source: ThinkSi Technologies
```

## Step 9: Migration Checklist

### 9.1 Pre-Migration Checklist

- [ ] Identify all source files to extract
- [ ] Map all external dependencies
- [ ] Plan new project structure
- [ ] Choose appropriate license
- [ ] Set up new repository

### 9.2 Migration Steps Checklist

- [ ] Create new repository structure
- [ ] Copy and organize source files
- [ ] Set up external dependencies
- [ ] Update CMake configuration
- [ ] Create documentation
- [ ] Set up build scripts
- [ ] Configure CI/CD
- [ ] Add license and legal files

### 9.3 Post-Migration Checklist

- [ ] Test build system
- [ ] Verify all dependencies work
- [ ] Update documentation
- [ ] Test on target hardware
- [ ] Create release tags
- [ ] Set up issue tracking
- [ ] Configure project settings

## Step 10: Testing and Validation

### 10.1 Build Testing

```bash
# Test clean build
rm -rf build/
./scripts/build.sh --clean

# Test different configurations
./scripts/build.sh --debug
./scripts/build.sh --release

# Test verbose output
./scripts/build.sh --verbose
```

### 10.2 Hardware Testing

1. Flash the binary to target hardware
2. Verify display functionality
3. Test touch input
4. Validate graphics performance
5. Check memory usage

## Step 11: Maintenance and Updates

### 11.1 Dependency Updates

- Monitor AmbiqSuite SDK updates
- Update FreeRTOS when needed
- Keep LVGL up to date
- Update ThinkSi NemaGFX as available

### 11.2 Project Maintenance

- Regular dependency audits
- Security updates
- Performance optimizations
- Feature additions
- Bug fixes

## Troubleshooting

### Common Issues

1. **Missing Dependencies**: Run `./scripts/setup-dependencies.sh`
2. **Build Failures**: Check toolchain installation
3. **Link Errors**: Verify library paths
4. **Runtime Issues**: Check hardware connections

### Getting Help

- Check documentation in `docs/`
- Review build logs
- Test with minimal configuration
- Consult AmbiqSuite documentation

## Conclusion

This guide provides a comprehensive approach to extracting your project from the AmbiqSuite SDK. The key is to:

1. **Carefully map dependencies** before starting
2. **Organize code logically** into modules
3. **Set up proper dependency management**
4. **Create comprehensive documentation**
5. **Test thoroughly** at each step

Following this guide will result in a clean, maintainable, and portable project that can be developed independently while still leveraging the necessary AmbiqSuite components.
