# ARM GCC Toolchain file for Apollo4P
# This file configures CMake to use the ARM GCC cross-compiler

set(CMAKE_SYSTEM_NAME Generic)
set(CMAKE_SYSTEM_PROCESSOR ARM)

# Specify the cross compiler
set(CMAKE_C_COMPILER arm-none-eabi-gcc)
set(CMAKE_CXX_COMPILER arm-none-eabi-g++)
set(CMAKE_ASM_COMPILER arm-none-eabi-gcc)

# Specify the linker
set(CMAKE_LINKER arm-none-eabi-ld)

# Specify the archiver
set(CMAKE_AR arm-none-eabi-ar)
set(CMAKE_RANLIB arm-none-eabi-ranlib)
set(CMAKE_STRIP arm-none-eabi-strip)
set(CMAKE_OBJCOPY arm-none-eabi-objcopy)
set(CMAKE_OBJDUMP arm-none-eabi-objdump)
set(CMAKE_SIZE arm-none-eabi-size)

# Search for programs in the build host directories
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)

# For libraries and headers in the target directories
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)

# Disable compiler checks
set(CMAKE_C_COMPILER_FORCED TRUE)
set(CMAKE_CXX_COMPILER_FORCED TRUE)

# Set the target architecture
set(TARGET_CPU cortex-m4)
set(TARGET_FPU fpv4-sp-d16)
set(TARGET_FLOAT_ABI hard)

# Common flags for all targets
set(COMMON_FLAGS
    -mthumb
    -mcpu=${TARGET_CPU}
    -mfpu=${TARGET_FPU}
    -mfloat-abi=${TARGET_FLOAT_ABI}
    -ffunction-sections
    -fdata-sections
    -fomit-frame-pointer
    -MMD
    -MP
    -std=c99
    -Wall
    -g
    -O0
)

# Set default C flags
set(CMAKE_C_FLAGS "${COMMON_FLAGS}" CACHE STRING "C Compiler Flags" FORCE)
set(CMAKE_C_FLAGS_DEBUG "-g -O0" CACHE STRING "C Debug Flags" FORCE)
set(CMAKE_C_FLAGS_RELEASE "-O2" CACHE STRING "C Release Flags" FORCE)

# Set default CXX flags
set(CMAKE_CXX_FLAGS "${COMMON_FLAGS}" CACHE STRING "C++ Compiler Flags" FORCE)
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0" CACHE STRING "C++ Debug Flags" FORCE)
set(CMAKE_CXX_FLAGS_RELEASE "-O2" CACHE STRING "C++ Release Flags" FORCE)

# Set ASM flags
set(CMAKE_ASM_FLAGS "${COMMON_FLAGS}" CACHE STRING "ASM Compiler Flags" FORCE)

# Set linker flags
set(CMAKE_EXE_LINKER_FLAGS
    "-mthumb -mcpu=${TARGET_CPU} -mfpu=${TARGET_FPU} -mfloat-abi=${TARGET_FLOAT_ABI} -nostartfiles -static"
    CACHE STRING "Linker Flags" FORCE
)

# Don't use the default linker script
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,--gc-sections" CACHE STRING "Linker Flags" FORCE)
