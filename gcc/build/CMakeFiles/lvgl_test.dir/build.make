# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build

# Include any dependencies generated for this target.
include CMakeFiles/lvgl_test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/lvgl_test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/lvgl_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/lvgl_test.dir/flags.make

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_43) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_44) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_45) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_46) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_47) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_48) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_49) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_50) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_51) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_52) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_53) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_54) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_55) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_56) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_57) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_58) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_59) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_60) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_61) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_62) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_63) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_64) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_65) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_66) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_67) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_68) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_69) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_70) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_71) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_72) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_73) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_74) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_75) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_76) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_77) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_78) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_79) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_80) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_81) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_82) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_83) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_84) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_85) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_86) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_87) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_88) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_89) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_90) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_91) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_92) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_93) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_94) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_95) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_96) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_97) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_98) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_99) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_100) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_101) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_102) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_103) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_104) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_105) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_106) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_107) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_108) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_109) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_110) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_111) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_112) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_113) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_114) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_115) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_116) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_117) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_118) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_119) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_120) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_121) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_122) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_123) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_124) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_125) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_126) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_127) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_128) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_129) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_130) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_131) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_132) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_133) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_134) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_135) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_136) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_137) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_138) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_139) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_140) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_141) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_142) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_143) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_144) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_145) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_146) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_147) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_148) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_149) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_150) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_151) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_152) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_153) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_154) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_155) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_156) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_157) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_158) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_159) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_160) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_161) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_162) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_163) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_164) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_165) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_166) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_167) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_168) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_169) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_170) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_171) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_172) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_173) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.s

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c
CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_174) "Building C object CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.obj -MF CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.obj.d -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c > CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.i

CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c -o CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.s

CMakeFiles/lvgl_test.dir/startup_gcc.c.obj: CMakeFiles/lvgl_test.dir/flags.make
CMakeFiles/lvgl_test.dir/startup_gcc.c.obj: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/startup_gcc.c
CMakeFiles/lvgl_test.dir/startup_gcc.c.obj: CMakeFiles/lvgl_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_175) "Building C object CMakeFiles/lvgl_test.dir/startup_gcc.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/lvgl_test.dir/startup_gcc.c.obj -MF CMakeFiles/lvgl_test.dir/startup_gcc.c.obj.d -o CMakeFiles/lvgl_test.dir/startup_gcc.c.obj -c /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/startup_gcc.c

CMakeFiles/lvgl_test.dir/startup_gcc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/lvgl_test.dir/startup_gcc.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/startup_gcc.c > CMakeFiles/lvgl_test.dir/startup_gcc.c.i

CMakeFiles/lvgl_test.dir/startup_gcc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/lvgl_test.dir/startup_gcc.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/startup_gcc.c -o CMakeFiles/lvgl_test.dir/startup_gcc.c.s

# Object files for target lvgl_test
lvgl_test_OBJECTS = \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.obj" \
"CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.obj" \
"CMakeFiles/lvgl_test.dir/startup_gcc.c.obj"

# External object files for target lvgl_test
lvgl_test_EXTERNAL_OBJECTS =

bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/startup_gcc.c.obj
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/build.make
bin/lvgl_test.axf: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/../../../../../../CMSIS/ARM/Lib/ARM/libarm_cortexM4lf_math.a
bin/lvgl_test.axf: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/../../../../../../mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a
bin/lvgl_test.axf: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/../../../../../../third_party/LVGL/ambiq_support/gpu_lib_apollo4/gcc/bin/lvgl_ambiq_porting.a
bin/lvgl_test.axf: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/../../../../../../third_party/ThinkSi/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a
bin/lvgl_test.axf: /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/../../../../bsp/gcc/bin/libam_bsp.a
bin/lvgl_test.axf: CMakeFiles/lvgl_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_176) "Linking C executable bin/lvgl_test.axf"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/lvgl_test.dir/link.txt --verbose=$(VERBOSE)
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold "Creating binary and listing files"
	arm-none-eabi-objcopy -Obinary /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/bin/lvgl_test.axf /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/bin/lvgl_test.bin
	arm-none-eabi-objdump -S /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/bin/lvgl_test.axf > /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/bin/lvgl_test.lst
	arm-none-eabi-size /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/bin/lvgl_test.axf > /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/bin/lvgl_test.size

# Rule to build all files generated by this target.
CMakeFiles/lvgl_test.dir/build: bin/lvgl_test.axf
.PHONY : CMakeFiles/lvgl_test.dir/build

CMakeFiles/lvgl_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/lvgl_test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/lvgl_test.dir/clean

CMakeFiles/lvgl_test.dir/depend:
	cd /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles/lvgl_test.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/lvgl_test.dir/depend

