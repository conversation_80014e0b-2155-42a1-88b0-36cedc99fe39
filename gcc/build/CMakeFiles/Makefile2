# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/lvgl_test.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/lvgl_test.dir/clean
clean: CMakeFiles/build_am_hal.dir/clean
clean: CMakeFiles/build_am_bsp.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/lvgl_test.dir

# All Build rule for target.
CMakeFiles/lvgl_test.dir/all: CMakeFiles/build_am_hal.dir/all
CMakeFiles/lvgl_test.dir/all: CMakeFiles/build_am_bsp.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100 "Built target lvgl_test"
.PHONY : CMakeFiles/lvgl_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/lvgl_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles 100
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/lvgl_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles 0
.PHONY : CMakeFiles/lvgl_test.dir/rule

# Convenience name for target.
lvgl_test: CMakeFiles/lvgl_test.dir/rule
.PHONY : lvgl_test

# clean rule for target.
CMakeFiles/lvgl_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/clean
.PHONY : CMakeFiles/lvgl_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/build_am_hal.dir

# All Build rule for target.
CMakeFiles/build_am_hal.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/build_am_hal.dir/build.make CMakeFiles/build_am_hal.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/build_am_hal.dir/build.make CMakeFiles/build_am_hal.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=1 "Built target build_am_hal"
.PHONY : CMakeFiles/build_am_hal.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/build_am_hal.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/build_am_hal.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles 0
.PHONY : CMakeFiles/build_am_hal.dir/rule

# Convenience name for target.
build_am_hal: CMakeFiles/build_am_hal.dir/rule
.PHONY : build_am_hal

# clean rule for target.
CMakeFiles/build_am_hal.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/build_am_hal.dir/build.make CMakeFiles/build_am_hal.dir/clean
.PHONY : CMakeFiles/build_am_hal.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/build_am_bsp.dir

# All Build rule for target.
CMakeFiles/build_am_bsp.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/build_am_bsp.dir/build.make CMakeFiles/build_am_bsp.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/build_am_bsp.dir/build.make CMakeFiles/build_am_bsp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num= "Built target build_am_bsp"
.PHONY : CMakeFiles/build_am_bsp.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/build_am_bsp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/build_am_bsp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles 0
.PHONY : CMakeFiles/build_am_bsp.dir/rule

# Convenience name for target.
build_am_bsp: CMakeFiles/build_am_bsp.dir/rule
.PHONY : build_am_bsp

# clean rule for target.
CMakeFiles/build_am_bsp.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/build_am_bsp.dir/build.make CMakeFiles/build_am_bsp.dir/clean
.PHONY : CMakeFiles/build_am_bsp.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

