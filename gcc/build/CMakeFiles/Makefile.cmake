# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/CMakeLists.txt"
  "/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/arm-gcc-toolchain.cmake"
  "CMakeFiles/3.28.3/CMakeASMCompiler.cmake"
  "CMakeFiles/3.28.3/CMakeCCompiler.cmake"
  "CMakeFiles/3.28.3/CMakeSystem.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeASMCompiler.cmake.in"
  "/usr/share/cmake-3.28/Modules/CMakeASMInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeCCompiler.cmake.in"
  "/usr/share/cmake-3.28/Modules/CMakeCCompilerABI.c"
  "/usr/share/cmake-3.28/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeCompilerIdDetection.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeDetermineASMCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeDetermineCCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeDetermineCompileFeatures.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeDetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeFindBinUtils.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeParseLibraryArchitecture.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeSystem.cmake.in"
  "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeTestASMCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeTestCompilerCommon.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeUnixFindMake.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/CrayClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/GNU-ASM.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/GNU-FindBinUtils.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/LCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/OrangeC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/TI-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/Tasking-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.28/Modules/Internal/FeatureTesting.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Generic.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.28.3/CMakeSystem.cmake"
  "CMakeFiles/3.28.3/CMakeCCompiler.cmake"
  "CMakeFiles/3.28.3/CMakeASMCompiler.cmake"
  "CMakeFiles/3.28.3/CMakeCCompiler.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/lvgl_test.dir/DependInfo.cmake"
  "CMakeFiles/build_am_hal.dir/DependInfo.cmake"
  "CMakeFiles/build_am_bsp.dir/DependInfo.cmake"
  )
