# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build

# Utility rule file for build_am_hal.

# Include any custom commands dependencies for this target.
include CMakeFiles/build_am_hal.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/build_am_hal.dir/progress.make

CMakeFiles/build_am_hal:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building Ambiq HAL library"
	/usr/bin/gmake -C /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/../../../../../../mcu/apollo4p/hal/mcu

build_am_hal: CMakeFiles/build_am_hal
build_am_hal: CMakeFiles/build_am_hal.dir/build.make
.PHONY : build_am_hal

# Rule to build all files generated by this target.
CMakeFiles/build_am_hal.dir/build: build_am_hal
.PHONY : CMakeFiles/build_am_hal.dir/build

CMakeFiles/build_am_hal.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/build_am_hal.dir/cmake_clean.cmake
.PHONY : CMakeFiles/build_am_hal.dir/clean

CMakeFiles/build_am_hal.dir/depend:
	cd /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles/build_am_hal.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/build_am_hal.dir/depend

