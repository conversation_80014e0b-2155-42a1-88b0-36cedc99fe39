# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/gcc/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named lvgl_test

# Build rule for target.
lvgl_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lvgl_test
.PHONY : lvgl_test

# fast build rule for target.
lvgl_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/build
.PHONY : lvgl_test/fast

#=============================================================================
# Target rules for targets named build_am_hal

# Build rule for target.
build_am_hal: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 build_am_hal
.PHONY : build_am_hal

# fast build rule for target.
build_am_hal/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/build_am_hal.dir/build.make CMakeFiles/build_am_hal.dir/build
.PHONY : build_am_hal/fast

#=============================================================================
# Target rules for targets named build_am_bsp

# Build rule for target.
build_am_bsp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 build_am_bsp
.PHONY : build_am_bsp

# fast build rule for target.
build_am_bsp/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/build_am_bsp.dir/build.make CMakeFiles/build_am_bsp.dir/build
.PHONY : build_am_bsp/fast

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.obj: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.i: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.i

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.s: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.c.s

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.obj: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.i: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.i

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.s: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.c.s

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.obj: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.i: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.i

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.s: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.c.s

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.obj: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.i: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.i

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.s: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.c.s

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.obj: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.i: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.i

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.s: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.c.s

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.obj: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.i: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.i

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.s: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.c.s

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.obj: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.i: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.i

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.s: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.c.s

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.obj: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.i: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.i

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.s: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.c.s

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.obj: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.i: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.i

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.s: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.c.s

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.obj: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.i: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.i

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.s: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.c.s

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.obj: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.i: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.i

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.s: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.c.s

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.obj: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.i: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.i

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.s: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.c.s

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.obj: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.i: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.i

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.s: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.c.s

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.obj: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.i: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.i

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.s: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.c.s

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.obj: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.i: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.i

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.s: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.c.s

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.obj: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.i: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.i

home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.s: home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.c.s

home/leo/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.obj: home/leo/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.i: home/leo/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.i

home/leo/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.s: home/leo/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.c.s

home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.obj: home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.i: home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.i

home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.s: home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.c.s

home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.obj: home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.i: home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.i

home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.s: home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.c.s

home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.obj: home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.i: home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.i

home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.s: home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.c.s

home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_id.obj: home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_id.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_id.i: home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_id.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.i

home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_id.s: home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_id.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_id.c.s

home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.obj: home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.i: home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.i

home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.s: home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.c.s

home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.obj: home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.obj

# target to build an object file
home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.obj
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.obj

home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.i: home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.i

# target to preprocess a source file
home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.i
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.i

home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.s: home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.s

# target to generate assembly for a file
home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/home/<USER>/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.s
.PHONY : home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.c.s

startup_gcc.obj: startup_gcc.c.obj
.PHONY : startup_gcc.obj

# target to build an object file
startup_gcc.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/startup_gcc.c.obj
.PHONY : startup_gcc.c.obj

startup_gcc.i: startup_gcc.c.i
.PHONY : startup_gcc.i

# target to preprocess a source file
startup_gcc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/startup_gcc.c.i
.PHONY : startup_gcc.c.i

startup_gcc.s: startup_gcc.c.s
.PHONY : startup_gcc.s

# target to generate assembly for a file
startup_gcc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lvgl_test.dir/build.make CMakeFiles/lvgl_test.dir/startup_gcc.c.s
.PHONY : startup_gcc.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... build_am_bsp"
	@echo "... build_am_hal"
	@echo "... lvgl_test"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/AMOLED.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/I2C.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/UART.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_hal_mspi.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/am_resources.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/aps12808l.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/board_init.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/gui_task.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_core.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/hynitron_cst92xx.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/lvgl_test.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_display_task.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/minimal_mem.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/rtos.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_apollo4.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/boards/apollo4p_evb_disp_shield_rev2/examples/graphics/lvgl_test_happystone/src/sh8601_example.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/devices/am_devices_display_generic.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/event_groups.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/list.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/GCC/AMapollo4/port.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/portable/MemMang/heap_4.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/queue.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/tasks.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/FreeRTOSv10.5.1/Source/timers.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_cpu_only.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_fake.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_and_partial_fb.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_one_fb.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/display_task_two_fb.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_decoder.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_font_align.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_fs.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_misc.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_nema_hal.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/ambiq_support/lv_ambiq_touch.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_disp.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_event.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_group.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_class.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_draw.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_pos.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_scroll.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_style_gen.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_obj_tree.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_refr.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/core/lv_theme.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_arc.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_blend.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_img.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_label.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_line.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_mask.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_rect.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/draw/lv_draw_triangle.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_fmt_txt.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_loader.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_10.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_12_subpx.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_14.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_16.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_18.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_20.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_22.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_24.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_26.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_28_compressed.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_30.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_32.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_34.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_36.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_38.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_40.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_42.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_44.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_46.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_48.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_montserrat_8.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_simsun_16_cjk.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_16.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/font/lv_font_unscii_8.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/gpu/lv_gpu_ambiq_nema.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_disp.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_indev.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/hal/lv_hal_tick.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/flex/lv_flex.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/layouts/grid/lv_grid.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/bmp/lv_bmp.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/freetype/lv_freetype.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_fatfs.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_posix.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_stdio.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/fsdrv/lv_fs_win32.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/gifdec.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/gif/lv_gif.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lodepng.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/png/lv_png.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/lv_qrcode.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/qrcode/qrcodegen.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/rlottie/lv_rlottie.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/lv_sjpg.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/libs/sjpg/tjpgd.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_anim_timeline.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_area.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_async.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_bidi.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_color.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_fs.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_gc.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_ll.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_log.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_mem.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_printf.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_style_gen.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_templ.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_timer.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_tlsf.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_txt_ap.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/misc/lv_utils.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/others/snapshot/lv_snapshot.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/basic/lv_theme_basic.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/default/lv_theme_default.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/themes/mono/lv_theme_mono.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/animimg/lv_animimg.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/arc/lv_arc.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/bar/lv_bar.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btn/lv_btn.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/btnmatrix/lv_btnmatrix.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_arrow.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/canvas/lv_canvas.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/chart/lv_chart.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/checkbox/lv_checkbox.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/colorwheel/lv_colorwheel.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/dropdown/lv_dropdown.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/img/lv_img.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/imgbtn/lv_imgbtn.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/keyboard/lv_keyboard.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/label/lv_label.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/led/lv_led.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/line/lv_line.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/list/lv_list.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/meter/lv_meter.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/msgbox/lv_msgbox.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/objx_templ/lv_objx_templ.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/roller/lv_roller.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/slider/lv_slider.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/span/lv_span.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinbox/lv_spinbox.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/spinner/lv_spinner.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/switch/lv_switch.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/table/lv_table.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tabview/lv_tabview.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/textarea/lv_textarea.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/tileview/lv_tileview.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/LVGL/lvgl/src/widgets/win/lv_win.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_dc_hal.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_event.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_hal.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/third_party/ThinkSi/config/apollo4p_nemagfx/nema_utils.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_delay.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_faultisr.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_id.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_id.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_id.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_stdio.s"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.obj"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.i"
	@echo "... home/leo/dev/AmbiqSuite_R4.5.0/utils/am_util_syscalls.s"
	@echo "... startup_gcc.obj"
	@echo "... startup_gcc.i"
	@echo "... startup_gcc.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

