# CMakeLists.txt for LVGL Test Happystone Example
# Converted from Makefile for Apollo4P EVB Display Shield Rev2

cmake_minimum_required(VERSION 3.16)

# Set the project name and version
project(lvgl_test_happystone VERSION 1.0.0 LANGUAGES C ASM)

# Set C standard
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Set the output directory
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Define the target
set(TARGET_NAME lvgl_test)

# =============================================================================
# COMPILER DEFINITIONS
# =============================================================================

# Preprocessor definitions
set(PROJECT_DEFINES
    PART_apollo4p
    AM_FREERTOS
    AM_PACKAGE_BGA
    AM_PART_APOLLO4P
    AM_UTIL_FAULTISR_PRINT
    LD_LIBRARY_PATH=../../../../../third_party/ThinkSi/NemaGFX_SDK/lib
    USE_MINIMAL_DISPLAY_TASK
    LV_AMBIQ_FB_RESX=390
    LV_AMBIQ_FB_RESY=390
    LV_AMBIQ_FB_USE_RGB565=1
    LV_AMBIQ_FB_USE_RGB888=0
    LV_CONF_INCLUDE_SIMPLE
    LV_LVGL_H_INCLUDE_SIMPLE
    NEMAGFX_SDK_PATH=../../../../../third_party/ThinkSi/NemaGFX_SDK
    NEMA_CUSTOM_MALLOC_INCLUDE="lv_ambiq_nema_hal.h"
    NEMA_PLATFORM=apollo4p_nemagfx
    NEMA_USE_CUSTOM_MALLOC
    WAIT_IRQ_BINARY_SEMAPHORE=1
    apollo4p_evb_disp_shield_rev2
    gcc
)

# =============================================================================
# INCLUDE DIRECTORIES
# =============================================================================

# Base AmbiqSuite paths
set(AMBISUITE_ROOT "${CMAKE_CURRENT_SOURCE_DIR}/../../../../../../")
set(CMSIS_ROOT "${AMBISUITE_ROOT}CMSIS")
set(DEVICES_ROOT "${AMBISUITE_ROOT}devices")
set(MCU_ROOT "${AMBISUITE_ROOT}mcu/apollo4p")
set(UTILS_ROOT "${AMBISUITE_ROOT}utils")
set(BSP_ROOT "${CMAKE_CURRENT_SOURCE_DIR}/../../../../bsp")

# Third-party paths
set(FREERTOS_ROOT "${AMBISUITE_ROOT}third_party/FreeRTOSv10.5.1")
set(LVGL_ROOT "${AMBISUITE_ROOT}third_party/LVGL")
set(THINKSI_ROOT "${AMBISUITE_ROOT}third_party/ThinkSi")

# Include directories
set(INCLUDE_DIRS
    ${AMBISUITE_ROOT}
    ${CMSIS_ROOT}/ARM/Include
    ${CMSIS_ROOT}/AmbiqMicro/Include
    ${DEVICES_ROOT}
    ${MCU_ROOT}
    ${MCU_ROOT}/hal
    ${FREERTOS_ROOT}/Source/include
    ${FREERTOS_ROOT}/Source/portable/GCC/AMapollo4
    ${LVGL_ROOT}/ambiq_support
    ${LVGL_ROOT}/lvgl/src
    ${LVGL_ROOT}/lvgl/src/gpu
    ${THINKSI_ROOT}/NemaGFX_SDK/NemaDC
    ${THINKSI_ROOT}/NemaGFX_SDK/NemaGFX
    ${THINKSI_ROOT}/NemaGFX_SDK/common/mem
    ${THINKSI_ROOT}/NemaGFX_SDK/include/tsi/NemaDC
    ${THINKSI_ROOT}/NemaGFX_SDK/include/tsi/NemaGFX
    ${THINKSI_ROOT}/NemaGFX_SDK/include/tsi/NemaVG
    ${THINKSI_ROOT}/NemaGFX_SDK/include/tsi/common
    ${THINKSI_ROOT}/config/apollo4p_nemagfx
    ${UTILS_ROOT}
    ${BSP_ROOT}
    ../src
)

# =============================================================================
# SOURCE FILES
# =============================================================================

# Device and utility sources
set(DEVICE_SOURCES
    ${DEVICES_ROOT}/am_devices_display_generic.c
    ${UTILS_ROOT}/am_util_delay.c
    ${UTILS_ROOT}/am_util_faultisr.c
    ${UTILS_ROOT}/am_util_id.c
    ${UTILS_ROOT}/am_util_stdio.c
    ${UTILS_ROOT}/am_util_syscalls.c
)

# Display task sources
set(DISPLAY_TASK_SOURCES
    ${LVGL_ROOT}/ambiq_support/display_task_cpu_only.c
    ${LVGL_ROOT}/ambiq_support/display_task_fake.c
    ${LVGL_ROOT}/ambiq_support/display_task_one_and_partial_fb.c
    ${LVGL_ROOT}/ambiq_support/display_task_one_fb.c
    ${LVGL_ROOT}/ambiq_support/display_task_two_fb.c
)

# FreeRTOS sources
set(FREERTOS_SOURCES
    ${FREERTOS_ROOT}/Source/event_groups.c
    ${FREERTOS_ROOT}/Source/list.c
    ${FREERTOS_ROOT}/Source/queue.c
    ${FREERTOS_ROOT}/Source/tasks.c
    ${FREERTOS_ROOT}/Source/timers.c
    ${FREERTOS_ROOT}/Source/portable/MemMang/heap_4.c
    ${FREERTOS_ROOT}/Source/portable/GCC/AMapollo4/port.c
)

# LVGL core sources
set(LVGL_CORE_SOURCES
    ${LVGL_ROOT}/lvgl/src/core/lv_disp.c
    ${LVGL_ROOT}/lvgl/src/core/lv_event.c
    ${LVGL_ROOT}/lvgl/src/core/lv_group.c
    ${LVGL_ROOT}/lvgl/src/core/lv_obj.c
    ${LVGL_ROOT}/lvgl/src/core/lv_obj_class.c
    ${LVGL_ROOT}/lvgl/src/core/lv_obj_draw.c
    ${LVGL_ROOT}/lvgl/src/core/lv_obj_pos.c
    ${LVGL_ROOT}/lvgl/src/core/lv_obj_scroll.c
    ${LVGL_ROOT}/lvgl/src/core/lv_obj_style.c
    ${LVGL_ROOT}/lvgl/src/core/lv_obj_style_gen.c
    ${LVGL_ROOT}/lvgl/src/core/lv_obj_tree.c
    ${LVGL_ROOT}/lvgl/src/core/lv_refr.c
    ${LVGL_ROOT}/lvgl/src/core/lv_theme.c
)

# LVGL draw sources
set(LVGL_DRAW_SOURCES
    ${LVGL_ROOT}/lvgl/src/draw/lv_draw_arc.c
    ${LVGL_ROOT}/lvgl/src/draw/lv_draw_blend.c
    ${LVGL_ROOT}/lvgl/src/draw/lv_draw_img.c
    ${LVGL_ROOT}/lvgl/src/draw/lv_draw_label.c
    ${LVGL_ROOT}/lvgl/src/draw/lv_draw_line.c
    ${LVGL_ROOT}/lvgl/src/draw/lv_draw_mask.c
    ${LVGL_ROOT}/lvgl/src/draw/lv_draw_rect.c
    ${LVGL_ROOT}/lvgl/src/draw/lv_draw_triangle.c
)

# LVGL font sources
set(LVGL_FONT_SOURCES
    ${LVGL_ROOT}/lvgl/src/font/lv_font.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_fmt_txt.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_loader.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_10.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_12.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_12_subpx.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_14.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_16.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_18.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_20.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_22.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_24.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_26.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_28.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_28_compressed.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_30.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_32.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_34.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_36.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_38.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_40.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_42.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_44.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_46.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_48.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_8.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_simsun_16_cjk.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_unscii_16.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_unscii_8.c
)

# LVGL GPU sources
set(LVGL_GPU_SOURCES
    ${LVGL_ROOT}/lvgl/src/gpu/lv_gpu_ambiq_nema.c
)

# LVGL HAL sources
set(LVGL_HAL_SOURCES
    ${LVGL_ROOT}/lvgl/src/hal/lv_hal_disp.c
    ${LVGL_ROOT}/lvgl/src/hal/lv_hal_indev.c
    ${LVGL_ROOT}/lvgl/src/hal/lv_hal_tick.c
)

# LVGL layout sources
set(LVGL_LAYOUT_SOURCES
    ${LVGL_ROOT}/lvgl/src/layouts/flex/lv_flex.c
    ${LVGL_ROOT}/lvgl/src/layouts/grid/lv_grid.c
)

# LVGL library sources
set(LVGL_LIB_SOURCES
    ${LVGL_ROOT}/lvgl/src/libs/bmp/lv_bmp.c
    ${LVGL_ROOT}/lvgl/src/libs/freetype/lv_freetype.c
    ${LVGL_ROOT}/lvgl/src/libs/fsdrv/lv_fs_fatfs.c
    ${LVGL_ROOT}/lvgl/src/libs/fsdrv/lv_fs_posix.c
    ${LVGL_ROOT}/lvgl/src/libs/fsdrv/lv_fs_stdio.c
    ${LVGL_ROOT}/lvgl/src/libs/fsdrv/lv_fs_win32.c
    ${LVGL_ROOT}/lvgl/src/libs/gif/gifdec.c
    ${LVGL_ROOT}/lvgl/src/libs/gif/lv_gif.c
    ${LVGL_ROOT}/lvgl/src/libs/png/lodepng.c
    ${LVGL_ROOT}/lvgl/src/libs/png/lv_png.c
    ${LVGL_ROOT}/lvgl/src/libs/qrcode/lv_qrcode.c
    ${LVGL_ROOT}/lvgl/src/libs/qrcode/qrcodegen.c
    ${LVGL_ROOT}/lvgl/src/libs/rlottie/lv_rlottie.c
    ${LVGL_ROOT}/lvgl/src/libs/sjpg/lv_sjpg.c
    ${LVGL_ROOT}/lvgl/src/libs/sjpg/tjpgd.c
)

# LVGL misc sources
set(LVGL_MISC_SOURCES
    ${LVGL_ROOT}/lvgl/src/misc/lv_anim.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_anim_timeline.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_area.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_async.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_bidi.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_color.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_fs.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_gc.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_ll.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_log.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_mem.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_printf.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_style.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_style_gen.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_templ.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_timer.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_tlsf.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_txt.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_txt_ap.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_utils.c
)

# LVGL other sources
set(LVGL_OTHER_SOURCES
    ${LVGL_ROOT}/lvgl/src/others/snapshot/lv_snapshot.c
)

# LVGL theme sources
set(LVGL_THEME_SOURCES
    ${LVGL_ROOT}/lvgl/src/themes/basic/lv_theme_basic.c
    ${LVGL_ROOT}/lvgl/src/themes/default/lv_theme_default.c
    ${LVGL_ROOT}/lvgl/src/themes/mono/lv_theme_mono.c
)

# LVGL widget sources (selected widgets)
set(LVGL_WIDGET_SOURCES
    ${LVGL_ROOT}/lvgl/src/widgets/animimg/lv_animimg.c
    ${LVGL_ROOT}/lvgl/src/widgets/arc/lv_arc.c
    ${LVGL_ROOT}/lvgl/src/widgets/bar/lv_bar.c
    ${LVGL_ROOT}/lvgl/src/widgets/btn/lv_btn.c
    ${LVGL_ROOT}/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c
    ${LVGL_ROOT}/lvgl/src/widgets/calendar/lv_calendar.c
    ${LVGL_ROOT}/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c
    ${LVGL_ROOT}/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c
    ${LVGL_ROOT}/lvgl/src/widgets/canvas/lv_canvas.c
    ${LVGL_ROOT}/lvgl/src/widgets/chart/lv_chart.c
    ${LVGL_ROOT}/lvgl/src/widgets/checkbox/lv_checkbox.c
    ${LVGL_ROOT}/lvgl/src/widgets/colorwheel/lv_colorwheel.c
    ${LVGL_ROOT}/lvgl/src/widgets/dropdown/lv_dropdown.c
    ${LVGL_ROOT}/lvgl/src/widgets/img/lv_img.c
    ${LVGL_ROOT}/lvgl/src/widgets/imgbtn/lv_imgbtn.c
    ${LVGL_ROOT}/lvgl/src/widgets/keyboard/lv_keyboard.c
    ${LVGL_ROOT}/lvgl/src/widgets/label/lv_label.c
    ${LVGL_ROOT}/lvgl/src/widgets/led/lv_led.c
    ${LVGL_ROOT}/lvgl/src/widgets/line/lv_line.c
    ${LVGL_ROOT}/lvgl/src/widgets/list/lv_list.c
    ${LVGL_ROOT}/lvgl/src/widgets/meter/lv_meter.c
    ${LVGL_ROOT}/lvgl/src/widgets/msgbox/lv_msgbox.c
    ${LVGL_ROOT}/lvgl/src/widgets/objx_templ/lv_objx_templ.c
    ${LVGL_ROOT}/lvgl/src/widgets/roller/lv_roller.c
    ${LVGL_ROOT}/lvgl/src/widgets/slider/lv_slider.c
    ${LVGL_ROOT}/lvgl/src/widgets/span/lv_span.c
    ${LVGL_ROOT}/lvgl/src/widgets/spinbox/lv_spinbox.c
    ${LVGL_ROOT}/lvgl/src/widgets/spinner/lv_spinner.c
    ${LVGL_ROOT}/lvgl/src/widgets/switch/lv_switch.c
    ${LVGL_ROOT}/lvgl/src/widgets/table/lv_table.c
    ${LVGL_ROOT}/lvgl/src/widgets/tabview/lv_tabview.c
    ${LVGL_ROOT}/lvgl/src/widgets/textarea/lv_textarea.c
    ${LVGL_ROOT}/lvgl/src/widgets/tileview/lv_tileview.c
    ${LVGL_ROOT}/lvgl/src/widgets/win/lv_win.c
)

# LVGL Ambiq support sources
set(LVGL_AMBIQ_SOURCES
    ${LVGL_ROOT}/ambiq_support/lv_ambiq_decoder.c
    ${LVGL_ROOT}/ambiq_support/lv_ambiq_font_align.c
    ${LVGL_ROOT}/ambiq_support/lv_ambiq_fs.c
    ${LVGL_ROOT}/ambiq_support/lv_ambiq_misc.c
    ${LVGL_ROOT}/ambiq_support/lv_ambiq_nema_hal.c
    ${LVGL_ROOT}/ambiq_support/lv_ambiq_touch.c
)

# ThinkSi NemaGFX sources
set(THINKSI_SOURCES
    ${THINKSI_ROOT}/config/apollo4p_nemagfx/nema_dc_hal.c
    ${THINKSI_ROOT}/config/apollo4p_nemagfx/nema_event.c
    ${THINKSI_ROOT}/config/apollo4p_nemagfx/nema_hal.c
    ${THINKSI_ROOT}/config/apollo4p_nemagfx/nema_utils.c
)

# Project-specific sources
set(PROJECT_SOURCES
    ../src/am_hal_mspi.c
    ../src/am_resources.c
    ../src/board_init.c
    ../src/AMOLED.c
    ../src/aps12808l.c
    ../src/gui_task.c
    ../src/hynitron_core.c
    ../src/hynitron_cst92xx.c
    ../src/I2C.c
    ../src/sh8601_apollo4.c
    ../src/sh8601_example.c
    ../src/UART.c
    ../src/minimal_display_task.c
    ../src/minimal_mem.c
    ../src/lvgl_test.c
    ../src/rtos.c
    startup_gcc.c
)

# Combine all sources
set(ALL_SOURCES
    ${DEVICE_SOURCES}
    ${DISPLAY_TASK_SOURCES}
    ${FREERTOS_SOURCES}
    ${LVGL_CORE_SOURCES}
    ${LVGL_DRAW_SOURCES}
    ${LVGL_FONT_SOURCES}
    ${LVGL_GPU_SOURCES}
    ${LVGL_HAL_SOURCES}
    ${LVGL_LAYOUT_SOURCES}
    ${LVGL_LIB_SOURCES}
    ${LVGL_MISC_SOURCES}
    ${LVGL_OTHER_SOURCES}
    ${LVGL_THEME_SOURCES}
    ${LVGL_WIDGET_SOURCES}
    ${LVGL_AMBIQ_SOURCES}
    ${THINKSI_SOURCES}
    ${PROJECT_SOURCES}
)

# =============================================================================
# STATIC LIBRARIES
# =============================================================================

set(STATIC_LIBS
    ${CMSIS_ROOT}/ARM/Lib/ARM/libarm_cortexM4lf_math.a
    ${MCU_ROOT}/hal/mcu/gcc/bin/libam_hal.a
    ${LVGL_ROOT}/ambiq_support/gpu_lib_apollo4/gcc/bin/lvgl_ambiq_porting.a
    ${THINKSI_ROOT}/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a
    ${BSP_ROOT}/gcc/bin/libam_bsp.a
)

# =============================================================================
# CREATE EXECUTABLE
# =============================================================================

# Create the executable
add_executable(${TARGET_NAME} ${ALL_SOURCES})

# Set target properties
set_target_properties(${TARGET_NAME} PROPERTIES
    OUTPUT_NAME ${TARGET_NAME}
    SUFFIX ".axf"
)

# Add include directories
target_include_directories(${TARGET_NAME} PRIVATE ${INCLUDE_DIRS})

# Add compile definitions
target_compile_definitions(${TARGET_NAME} PRIVATE ${PROJECT_DEFINES})

# Link static libraries
target_link_libraries(${TARGET_NAME} PRIVATE ${STATIC_LIBS})

# Add linker script and other options
target_link_options(${TARGET_NAME} PRIVATE
    -T${CMAKE_CURRENT_SOURCE_DIR}/linker_script.ld
    -Wl,--gc-sections
    -Wl,--entry,Reset_Handler
    -Wl,-Map,${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${TARGET_NAME}.map
    -Wl,--start-group
    -lm
    -lc
    -lgcc
    -lnosys
    -Wl,--end-group
)

# =============================================================================
# CUSTOM TARGETS
# =============================================================================

# Create binary file from ELF
add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
    COMMAND ${CMAKE_OBJCOPY} -Obinary $<TARGET_FILE:${TARGET_NAME}> ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${TARGET_NAME}.bin
    COMMAND ${CMAKE_OBJDUMP} -S $<TARGET_FILE:${TARGET_NAME}> > ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${TARGET_NAME}.lst
    COMMAND ${CMAKE_SIZE} $<TARGET_FILE:${TARGET_NAME}> > ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${TARGET_NAME}.size
    COMMENT "Creating binary and listing files"
)

# =============================================================================
# BUILD DEPENDENCIES
# =============================================================================

# Add custom targets for building dependencies
add_custom_target(build_am_hal
    COMMAND ${CMAKE_MAKE_PROGRAM} -C ${MCU_ROOT}/hal/mcu
    COMMENT "Building Ambiq HAL library"
)

add_custom_target(build_am_bsp
    COMMAND ${CMAKE_MAKE_PROGRAM} -C ${BSP_ROOT}
    COMMENT "Building Ambiq BSP library"
)

# Make the main target depend on the libraries
add_dependencies(${TARGET_NAME} build_am_hal build_am_bsp)

# =============================================================================
# INSTALL TARGETS
# =============================================================================

# Install the binary
install(TARGETS ${TARGET_NAME}
    RUNTIME DESTINATION bin
)

# Install the binary file
install(FILES ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${TARGET_NAME}.bin
    DESTINATION bin
)
