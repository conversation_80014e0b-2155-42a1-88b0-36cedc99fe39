#include "hynitron_core.h"
#if HYNITRON_ENABLE_UPGRADE
#include "hynitron_firmware.h"
#endif

struct hyn_chip g_chip_obj;
struct hyn_chip *p_g_chip_obj;
static void (*const g_init_chip_obj[])(struct hyn_chip *) =
{
        hyn_cst92xx_init_obj,
};
// Function to be modified for drive migration
void hyn_delay_ms(uint16_t ms)
{
    DELAY_MS(ms);
}
// Function to be modified for drive migration
void hyn_reset_ic(void)
{
    RST_PIN_OUT_LOW;
    hyn_delay_ms(10);
    RST_PIN_OUT_HIGH;
    p_g_chip_obj->chip_ic_workmode = ENUM_MODE_NORMAL;
}
int16_t hyn_poweron_ic(bool on)
{
    if (on == FALSE)
    {
        // set power pin low
    }
    else
    {
        // set power pin high
    }
    p_g_chip_obj->chip_ic_workmode = ENUM_MODE_NORMAL;
    return 0;
}
// Function to be modified for drive migration
// Look out  return value
// return -1 : fail
// return 0 : success
int16_t hyn_i2c_write(uint8_t addr, uint8_t *buf, uint16_t len)
{
    int16_t ret = 0;
    for (uint8_t i = 0;; i++)
    {
        if (i >= 3)
        {
            return -1;
        }
        ret = hyn_i2c_write_port(addr, buf, len);
        // 	    HYNITRON_DEBUG("iic_write_data:%d",ret);
        if (ret)
        {
            continue;
        }
        break;
    }
    return 0;
}
// Function to be modified for drive migration
// Look out  return value
// return -1 : fail
// return 0 : success
int16_t hyn_i2c_read(uint8_t addr, uint8_t *buf, uint16_t len)
{
    int16_t ret = 0;
    for (uint8_t i = 0;; i++)
    {
        if (i >= 3)
        {
            return -1;
        }
        ret = hyn_i2c_read_port(addr, buf, len);
        //        HYNITRON_DEBUG("iic_read_data:%d",ret);
        if (ret)
        {
            continue;
        }
        break;
    }
    return 0;
}
/********************************************************/
void hyn_report_finger(void)
{
    if (!p_g_chip_obj)
    {
        HYNITRON_ERROR("p_g_chip_obj NULL return");
        return;
    }
    if (p_g_chip_obj->touch_info.finger_num)
    {
        //HYNITRON_DEBUG("finger num:%d", p_g_chip_obj->touch_info.finger_num);
        //HYNITRON_DEBUG("report id:%d", p_g_chip_obj->point_info[0].finger_id);
        //HYNITRON_DEBUG("valid :%d", p_g_chip_obj->point_info[0].valid);
				if(p_g_chip_obj->point_info[0].evt)
				{
					HYNITRON_DEBUG("report x:%d", p_g_chip_obj->point_info[0].x);
					HYNITRON_DEBUG("report y:%d", p_g_chip_obj->point_info[0].y);
				}
//        HYNITRON_DEBUG("report evt:%d", p_g_chip_obj->point_info[0].evt);
//        HYNITRON_DEBUG("report x:%d", p_g_chip_obj->point_info[0].x);
//        HYNITRON_DEBUG("report y:%d", p_g_chip_obj->point_info[0].y);
    }
}
void hyn_report_button(void)
{
    if (!p_g_chip_obj)
    {
        HYNITRON_ERROR("p_g_chip_obj NULL return");
        return;
    }
    HYNITRON_DEBUG("report button:%d", p_g_chip_obj->touch_info.key_id);
}
void hyn_report_big_palm()
{
    if (!p_g_chip_obj)
    {
        HYNITRON_ERROR("p_g_chip_obj NULL return");
        return;
    }
    if ((p_g_chip_obj->status.sleep_done) || (p_g_chip_obj->status.ges_report_done))
    {
        HYNITRON_DEBUG("status.sleep_done or status.ges_report_done may set");
        return;
    }
    if (p_g_chip_obj->touch_info.palm)
    {
        HYNITRON_DEBUG("report palm:%d", p_g_chip_obj->touch_info.palm);
        p_g_chip_obj->status.ges_report_done = TRUE;
    }
}
void hyn_report_gesture(void)
{
    if (!p_g_chip_obj)
    {
        HYNITRON_ERROR("p_g_chip_obj NULL return");
        return;
    }
    if ((!p_g_chip_obj->status.sleep_done) || (p_g_chip_obj->status.ges_report_done))
    {
        //HYNITRON_DEBUG("status.sleep_done maybe clear, or status.ges_report_done may set");
        return;
    }
    if (p_g_chip_obj->touch_info.gesture)
    {
        p_g_chip_obj->status.ges_report_done = TRUE;
        HYNITRON_DEBUG("report gesture:%d", p_g_chip_obj->touch_info.gesture);
    }
}
void hyn_tp_suspend(void)
{
    int16_t ret = 0;
    if (!p_g_chip_obj)
    {
        HYNITRON_ERROR("p_g_chip_obj NULL return");
        return;
    }
    if ((p_g_chip_obj->status.factory_test == 1) || (p_g_chip_obj->status.update_fw_running == 1))
    {
        HYNITRON_DEBUG("hyn_tp_suspend status NA return");
        return;
    }
    p_g_chip_obj->status.sleep_done = TRUE;
    p_g_chip_obj->status.ges_report_done = FALSE;

    ret = p_g_chip_obj->enter_sleep(DEEP_SLEEP);
    if (ret)
    {
        HYNITRON_ERROR("enter_sleep failed");
    }
}
void hyn_tp_resume(void)
{
    int16_t ret = 0;
    if (!p_g_chip_obj)
    {
        HYNITRON_ERROR("p_g_chip_obj NULL return");
        return;
    }
    if ((p_g_chip_obj->status.factory_test == 1) || (p_g_chip_obj->status.update_fw_running == 1))
    {
        HYNITRON_DEBUG("hyn_tp_resume status NA return");
        return;
    }
    p_g_chip_obj->status.sleep_done = FALSE;
    p_g_chip_obj->status.ges_report_done = FALSE;
    p_g_chip_obj->status.esd_enable = TRUE;
    ret = p_g_chip_obj->wake_up();
    if (ret)
    {
        HYNITRON_ERROR("wake_up failed");
    }
}
void hyn_tp_irq_handler(void)
{
    int16_t ret = 0;
    p_g_chip_obj->status.int_trig = 1;
    if ((p_g_chip_obj->chip_ic_workmode != ENUM_MODE_NORMAL) && (p_g_chip_obj->chip_ic_workmode != ENUM_MODE_WAKEUP))
    {
        HYNITRON_ERROR("hyn_tp_irq_handler chip_ic_workmode error");
        return;
    }
    ret = p_g_chip_obj->read_point();
    if (ret)
    {
        HYNITRON_ERROR("read finger information failed");
        return;
    }
    // hyn_report_button();

    hyn_report_big_palm();

    hyn_report_gesture();

    hyn_report_finger();
}

int16_t hyn_get_bin_addr(uint8_t data_seq, uint16_t data_len)
{

#if HYN_ENABLE_UPGRADE_128_BYTE_WRITE
    if (data_len > MEM_128_SIZE)
    {
        HYNITRON_ERROR("get_bin_addr data_len:0x%04x,force set MEM_128_SIZE", data_len);
        data_len = MEM_128_SIZE;
    }
    if (data_seq > (MEM_SIZE / MEM_128_SIZE))
    {
        HYNITRON_ERROR("get_bin_addr data_seq:0x%04x,force set MEM_SIZE/MEM_128_SIZE", data_seq);
        data_seq = (MEM_SIZE / MEM_128_SIZE);
    }
#endif

    p_g_chip_obj->bin_data.data = (uint8_t *)fw_data + (data_seq * data_len);
    p_g_chip_obj->bin_data.data_seq = data_seq;
    p_g_chip_obj->bin_data.data_len = data_len;
    // HYNITRON_DEBUG("get_bin_addr data_seq:0x%04x,data_len:0x%04x.data_point:0x%04x",data_seq,data_len,(data_seq*data_len));

    return 0;
}
int16_t hyn_tp_init(void)
{

    int16_t ret = 0;

    HYNITRON_DEBUG("%s:start driver init.", HYNITRON_DRIVER_VERSION);
    HYN_FUNC_ENTER;
    memset(&g_chip_obj, 0, sizeof(g_chip_obj));
    p_g_chip_obj = &g_chip_obj;
    g_init_chip_obj[0](&g_chip_obj);
    p_g_chip_obj->reset_ic = hyn_reset_ic;
    p_g_chip_obj->poweron_ic = hyn_poweron_ic;
    p_g_chip_obj->delay1ms = hyn_delay_ms;
    p_g_chip_obj->i2c_write = hyn_i2c_write;
    p_g_chip_obj->i2c_read = hyn_i2c_read;
    p_g_chip_obj->get_bin_addr = hyn_get_bin_addr;

    p_g_chip_obj->status.ic_init_done = 0;
    p_g_chip_obj->poweron_ic(TRUE);
    ret = p_g_chip_obj->read_chip_id();
    if (ret)
    {
        HYNITRON_ERROR("read_chip_id fail,error return");
        return -1;
    }

    ret = p_g_chip_obj->read_chip_info();
    if (ret)
    {
        HYNITRON_ERROR("read chip info fail,maybe chip null");
    }
    else
    {
        HYNITRON_DEBUG("chip firmware_info_ok: 0x%04x", p_g_chip_obj->IC_firmware.firmware_info_ok);
        HYNITRON_DEBUG("chip checksum: 0x%04x", p_g_chip_obj->IC_firmware.firmware_checksum);
        HYNITRON_DEBUG("chip firmware_version: 0x%04x", p_g_chip_obj->IC_firmware.firmware_version);
        HYNITRON_DEBUG("chip firmware_project_id: 0x%04x", p_g_chip_obj->IC_firmware.firmware_project_id);
    }

#if (HYNITRON_ENABLE_UPGRADE == 2)//1-upgrade,2-do not upgrade
    {
        uint8_t need_upgrade = 0;
        need_upgrade = 0;
#if HYN_ENABLE_UPGRADE_128_BYTE_WRITE
        ret = p_g_chip_obj->get_bin_addr(0, MEM_128_SIZE);
#else
        ret = p_g_chip_obj->get_bin_addr(0, MEM_SIZE);
#endif
        if (ret < 0)
        {
            HYNITRON_ERROR("get_bin_addr fail.");
            goto END_UPGRADE;
        }
        ret = p_g_chip_obj->bin_data_parse();
        if (ret < 0)
        {
            HYNITRON_ERROR("bin_data_parse fail.");
            goto END_UPGRADE;
        }
        HYNITRON_DEBUG("bin_data.ok: 0x%x", p_g_chip_obj->bin_data.ok);
        HYNITRON_DEBUG("bin_data.checksum: 0x%04x", p_g_chip_obj->bin_data.checksum);
        HYNITRON_DEBUG("bin_data.version: 0x%04x", p_g_chip_obj->bin_data.version);
        HYNITRON_DEBUG("bin_data.project_id: 0x%04x", p_g_chip_obj->bin_data.project_id);
        HYNITRON_DEBUG("bin_data.chip_type: 0x%04x", p_g_chip_obj->bin_data.chip_type);
        ret = p_g_chip_obj->upgrade_firmware_judge();
        if (ret)
        {
            HYNITRON_DEBUG("upgrade_firmware_judge return,no need update fw.");
            goto END_UPGRADE;
        }
        else
        {
            need_upgrade = 1;
        }
        HYNITRON_DEBUG("need_upgrade=%d, firmware_version=0x%04X.", need_upgrade, p_g_chip_obj->bin_data.version);
        if (need_upgrade)
        {
            ret = p_g_chip_obj->upgrade_firmware();
            if (ret)
            {
                HYNITRON_ERROR("upgrade_firmware failed");
                goto END_UPGRADE;
            }
            HYNITRON_DEBUG("upgrade_firmware OK done.");
        }
    }
END_UPGRADE:
#endif

    p_g_chip_obj->reset_ic();
    p_g_chip_obj->delay1ms(30);
    ret = p_g_chip_obj->get_firmware_info();
    if (ret)
    {
        HYNITRON_ERROR("get_firmware_info failed");
    }
    p_g_chip_obj->chip_ic_workmode = ENUM_MODE_NORMAL;
    p_g_chip_obj->reset_ic();
    p_g_chip_obj->delay1ms(30);
    p_g_chip_obj->status.ic_init_done = 1;
    p_g_chip_obj->status.esd_enable = TRUE;
    HYNITRON_DEBUG("hyn_tp_init*************DONE*************");
    HYN_FUNC_EXIT;
    return 0;
}
