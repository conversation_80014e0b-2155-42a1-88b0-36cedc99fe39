#ifndef BRICK_50_43_ARGB8888__
#define BRICK_50_43_ARGB8888__


const unsigned char brick_50_43_argb8888[] = {
0xff, 0xd0, 0xa5, 0x92, 0xff, 0xbd, 0x84, 0x71, 0xff, 0xc4, 0x86, 0x71, 0xff, 0xc5, 0x88, 
0x73, 0xff, 0xc5, 0x87, 0x72, 0xff, 0xc3, 0x8b, 0x74, 0xff, 0xc5, 0x8d, 0x76, 0xff, 0xcd, 
0x8b, 0x75, 0xff, 0xc4, 0x87, 0x72, 0xff, 0xc2, 0x85, 0x70, 0xff, 0xc5, 0x88, 0x73, 0xff, 
0xc5, 0x88, 0x73, 0xff, 0xc7, 0x8a, 0x77, 0xff, 0xc6, 0x89, 0x76, 0xff, 0xc5, 0x86, 0x74, 
0xff, 0xc7, 0x88, 0x76, 0xff, 0xc8, 0x89, 0x77, 0xff, 0xc2, 0x83, 0x72, 0xff, 0xbe, 0x84, 
0x6e, 0xff, 0xc7, 0x86, 0x74, 0xff, 0xc5, 0x86, 0x75, 0xff, 0xba, 0x7c, 0x65, 0xff, 0xbe, 
0x80, 0x6b, 0xff, 0xcd, 0xa5, 0x9b, 0xff, 0xe4, 0xe3, 0xe1, 0xff, 0xcc, 0x9e, 0x8f, 0xff, 
0xc0, 0x75, 0x56, 0xff, 0xc8, 0x7c, 0x58, 0xff, 0xc8, 0x7d, 0x5d, 0xff, 0xc8, 0x7d, 0x5d, 
0xff, 0xc3, 0x7a, 0x57, 0xff, 0xbd, 0x71, 0x4f, 0xff, 0xc1, 0x78, 0x57, 0xff, 0xc0, 0x77, 
0x57, 0xff, 0xbd, 0x76, 0x58, 0xff, 0xb8, 0x73, 0x56, 0xff, 0xae, 0x67, 0x4b, 0xff, 0xb3, 
0x6c, 0x4e, 0xff, 0xc4, 0x7b, 0x5a, 0xff, 0xc2, 0x78, 0x53, 0xff, 0xc4, 0x79, 0x5a, 0xff, 
0xcc, 0x81, 0x62, 0xff, 0xbf, 0x74, 0x55, 0xff, 0xb7, 0x6e, 0x4e, 0xff, 0xc0, 0x76, 0x59, 
0xff, 0xb7, 0x6d, 0x50, 0xff, 0xb1, 0x6a, 0x4e, 0xff, 0xb8, 0x71, 0x55, 0xff, 0xd9, 0xb7, 
0xab, 0xff, 0xee, 0xee, 0xee, 0xff, 0xbe, 0x8e, 0x7a, 0xff, 0xc3, 0x88, 0x76, 0xff, 0xc3, 
0x86, 0x71, 0xff, 0xc7, 0x8a, 0x75, 0xff, 0xc6, 0x88, 0x73, 0xff, 0xc3, 0x89, 0x75, 0xff, 
0xbf, 0x87, 0x70, 0xff, 0xc7, 0x86, 0x72, 0xff, 0xc0, 0x83, 0x6e, 0xff, 0xc2, 0x85, 0x70, 
0xff, 0xc4, 0x87, 0x72, 0xff, 0xc5, 0x88, 0x73, 0xff, 0xbf, 0x82, 0x6f, 0xff, 0xc2, 0x85, 
0x72, 0xff, 0xc1, 0x82, 0x70, 0xff, 0xc7, 0x88, 0x76, 0xff, 0xc5, 0x87, 0x72, 0xff, 0xbf, 
0x80, 0x6e, 0xff, 0xbb, 0x81, 0x69, 0xff, 0xc3, 0x82, 0x6e, 0xff, 0xc4, 0x85, 0x73, 0xff, 
0xb9, 0x7b, 0x62, 0xff, 0xba, 0x79, 0x63, 0xff, 0xc6, 0x9c, 0x8e, 0xff, 0xe5, 0xe5, 0xe5, 
0xff, 0xc8, 0x9a, 0x8b, 0xff, 0xbb, 0x6e, 0x50, 0xff, 0xc1, 0x73, 0x4d, 0xff, 0xc6, 0x7a, 
0x58, 0xff, 0xc0, 0x76, 0x53, 0xff, 0xbf, 0x76, 0x53, 0xff, 0xc8, 0x7c, 0x5c, 0xff, 0xc7, 
0x7c, 0x5c, 0xff, 0xc3, 0x78, 0x59, 0xff, 0xc2, 0x78, 0x5b, 0xff, 0xbe, 0x77, 0x59, 0xff, 
0xb1, 0x6a, 0x4c, 0xff, 0xbf, 0x76, 0x56, 0xff, 0xc2, 0x79, 0x58, 0xff, 0xc4, 0x79, 0x59, 
0xff, 0xc2, 0x77, 0x58, 0xff, 0xc5, 0x7a, 0x5a, 0xff, 0xbb, 0x70, 0x51, 0xff, 0xbf, 0x76, 
0x56, 0xff, 0xc5, 0x7c, 0x5c, 0xff, 0xc0, 0x76, 0x59, 0xff, 0xb2, 0x68, 0x4b, 0xff, 0xc2, 
0x78, 0x5d, 0xff, 0xd1, 0xaf, 0xa3, 0xff, 0xea, 0xea, 0xea, 0xff, 0xbc, 0x83, 0x70, 0xff, 
0xc6, 0x87, 0x75, 0xff, 0xbb, 0x81, 0x69, 0xff, 0xbb, 0x81, 0x69, 0xff, 0xc3, 0x82, 0x6e, 
0xff, 0xca, 0x8b, 0x79, 0xff, 0xc8, 0x8f, 0x7b, 0xff, 0xc9, 0x8b, 0x76, 0xff, 0xc4, 0x87, 
0x74, 0xff, 0xc6, 0x89, 0x76, 0xff, 0xc4, 0x87, 0x74, 0xff, 0xc5, 0x88, 0x73, 0xff, 0xbe, 
0x81, 0x6c, 0xff, 0xc4, 0x87, 0x72, 0xff, 0xc1, 0x83, 0x6e, 0xff, 0xc5, 0x87, 0x72, 0xff, 
0xbc, 0x7e, 0x67, 0xff, 0xc1, 0x83, 0x6e, 0xff, 0xc2, 0x89, 0x6e, 0xff, 0xc5, 0x84, 0x6e, 
0xff, 0xbe, 0x80, 0x6b, 0xff, 0xb0, 0x70, 0x55, 0xff, 0xb2, 0x70, 0x58, 0xff, 0xc3, 0x98, 
0x88, 0xff, 0xe4, 0xe6, 0xe5, 0xff, 0xd3, 0xa5, 0x96, 0xff, 0xc6, 0x7a, 0x5a, 0xff, 0xbe, 
0x6f, 0x46, 0xff, 0xc6, 0x76, 0x51, 0xff, 0xc5, 0x79, 0x57, 0xff, 0xc1, 0x78, 0x57, 0xff, 
0xc2, 0x77, 0x58, 0xff, 0xbd, 0x70, 0x52, 0xff, 0xce, 0x83, 0x64, 0xff, 0xb5, 0x6c, 0x4c, 
0xff, 0xcb, 0x82, 0x62, 0xff, 0xc8, 0x7f, 0x5f, 0xff, 0xc0, 0x77, 0x57, 0xff, 0xc8, 0x7f, 
0x5f, 0xff, 0xc9, 0x80, 0x60, 0xff, 0xba, 0x6f, 0x4f, 0xff, 0xc4, 0x79, 0x59, 0xff, 0xc4, 
0x79, 0x59, 0xff, 0xb7, 0x6c, 0x4c, 0xff, 0xc5, 0x7a, 0x5b, 0xff, 0xc6, 0x7d, 0x5d, 0xff, 
0xbf, 0x75, 0x58, 0xff, 0xb6, 0x6c, 0x4f, 0xff, 0xcb, 0xa9, 0x9d, 0xff, 0xe8, 0xe8, 0xe8, 
0xff, 0xd0, 0x91, 0x80, 0xff, 0xc0, 0x7f, 0x6b, 0xff, 0xc4, 0x8a, 0x72, 0xff, 0xbf, 0x87, 
0x6e, 0xff, 0xc8, 0x87, 0x73, 0xff, 0xc9, 0x8a, 0x78, 0xff, 0xbe, 0x85, 0x71, 0xff, 0xc1, 
0x87, 0x73, 0xff, 0xc6, 0x89, 0x76, 0xff, 0xc7, 0x8a, 0x77, 0xff, 0xc4, 0x87, 0x74, 0xff, 
0xc3, 0x86, 0x71, 0xff, 0xbe, 0x81, 0x6c, 0xff, 0xc3, 0x86, 0x71, 0xff, 0xbf, 0x81, 0x6c, 
0xff, 0xc1, 0x83, 0x6e, 0xff, 0xc2, 0x86, 0x6e, 0xff, 0xc5, 0x87, 0x72, 0xff, 0xc3, 0x89, 
0x71, 0xff, 0xc6, 0x85, 0x6f, 0xff, 0xc6, 0x85, 0x71, 0xff, 0xb9, 0x79, 0x5e, 0xff, 0xb7, 
0x75, 0x5d, 0xff, 0xc1, 0x95, 0x88, 0xff, 0xe3, 0xe4, 0xe6, 0xff, 0xd2, 0xa4, 0x95, 0xff, 
0xbf, 0x70, 0x4f, 0xff, 0xba, 0x69, 0x3e, 0xff, 0xc1, 0x6f, 0x49, 0xff, 0xc3, 0x77, 0x53, 
0xff, 0xc6, 0x7d, 0x5c, 0xff, 0xc5, 0x7a, 0x5b, 0xff, 0xc7, 0x7c, 0x5d, 0xff, 0xc2, 0x77, 
0x58, 0xff, 0xbd, 0x74, 0x53, 0xff, 0xc6, 0x7d, 0x5c, 0xff, 0xc9, 0x83, 0x61, 0xff, 0xbd, 
0x76, 0x56, 0xff, 0xc0, 0x79, 0x5b, 0xff, 0xc8, 0x81, 0x65, 0xff, 0xc5, 0x7a, 0x5a, 0xff, 
0xc7, 0x7c, 0x5c, 0xff, 0xc9, 0x7e, 0x5e, 0xff, 0xc6, 0x7b, 0x5b, 0xff, 0xb7, 0x6c, 0x4c, 
0xff, 0xbf, 0x74, 0x54, 0xff, 0xc9, 0x7e, 0x5f, 0xff, 0xbd, 0x72, 0x53, 0xff, 0xcb, 0xa9, 
0x9d, 0xff, 0xe7, 0xe7, 0xe7, 0xff, 0xc2, 0x83, 0x72, 0xff, 0xcd, 0x8c, 0x7a, 0xff, 0xc6, 
0x8c, 0x74, 0xff, 0xc3, 0x8b, 0x70, 0xff, 0xc8, 0x87, 0x73, 0xff, 0xc4, 0x85, 0x73, 0xff, 
0xc8, 0x8f, 0x7c, 0xff, 0xb6, 0x7b, 0x69, 0xff, 0xc4, 0x87, 0x74, 0xff, 0xc6, 0x89, 0x76, 
0xff, 0xc7, 0x8a, 0x77, 0xff, 0xc4, 0x87, 0x72, 0xff, 0xc0, 0x83, 0x6e, 0xff, 0xc1, 0x84, 
0x6f, 0xff, 0xbf, 0x81, 0x6c, 0xff, 0xc0, 0x82, 0x6b, 0xff, 0xc4, 0x87, 0x72, 0xff, 0xc4, 
0x85, 0x73, 0xff, 0xbf, 0x85, 0x6f, 0xff, 0xc3, 0x82, 0x6e, 0xff, 0xc9, 0x88, 0x76, 0xff, 
0xc2, 0x82, 0x69, 0xff, 0xc0, 0x7e, 0x68, 0xff, 0xc4, 0x98, 0x8d, 0xff, 0xe5, 0xe9, 0xea, 
0xff, 0xd0, 0xa2, 0x95, 0xff, 0xb4, 0x65, 0x44, 0xff, 0xb9, 0x67, 0x3f, 0xff, 0xbc, 0x6c, 
0x47, 0xff, 0xc7, 0x7b, 0x59, 0xff, 0xcd, 0x84, 0x63, 0xff, 0xc2, 0x78, 0x5b, 0xff, 0xc0, 
0x77, 0x57, 0xff, 0xc3, 0x7a, 0x5a, 0xff, 0xba, 0x74, 0x52, 0xff, 0xc7, 0x81, 0x5d, 0xff, 
0xc4, 0x7e, 0x5a, 0xff, 0xc7, 0x83, 0x60, 0xff, 0x9f, 0x5a, 0x3b, 0xff, 0xb6, 0x71, 0x54, 
0xff, 0xca, 0x80, 0x5d, 0xff, 0xc8, 0x7e, 0x5b, 0xff, 0xc3, 0x79, 0x56, 0xff, 0xc4, 0x7a, 
0x57, 0xff, 0xc9, 0x7e, 0x5e, 0xff, 0xc9, 0x7e, 0x5e, 0xff, 0xbd, 0x72, 0x52, 0xff, 0xbf, 
0x74, 0x55, 0xff, 0xd0, 0xae, 0xa2, 0xff, 0xe8, 0xe8, 0xe8, 0xff, 0xc2, 0x8a, 0x79, 0xff, 
0xbf, 0x82, 0x6f, 0xff, 0xc1, 0x87, 0x6f, 0xff, 0xc3, 0x89, 0x71, 0xff, 0xc6, 0x85, 0x71, 
0xff, 0xc3, 0x86, 0x73, 0xff, 0xb5, 0x7e, 0x6a, 0xff, 0xc3, 0x88, 0x76, 0xff, 0xc3, 0x86, 
0x71, 0xff, 0xc2, 0x85, 0x70, 0xff, 0xc5, 0x88, 0x73, 0xff, 0xc2, 0x85, 0x70, 0xff, 0xc2, 
0x85, 0x70, 0xff, 0xc2, 0x85, 0x70, 0xff, 0xc0, 0x82, 0x6d, 0xff, 0xbf, 0x81, 0x6c, 0xff, 
0xc0, 0x81, 0x6f, 0xff, 0xc2, 0x83, 0x72, 0xff, 0xc1, 0x87, 0x71, 0xff, 0xc2, 0x81, 0x6d, 
0xff, 0xbf, 0x80, 0x6f, 0xff, 0xbd, 0x7c, 0x66, 0xff, 0xc1, 0x7e, 0x6b, 0xff, 0xc6, 0x9a, 
0x8f, 0xff, 0xe3, 0xe4, 0xe6, 0xff, 0xd1, 0xa5, 0x98, 0xff, 0xb8, 0x6c, 0x4c, 0xff, 0xb9, 
0x6a, 0x43, 0xff, 0xac, 0x5e, 0x3a, 0xff, 0xb5, 0x6a, 0x4a, 0xff, 0xc4, 0x7d, 0x5d, 0xff, 
0xc8, 0x7e, 0x61, 0xff, 0xc2, 0x79, 0x59, 0xff, 0xc3, 0x7a, 0x5a, 0xff, 0xb7, 0x71, 0x4f, 
0xff, 0xbf, 0x79, 0x57, 0xff, 0xc9, 0x83, 0x61, 0xff, 0xca, 0x84, 0x62, 0xff, 0xc6, 0x7f, 
0x5f, 0xff, 0xc2, 0x7d, 0x5e, 0xff, 0xbf, 0x75, 0x52, 0xff, 0xb9, 0x6f, 0x4c, 0xff, 0xcd, 
0x83, 0x60, 0xff, 0xc6, 0x7c, 0x59, 0xff, 0xc5, 0x7b, 0x58, 0xff, 0xc7, 0x7c, 0x5c, 0xff, 
0xc2, 0x77, 0x57, 0xff, 0xbf, 0x74, 0x54, 0xff, 0xce, 0xac, 0xa0, 0xff, 0xe4, 0xe4, 0xe4, 
0xff, 0xc9, 0x9c, 0x89, 0xff, 0xc1, 0x88, 0x75, 0xff, 0xc5, 0x88, 0x73, 0xff, 0xc4, 0x88, 
0x70, 0xff, 0xc1, 0x83, 0x6e, 0xff, 0xbd, 0x84, 0x70, 0xff, 0xa8, 0x74, 0x5f, 0xff, 0xb4, 
0x79, 0x67, 0xff, 0xc1, 0x84, 0x6f, 0xff, 0xbf, 0x82, 0x6d, 0xff, 0xbf, 0x82, 0x6d, 0xff, 
0xc1, 0x84, 0x6f, 0xff, 0xc1, 0x84, 0x6f, 0xff, 0xc2, 0x85, 0x70, 0xff, 0xbf, 0x81, 0x6c, 
0xff, 0xbf, 0x81, 0x6c, 0xff, 0xc6, 0x88, 0x73, 0xff, 0xc3, 0x84, 0x72, 0xff, 0xc0, 0x86, 
0x70, 0xff, 0xc2, 0x81, 0x6d, 0xff, 0xc0, 0x81, 0x6f, 0xff, 0xc0, 0x82, 0x6b, 0xff, 0xc4, 
0x83, 0x6f, 0xff, 0xc2, 0x98, 0x8c, 0xff, 0xe3, 0xe5, 0xe4, 0xff, 0xc8, 0x9c, 0x8f, 0xff, 
0xb8, 0x6f, 0x4f, 0xff, 0xc6, 0x7a, 0x56, 0xff, 0xc4, 0x79, 0x59, 0xff, 0xc1, 0x78, 0x58, 
0xff, 0xb9, 0x72, 0x52, 0xff, 0xc0, 0x75, 0x58, 0xff, 0xc5, 0x7c, 0x5c, 0xff, 0xca, 0x81, 
0x61, 0xff, 0xc0, 0x75, 0x56, 0xff, 0xc0, 0x75, 0x55, 0xff, 0xc9, 0x7e, 0x5e, 0xff, 0xc7, 
0x7e, 0x5d, 0xff, 0xc0, 0x77, 0x56, 0xff, 0xc0, 0x77, 0x56, 0xff, 0xc6, 0x7b, 0x5b, 0xff, 
0xbe, 0x74, 0x51, 0xff, 0xbd, 0x73, 0x50, 0xff, 0xc4, 0x7a, 0x57, 0xff, 0xc1, 0x77, 0x54, 
0xff, 0xc7, 0x7c, 0x5c, 0xff, 0xc4, 0x79, 0x59, 0xff, 0xca, 0x7f, 0x5f, 0xff, 0xc7, 0xa5, 
0x99, 0xff, 0xdf, 0xdf, 0xdf, 0xff, 0xcb, 0xa5, 0x92, 0xff, 0xbd, 0x85, 0x74, 0xff, 0xc6, 
0x88, 0x73, 0xff, 0xc2, 0x84, 0x6d, 0xff, 0xbe, 0x80, 0x6b, 0xff, 0xb9, 0x82, 0x6d, 0xff, 
0xb2, 0x7f, 0x6a, 0xff, 0xb4, 0x79, 0x67, 0xff, 0xc3, 0x87, 0x6f, 0xff, 0xc4, 0x88, 0x70, 
0xff, 0xc4, 0x87, 0x72, 0xff, 0xc7, 0x8a, 0x75, 0xff, 0xc1, 0x84, 0x6f, 0xff, 0xc4, 0x87, 
0x72, 0xff, 0xc1, 0x83, 0x6e, 0xff, 0xc6, 0x87, 0x75, 0xff, 0xc8, 0x8a, 0x73, 0xff, 0xc2, 
0x83, 0x71, 0xff, 0xbf, 0x85, 0x6d, 0xff, 0xc1, 0x80, 0x6a, 0xff, 0xc0, 0x81, 0x6f, 0xff, 
0xc3, 0x85, 0x6c, 0xff, 0xc5, 0x87, 0x70, 0xff, 0xbe, 0x97, 0x88, 0xff, 0xe3, 0xe5, 0xe4, 
0xff, 0xce, 0xa2, 0x95, 0xff, 0xc2, 0x78, 0x5b, 0xff, 0xbc, 0x72, 0x4f, 0xff, 0xbb, 0x72, 
0x52, 0xff, 0xbd, 0x76, 0x58, 0xff, 0xb8, 0x71, 0x51, 0xff, 0xc7, 0x7c, 0x5f, 0xff, 0xc6, 
0x7b, 0x5b, 0xff, 0xc6, 0x7b, 0x5c, 0xff, 0xc6, 0x79, 0x5b, 0xff, 0xc8, 0x7b, 0x5d, 0xff, 
0xc5, 0x78, 0x5a, 0xff, 0xc5, 0x79, 0x59, 0xff, 0xbf, 0x73, 0x53, 0xff, 0xc5, 0x79, 0x57, 
0xff, 0xc4, 0x79, 0x59, 0xff, 0xc5, 0x7b, 0x58, 0xff, 0xb6, 0x6c, 0x49, 0xff, 0xbf, 0x75, 
0x52, 0xff, 0xc9, 0x7f, 0x5c, 0xff, 0xbe, 0x73, 0x53, 0xff, 0xc6, 0x7b, 0x5b, 0xff, 0xc6, 
0x7b, 0x5b, 0xff, 0xc8, 0xa6, 0x9a, 0xff, 0xe2, 0xe2, 0xe2, 0xff, 0xc8, 0xa2, 0x8f, 0xff, 
0xc6, 0x87, 0x75, 0xff, 0xc9, 0x85, 0x6e, 0xff, 0xc2, 0x8a, 0x6f, 0xff, 0xc6, 0x89, 0x76, 
0xff, 0xc5, 0x83, 0x75, 0xff, 0xc1, 0x84, 0x71, 0xff, 0xc2, 0x8a, 0x6f, 0xff, 0xc0, 0x83, 
0x70, 0xff, 0xc1, 0x84, 0x71, 0xff, 0xbf, 0x82, 0x6d, 0xff, 0xbf, 0x81, 0x6c, 0xff, 0xc4, 
0x86, 0x71, 0xff, 0xc5, 0x87, 0x72, 0xff, 0xc1, 0x83, 0x6e, 0xff, 0xc0, 0x82, 0x6d, 0xff, 
0xc5, 0x87, 0x70, 0xff, 0xbe, 0x81, 0x6c, 0xff, 0xc1, 0x80, 0x6a, 0xff, 0xbf, 0x7e, 0x68, 
0xff, 0xbd, 0x7c, 0x66, 0xff, 0xc3, 0x7e, 0x6e, 0xff, 0xc5, 0x86, 0x77, 0xff, 0xbd, 0x94, 
0x82, 0xff, 0xe2, 0xe3, 0xdd, 0xff, 0xcb, 0x9f, 0x92, 0xff, 0xbe, 0x71, 0x55, 0xff, 0xbc, 
0x72, 0x4d, 0xff, 0xb8, 0x71, 0x51, 0xff, 0xc2, 0x75, 0x5b, 0xff, 0xc6, 0x77, 0x59, 0xff, 
0xbf, 0x7b, 0x54, 0xff, 0xb7, 0x6a, 0x4c, 0xff, 0xc6, 0x7b, 0x5c, 0xff, 0xc1, 0x76, 0x56, 
0xff, 0xb8, 0x6d, 0x4d, 0xff, 0xc3, 0x78, 0x58, 0xff, 0xbd, 0x72, 0x52, 0xff, 0xc3, 0x78, 
0x58, 0xff, 0xc7, 0x7c, 0x5c, 0xff, 0xc4, 0x7b, 0x5a, 0xff, 0xc7, 0x78, 0x59, 0xff, 0xc3, 
0x74, 0x55, 0xff, 0xc2, 0x7c, 0x5a, 0xff, 0xbe, 0x77, 0x57, 0xff, 0xc0, 0x73, 0x55, 0xff, 
0xcc, 0x7d, 0x5f, 0xff, 0xc2, 0x77, 0x5a, 0xff, 0xc6, 0xa4, 0x9b, 0xff, 0xe0, 0xe9, 0xe6, 
0xff, 0xc4, 0x9a, 0x8a, 0xff, 0xc1, 0x86, 0x74, 0xff, 0xc4, 0x88, 0x6e, 0xff, 0xc1, 0x89, 
0x70, 0xff, 0xc9, 0x87, 0x77, 0xff, 0xca, 0x86, 0x79, 0xff, 0xbf, 0x85, 0x71, 0xff, 0xb8, 
0x83, 0x64, 0xff, 0xb9, 0x7c, 0x69, 0xff, 0xbd, 0x80, 0x6d, 0xff, 0xbf, 0x82, 0x6d, 0xff, 
0xc2, 0x85, 0x70, 0xff, 0xc7, 0x89, 0x74, 0xff, 0xc6, 0x88, 0x73, 0xff, 0xc0, 0x82, 0x6d, 
0xff, 0xbe, 0x80, 0x6b, 0xff, 0xbb, 0x85, 0x69, 0xff, 0xbc, 0x7d, 0x6b, 0xff, 0xc0, 0x7b, 
0x6c, 0xff, 0xbd, 0x7e, 0x6c, 0xff, 0xbd, 0x85, 0x6c, 0xff, 0xc3, 0x81, 0x6b, 0xff, 0xbe, 
0x80, 0x71, 0xff, 0xbe, 0x99, 0x89, 0xff, 0xdc, 0xe5, 0xe4, 0xff, 0xc7, 0xa3, 0x93, 0xff, 
0xb9, 0x69, 0x50, 0xff, 0xc2, 0x67, 0x52, 0xff, 0xc2, 0x72, 0x59, 0xff, 0xbb, 0x76, 0x4f, 
0xff, 0xc2, 0x7d, 0x53, 0xff, 0xbd, 0x74, 0x53, 0xff, 0xb9, 0x70, 0x50, 0xff, 0xb8, 0x6f, 
0x4f, 0xff, 0xb6, 0x6d, 0x4d, 0xff, 0xb9, 0x70, 0x50, 0xff, 0xbb, 0x72, 0x51, 0xff, 0xb2, 
0x69, 0x48, 0xff, 0xbd, 0x74, 0x54, 0xff, 0xc2, 0x79, 0x59, 0xff, 0xbd, 0x79, 0x52, 0xff, 
0xb9, 0x72, 0x52, 0xff, 0xb6, 0x6c, 0x51, 0xff, 0xbe, 0x73, 0x54, 0xff, 0xc4, 0x7a, 0x53, 
0xff, 0xc1, 0x7a, 0x4e, 0xff, 0xbf, 0x77, 0x4f, 0xff, 0xbf, 0x74, 0x54, 0xff, 0xcb, 0xa4, 
0x9d, 0xff, 0xe0, 0xe0, 0xe0, 0xff, 0xca, 0x9b, 0x91, 0xff, 0xc4, 0x86, 0x79, 0xff, 0xc5, 
0x87, 0x70, 0xff, 0xbe, 0x89, 0x6a, 0xff, 0xbd, 0x85, 0x6a, 0xff, 0xbd, 0x85, 0x6e, 0xff, 
0xbf, 0x85, 0x71, 0xff, 0xc4, 0x81, 0x71, 0xff, 0xc3, 0x86, 0x73, 0xff, 0xc4, 0x87, 0x74, 
0xff, 0xc0, 0x83, 0x6e, 0xff, 0xbc, 0x7f, 0x6a, 0xff, 0xbd, 0x7f, 0x6a, 0xff, 0xbc, 0x7e, 
0x69, 0xff, 0xbb, 0x7d, 0x68, 0xff, 0xbb, 0x7d, 0x68, 0xff, 0xbb, 0x78, 0x68, 0xff, 0xbc, 
0x7d, 0x6c, 0xff, 0xc1, 0x84, 0x71, 0xff, 0xb8, 0x82, 0x66, 0xff, 0xb9, 0x86, 0x67, 0xff, 
0xc4, 0x83, 0x6d, 0xff, 0xc0, 0x82, 0x75, 0xff, 0xc1, 0x9b, 0x90, 0xff, 0xe2, 0xe8, 0xe6, 
0xff, 0xcb, 0xa5, 0x9a, 0xff, 0xbe, 0x71, 0x57, 0xff, 0xb1, 0x5f, 0x3a, 0xff, 0xb8, 0x71, 
0x47, 0xff, 0xbe, 0x76, 0x50, 0xff, 0xbf, 0x70, 0x52, 0xff, 0xc7, 0x75, 0x5f, 0xff, 0xc5, 
0x78, 0x5a, 0xff, 0xbd, 0x70, 0x52, 0xff, 0xbe, 0x71, 0x53, 0xff, 0xc0, 0x73, 0x55, 0xff, 
0xba, 0x6b, 0x4d, 0xff, 0xbd, 0x6e, 0x50, 0xff, 0xc5, 0x76, 0x57, 0xff, 0xb5, 0x66, 0x47, 
0xff, 0xc0, 0x75, 0x56, 0xff, 0xba, 0x71, 0x50, 0xff, 0xc1, 0x72, 0x51, 0xff, 0xc6, 0x71, 
0x52, 0xff, 0xc4, 0x73, 0x55, 0xff, 0xc4, 0x7e, 0x5c, 0xff, 0xc4, 0x7f, 0x58, 0xff, 0xc7, 
0x78, 0x51, 0xff, 0xd0, 0xae, 0xa2, 0xff, 0xe2, 0xe3, 0xde, 0xff, 0xc6, 0x9d, 0x87, 0xff, 
0xc4, 0x82, 0x74, 0xff, 0xc8, 0x82, 0x76, 0xff, 0xc3, 0x89, 0x73, 0xff, 0xc4, 0x88, 0x6c, 
0xff, 0xc4, 0x87, 0x6b, 0xff, 0xc4, 0x87, 0x72, 0xff, 0xc0, 0x85, 0x75, 0xff, 0xc6, 0x89, 
0x76, 0xff, 0xc6, 0x89, 0x76, 0xff, 0xc4, 0x87, 0x72, 0xff, 0xc1, 0x84, 0x6f, 0xff, 0xc1, 
0x84, 0x6f, 0xff, 0xc2, 0x84, 0x6f, 0xff, 0xc1, 0x83, 0x6e, 0xff, 0xc0, 0x82, 0x6d, 0xff, 
0xc0, 0x7f, 0x6d, 0xff, 0xb7, 0x7d, 0x69, 0xff, 0xc3, 0x86, 0x71, 0xff, 0xc4, 0x86, 0x6f, 
0xff, 0xc5, 0x89, 0x71, 0xff, 0xc9, 0x88, 0x76, 0xff, 0xc0, 0x86, 0x78, 0xff, 0xc5, 0xa0, 
0x8e, 0xff, 0xec, 0xe6, 0xea, 0xff, 0xd5, 0xac, 0xa6, 0xff, 0xbd, 0x73, 0x58, 0xff, 0xb4, 
0x5f, 0x36, 0xff, 0xbe, 0x71, 0x47, 0xff, 0xc6, 0x80, 0x5c, 0xff, 0xc7, 0x80, 0x62, 0xff, 
0xbe, 0x74, 0x57, 0xff, 0xbf, 0x74, 0x55, 0xff, 0xc3, 0x78, 0x59, 0xff, 0xc3, 0x78, 0x59, 
0xff, 0xc7, 0x7b, 0x5b, 0xff, 0xc3, 0x77, 0x57, 0xff, 0xbd, 0x6e, 0x4f, 0xff, 0xbf, 0x70, 
0x51, 0xff, 0xc5, 0x76, 0x55, 0xff, 0xcd, 0x73, 0x51, 0xff, 0xc6, 0x77, 0x58, 0xff, 0xc1, 
0x7a, 0x5c, 0xff, 0xc2, 0x75, 0x57, 0xff, 0xc9, 0x76, 0x58, 0xff, 0xc6, 0x76, 0x55, 0xff, 
0xbe, 0x75, 0x55, 0xff, 0xc1, 0x7c, 0x5f, 0xff, 0xcd, 0xac, 0xa5, 0xff, 0xe8, 0xe4, 0xe5, 
0xff, 0xc9, 0x9f, 0x86, 0xff, 0xc8, 0x87, 0x75, 0xff, 0xc9, 0x87, 0x77, 0xff, 0xbd, 0x8a, 
0x77, 0xff, 0xc0, 0x85, 0x75, 0xff, 0xc7, 0x7f, 0x70, 0xff, 0xc5, 0x84, 0x70, 0xff, 0xc1, 
0x89, 0x70, 0xff, 0xbf, 0x82, 0x6f, 0xff, 0xc0, 0x83, 0x70, 0xff, 0xc1, 0x84, 0x6f, 0xff, 
0xc2, 0x85, 0x70, 0xff, 0xc2, 0x85, 0x70, 0xff, 0xc3, 0x86, 0x71, 0xff, 0xc1, 0x84, 0x6f, 
0xff, 0xbf, 0x81, 0x6c, 0xff, 0xc1, 0x86, 0x64, 0xff, 0xbf, 0x81, 0x68, 0xff, 0xc6, 0x82, 
0x75, 0xff, 0xbe, 0x80, 0x73, 0xff, 0xc0, 0x89, 0x75, 0xff, 0xc3, 0x84, 0x73, 0xff, 0xb9, 
0x7b, 0x6c, 0xff, 0xbe, 0x98, 0x85, 0xff, 0xe9, 0xe9, 0xe7, 0xff, 0xca, 0xa1, 0x8b, 0xff, 
0xb7, 0x6f, 0x49, 0xff, 0xb9, 0x6d, 0x4b, 0xff, 0xb8, 0x71, 0x53, 0xff, 0xc0, 0x75, 0x56, 
0xff, 0xc1, 0x74, 0x56, 0xff, 0xb7, 0x6f, 0x57, 0xff, 0xb3, 0x6f, 0x4c, 0xff, 0xbc, 0x78, 
0x55, 0xff, 0xbe, 0x7a, 0x57, 0xff, 0xbb, 0x75, 0x53, 0xff, 0xba, 0x71, 0x50, 0xff, 0xbd, 
0x74, 0x51, 0xff, 0xbf, 0x75, 0x52, 0xff, 0xbb, 0x71, 0x4c, 0xff, 0xbc, 0x72, 0x4d, 0xff, 
0xc5, 0x76, 0x57, 0xff, 0xc4, 0x79, 0x5a, 0xff, 0xbe, 0x7a, 0x57, 0xff, 0xbc, 0x78, 0x51, 
0xff, 0xc1, 0x77, 0x52, 0xff, 0xc1, 0x74, 0x56, 0xff, 0xb6, 0x6c, 0x51, 0xff, 0xc1, 0xa9, 
0x9f, 0xff, 0xec, 0xe8, 0xe7, 0xff, 0xc1, 0xae, 0xa7, 0xff, 0xbf, 0x8b, 0x80, 0xff, 0xc0, 
0x82, 0x6d, 0xff, 0xbe, 0x86, 0x6b, 0xff, 0xc4, 0x8b, 0x77, 0xff, 0xc6, 0x88, 0x7b, 0xff, 
0xc5, 0x87, 0x78, 0xff, 0xca, 0x8d, 0x78, 0xff, 0xc7, 0x8a, 0x77, 0xff, 0xc5, 0x88, 0x75, 
0xff, 0xc3, 0x86, 0x71, 0xff, 0xc1, 0x84, 0x6f, 0xff, 0xbf, 0x82, 0x6d, 0xff, 0xc0, 0x83, 
0x6e, 0xff, 0xc1, 0x84, 0x6f, 0xff, 0xbe, 0x81, 0x6c, 0xff, 0xb5, 0x7c, 0x5e, 0xff, 0xc1, 
0x82, 0x70, 0xff, 0xc6, 0x87, 0x78, 0xff, 0xbc, 0x85, 0x70, 0xff, 0xc1, 0x89, 0x6e, 0xff, 
0xc0, 0x7c, 0x69, 0xff, 0xb3, 0x7c, 0x75, 0xff, 0xbc, 0xad, 0xa8, 0xff, 0xe6, 0xe5, 0xea, 
0xff, 0xca, 0xa8, 0x9c, 0xff, 0xbf, 0x7c, 0x5f, 0xff, 0xbc, 0x6d, 0x4e, 0xff, 0xb8, 0x6f, 
0x4e, 0xff, 0xb5, 0x75, 0x52, 0xff, 0xa6, 0x67, 0x48, 0xff, 0xb0, 0x72, 0x59, 0xff, 0xc1, 
0x76, 0x56, 0xff, 0xbc, 0x71, 0x51, 0xff, 0xbe, 0x73, 0x54, 0xff, 0xc0, 0x73, 0x55, 0xff, 
0xc1, 0x75, 0x55, 0xff, 0xc7, 0x78, 0x59, 0xff, 0xc3, 0x74, 0x53, 0xff, 0xb3, 0x65, 0x41, 
0xff, 0xb1, 0x6b, 0x51, 0xff, 0xc0, 0x71, 0x53, 0xff, 0xc4, 0x74, 0x4f, 0xff, 0xc2, 0x79, 
0x50, 0xff, 0xbf, 0x77, 0x51, 0xff, 0xc1, 0x72, 0x53, 0xff, 0xc2, 0x73, 0x55, 0xff, 0xb9, 
0x70, 0x50, 0xff, 0xbf, 0xb0, 0xa9, 0xff, 0xe8, 0xe3, 0xe7, 0xff, 0xdc, 0xe1, 0xe4, 0xff, 
0xcf, 0xbc, 0xb8, 0xff, 0xc5, 0x9b, 0x8d, 0xff, 0xbd, 0x83, 0x6f, 0xff, 0xc6, 0x84, 0x6e, 
0xff, 0xc3, 0x89, 0x75, 0xff, 0xbd, 0x8a, 0x77, 0xff, 0xc5, 0x8c, 0x7b, 0xff, 0xc2, 0x85, 
0x72, 0xff, 0xc1, 0x84, 0x71, 0xff, 0xc3, 0x86, 0x71, 0xff, 0xc2, 0x85, 0x70, 0xff, 0xbe, 
0x81, 0x6c, 0xff, 0xbe, 0x81, 0x6c, 0xff, 0xbe, 0x81, 0x6c, 0xff, 0xbb, 0x7e, 0x69, 0xff, 
0xbe, 0x86, 0x75, 0xff, 0xb7, 0x84, 0x6f, 0xff, 0xbc, 0x80, 0x68, 0xff, 0xbe, 0x7c, 0x66, 
0xff, 0xc0, 0x83, 0x70, 0xff, 0xc3, 0x91, 0x86, 0xff, 0xcd, 0xb1, 0xad, 0xff, 0xe0, 0xe7, 
0xe0, 0xff, 0xeb, 0xec, 0xf1, 0xff, 0xd8, 0xde, 0xda, 0xff, 0xc3, 0xaf, 0xa4, 0xff, 0xc1, 
0x7e, 0x6e, 0xff, 0xc1, 0x6b, 0x52, 0xff, 0xba, 0x71, 0x4e, 0xff, 0xb5, 0x74, 0x4c, 0xff, 
0xbc, 0x6c, 0x49, 0xff, 0xba, 0x6f, 0x52, 0xff, 0xbf, 0x74, 0x57, 0xff, 0xc4, 0x78, 0x5e, 
0xff, 0xbe, 0x71, 0x57, 0xff, 0xc2, 0x75, 0x59, 0xff, 0xc4, 0x77, 0x59, 0xff, 0xc6, 0x77, 
0x58, 0xff, 0xbe, 0x6f, 0x50, 0xff, 0xb8, 0x67, 0x4c, 0xff, 0xb4, 0x6d, 0x51, 0xff, 0xb3, 
0x72, 0x54, 0xff, 0xba, 0x73, 0x53, 0xff, 0xc8, 0x75, 0x55, 0xff, 0xc1, 0x71, 0x56, 0xff, 
0xba, 0x81, 0x6e, 0xff, 0xc4, 0xa4, 0x97, 0xff, 0xdc, 0xdb, 0xd6, 0xff, 0xe7, 0xe8, 0xec, 
0xff, 0xe5, 0xcd, 0xcd, 0xff, 0xe3, 0xe2, 0xe0, 0xff, 0xd6, 0xe1, 0xdd, 0xff, 0xc2, 0xaf, 
0xa8, 0xff, 0xc5, 0x8d, 0x7e, 0xff, 0xc7, 0x89, 0x70, 0xff, 0xc1, 0x88, 0x6d, 0xff, 0xc8, 
0x81, 0x6f, 0xff, 0xc1, 0x84, 0x71, 0xff, 0xc1, 0x84, 0x71, 0xff, 0xc5, 0x88, 0x73, 0xff, 
0xc4, 0x87, 0x72, 0xff, 0xc0, 0x83, 0x6e, 0xff, 0xc0, 0x83, 0x6e, 0xff, 0xc2, 0x85, 0x70, 
0xff, 0xbf, 0x82, 0x6d, 0xff, 0xbd, 0x74, 0x6b, 0xff, 0xb7, 0x81, 0x65, 0xff, 0xc2, 0x89, 
0x6b, 0xff, 0xc2, 0x84, 0x75, 0xff, 0xb9, 0x9c, 0x98, 0xff, 0xcc, 0xd1, 0xcd, 0xff, 0xe3, 
0xdf, 0xd6, 0xff, 0xe5, 0xc5, 0xba, 0xff, 0xd1, 0x98, 0x87, 0xff, 0xcb, 0xaa, 0x99, 0xff, 
0xdf, 0xdb, 0xd2, 0xff, 0xd3, 0xd5, 0xd2, 0xff, 0xbf, 0xa1, 0x97, 0xff, 0xc2, 0x7c, 0x63, 
0xff, 0xc1, 0x75, 0x55, 0xff, 0xb7, 0x77, 0x5b, 0xff, 0xb9, 0x7c, 0x5d, 0xff, 0x94, 0x57, 
0x3a, 0xff, 0xab, 0x6b, 0x4f, 0xff, 0xb9, 0x79, 0x5d, 0xff, 0xa6, 0x66, 0x4a, 0xff, 0xa3, 
0x64, 0x45, 0xff, 0xbb, 0x7c, 0x5b, 0xff, 0xb5, 0x76, 0x53, 0xff, 0xaa, 0x6d, 0x51, 0xff, 
0x9d, 0x60, 0x41, 0xff, 0xbb, 0x76, 0x57, 0xff, 0xbe, 0x74, 0x59, 0xff, 0xa6, 0x68, 0x53, 
0xff, 0xb7, 0x96, 0x87, 0xff, 0xd1, 0xc8, 0xc1, 0xff, 0xd5, 0xd5, 0xd5, 0xff, 0xd3, 0xae, 
0x9e, 0xff, 0xbc, 0x95, 0x90, 0xff, 0xcf, 0x88, 0x68, 0xff, 0xcd, 0x99, 0x8b, 0xff, 0xe3, 
0xcf, 0xd0, 0xff, 0xe4, 0xe4, 0xe6, 0xff, 0xd4, 0xcc, 0xc9, 0xff, 0xc6, 0xa6, 0x9b, 0xff, 
0xb9, 0x84, 0x74, 0xff, 0xc4, 0x85, 0x73, 0xff, 0xc4, 0x86, 0x6f, 0xff, 0xc5, 0x85, 0x6c, 
0xff, 0xbb, 0x7e, 0x6b, 0xff, 0xc7, 0x88, 0x79, 0xff, 0xc4, 0x86, 0x77, 0xff, 0xbd, 0x82, 
0x70, 0xff, 0xbe, 0x84, 0x6e, 0xff, 0xb8, 0x7f, 0x6b, 0xff, 0xc0, 0x7f, 0x6b, 0xff, 0xc3, 
0x85, 0x70, 0xff, 0xba, 0x90, 0x80, 0xff, 0xcc, 0xc1, 0xbd, 0xff, 0xde, 0xd6, 0xd4, 0xff, 
0xd7, 0xb5, 0xa9, 0xff, 0xc8, 0x8c, 0x72, 0xff, 0xd1, 0x8a, 0x6a, 0xff, 0xc0, 0x81, 0x5e, 
0xff, 0xd7, 0x8b, 0x69, 0xff, 0xdb, 0x95, 0x7c, 0xff, 0xd6, 0xb8, 0xad, 0xff, 0xdc, 0xe0, 
0xdf, 0xff, 0xcd, 0xca, 0xc5, 0xff, 0xc0, 0x97, 0x85, 0xff, 0xb8, 0x71, 0x51, 0xff, 0xae, 
0x6c, 0x4c, 0xff, 0xbc, 0x75, 0x57, 0xff, 0xc4, 0x78, 0x56, 0xff, 0xc4, 0x74, 0x4f, 0xff, 
0xad, 0x70, 0x54, 0xff, 0xaa, 0x70, 0x5c, 0xff, 0xb6, 0x77, 0x56, 0xff, 0x82, 0x59, 0x3b, 
0xff, 0x9e, 0x62, 0x48, 0xff, 0xc1, 0x75, 0x5d, 0xff, 0xbd, 0x6f, 0x58, 0xff, 0xbf, 0x90, 
0x7e, 0xff, 0xcf, 0xc4, 0xbe, 0xff, 0xda, 0xd6, 0xd7, 0xff, 0xd7, 0xb5, 0xac, 0xff, 0xc9, 
0x86, 0x6c, 0xff, 0xc7, 0x84, 0x67, 0xff, 0xc6, 0x83, 0x68, 0xff, 0xcb, 0x87, 0x72, 0xff, 
0xd2, 0x87, 0x67, 0xff, 0xc9, 0x83, 0x5f, 0xff, 0xd0, 0xa2, 0x8a, 0xff, 0xdd, 0xd0, 0xc7, 
0xff, 0xdd, 0xdf, 0xda, 0xff, 0xcf, 0xc1, 0xbe, 0xff, 0xb0, 0x8d, 0x89, 0xff, 0xbd, 0x84, 
0x71, 0xff, 0xb9, 0x7f, 0x6b, 0xff, 0xbb, 0x81, 0x6d, 0xff, 0xb7, 0x7d, 0x67, 0xff, 0xba, 
0x7e, 0x66, 0xff, 0xbe, 0x82, 0x6a, 0xff, 0xbe, 0x80, 0x69, 0xff, 0xbd, 0x83, 0x6b, 0xff, 
0xbe, 0x8a, 0x7f, 0xff, 0xbc, 0xae, 0xab, 0xff, 0xcf, 0xd4, 0xce, 0xff, 0xda, 0xc5, 0xb2, 
0xff, 0xc1, 0x8f, 0x74, 0xff, 0xaf, 0x77, 0x5e, 0xff, 0xbc, 0x7f, 0x6a, 0xff, 0xce, 0x86, 
0x6e, 0xff, 0xcc, 0x81, 0x64, 0xff, 0xc1, 0x80, 0x62, 0xff, 0xce, 0x89, 0x68, 0xff, 0xd8, 
0x89, 0x6b, 0xff, 0xd3, 0x95, 0x80, 0xff, 0xd7, 0xbf, 0xb3, 0xff, 0xe0, 0xdb, 0xd7, 0xff, 
0xc1, 0xb6, 0xb4, 0xff, 0xb7, 0x81, 0x69, 0xff, 0xc3, 0x79, 0x54, 0xff, 0xc1, 0x75, 0x53, 
0xff, 0xbb, 0x71, 0x4c, 0xff, 0xc3, 0x77, 0x57, 0xff, 0xb5, 0x71, 0x5a, 0xff, 0xb0, 0x74, 
0x58, 0xff, 0xbe, 0x7c, 0x5c, 0xff, 0xbd, 0x7c, 0x5c, 0xff, 0xaf, 0x8a, 0x70, 0xff, 0xba, 
0xb3, 0xad, 0xff, 0xd2, 0xd1, 0xd6, 0xff, 0xd6, 0xbb, 0xb4, 0xff, 0xc9, 0x8e, 0x70, 0xff, 
0xd1, 0x88, 0x67, 0xff, 0xc7, 0x80, 0x6a, 0xff, 0xcb, 0x88, 0x6b, 0xff, 0xcb, 0x88, 0x6b, 
0xff, 0xcf, 0x83, 0x63, 0xff, 0xd2, 0x90, 0x70, 0xff, 0xc6, 0x7f, 0x5f, 0xff, 0xcb, 0x78, 
0x56, 0xff, 0xcc, 0x82, 0x67, 0xff, 0xce, 0xa7, 0x96, 0xff, 0xe0, 0xd7, 0xd0, 0xff, 0xd8, 
0xda, 0xd7, 0xff, 0xbe, 0xb0, 0xaf, 0xff, 0xb3, 0x84, 0x7c, 0xff, 0xbd, 0x7a, 0x69, 0xff, 
0xb7, 0x7a, 0x67, 0xff, 0xbf, 0x81, 0x6a, 0xff, 0xca, 0x7f, 0x68, 0xff, 0xbe, 0x86, 0x77, 
0xff, 0xbe, 0xa5, 0x9e, 0xff, 0xcc, 0xd2, 0xd0, 0xff, 0xdb, 0xc3, 0xbf, 0xff, 0xc4, 0x8c, 
0x7f, 0xff, 0xc5, 0x7a, 0x63, 0xff, 0xcb, 0x7e, 0x64, 0xff, 0xce, 0x82, 0x6b, 0xff, 0xc5, 
0x7d, 0x64, 0xff, 0xc5, 0x83, 0x61, 0xff, 0xd6, 0x8b, 0x6c, 0xff, 0xd5, 0x81, 0x65, 0xff, 
0xd3, 0x82, 0x65, 0xff, 0xce, 0x85, 0x64, 0xff, 0xcf, 0x86, 0x5d, 0xff, 0xd4, 0x8a, 0x65, 
0xff, 0xd3, 0xa4, 0x94, 0xff, 0xdd, 0xd1, 0xd5, 0xff, 0xda, 0xd1, 0xd6, 0xff, 0xc5, 0xa3, 
0x97, 0xff, 0xb9, 0x7b, 0x66, 0xff, 0xbe, 0x7a, 0x57, 0xff, 0xbf, 0x7b, 0x58, 0xff, 0xc9, 
0x7b, 0x65, 0xff, 0xbe, 0x74, 0x57, 0xff, 0xc5, 0x82, 0x65, 0xff, 0xc3, 0xb3, 0xa4, 0xff, 
0xdb, 0xd8, 0xd1, 0xff, 0xd8, 0xca, 0xc1, 0xff, 0xcf, 0x9b, 0x86, 0xff, 0xcb, 0x81, 0x66, 
0xff, 0xc4, 0x81, 0x67, 0xff, 0xc3, 0x81, 0x67, 0xff, 0xce, 0x82, 0x62, 0xff, 0xc8, 0x83, 
0x64, 0xff, 0xc9, 0x84, 0x67, 0xff, 0xcf, 0x83, 0x69, 0xff, 0xca, 0x7d, 0x5f, 0xff, 0xcf, 
0x86, 0x66, 0xff, 0xc6, 0x7f, 0x61, 0xff, 0xcc, 0x7b, 0x5d, 0xff, 0xd1, 0x7a, 0x5e, 0xff, 
0xc6, 0x85, 0x6f, 0xff, 0xd3, 0xb6, 0xa6, 0xff, 0xd8, 0xdd, 0xe1, 0xff, 0xd3, 0xd4, 0xd9, 
0xff, 0xc3, 0xa8, 0x9f, 0xff, 0xb8, 0x8a, 0x73, 0xff, 0xbb, 0x89, 0x72, 0xff, 0xbf, 0x9f, 
0x94, 0xff, 0xd3, 0xca, 0xcb, 0xff, 0xd9, 0xd4, 0xd1, 0xff, 0xce, 0x9e, 0x94, 0xff, 0xc2, 
0x81, 0x6d, 0xff, 0xc4, 0x78, 0x5e, 0xff, 0xbd, 0x72, 0x5b, 0xff, 0xc2, 0x7a, 0x61, 0xff, 
0xc6, 0x78, 0x54, 0xff, 0xce, 0x7b, 0x59, 0xff, 0xce, 0x7e, 0x63, 0xff, 0xc2, 0x80, 0x60, 
0xff, 0xcc, 0x87, 0x6a, 0xff, 0xd0, 0x86, 0x69, 0xff, 0xd6, 0x87, 0x69, 0xff, 0xcd, 0x7e, 
0x60, 0xff, 0xce, 0x87, 0x6b, 0xff, 0xc4, 0x7d, 0x5f, 0xff, 0xca, 0x7f, 0x5f, 0xff, 0xd6, 
0xaf, 0x9e, 0xff, 0xdc, 0xd9, 0xd0, 0xff, 0xd8, 0xcd, 0xd5, 0xff, 0xc3, 0x92, 0x8d, 0xff, 
0xbf, 0x7c, 0x62, 0xff, 0xb0, 0x75, 0x57, 0xff, 0xbc, 0xa0, 0x8a, 0xff, 0xd4, 0xcc, 0xd9, 
0xff, 0xdc, 0xd7, 0xd4, 0xff, 0xca, 0x9f, 0x8f, 0xff, 0xc7, 0x7d, 0x64, 0xff, 0xc3, 0x79, 
0x60, 0xff, 0xca, 0x83, 0x6d, 0xff, 0xca, 0x7e, 0x64, 0xff, 0xca, 0x80, 0x63, 0xff, 0xbd, 
0x81, 0x65, 0xff, 0xc6, 0x81, 0x62, 0xff, 0xc9, 0x82, 0x64, 0xff, 0xcd, 0x83, 0x60, 0xff, 
0xd2, 0x85, 0x69, 0xff, 0xd2, 0x83, 0x65, 0xff, 0xd1, 0x83, 0x5f, 0xff, 0xcb, 0x7f, 0x5f, 
0xff, 0xc9, 0x7d, 0x65, 0xff, 0xce, 0x81, 0x67, 0xff, 0xca, 0x7a, 0x57, 0xff, 0xd0, 0x90, 
0x77, 0xff, 0xe4, 0xd2, 0xc6, 0xff, 0xe4, 0xe5, 0xe0, 0xff, 0xd2, 0xd5, 0xdc, 0xff, 0xd2, 
0xd5, 0xdc, 0xff, 0xe3, 0xe4, 0xdf, 0xff, 0xd6, 0xc2, 0xb9, 0xff, 0xbf, 0x7d, 0x65, 0xff, 
0xc2, 0x7e, 0x57, 0xff, 0xc0, 0x76, 0x5b, 0xff, 0xbf, 0x6e, 0x5b, 0xff, 0xc4, 0x73, 0x5e, 
0xff, 0xc5, 0x7e, 0x60, 0xff, 0xbb, 0x7d, 0x56, 0xff, 0xc9, 0x89, 0x65, 0xff, 0xcf, 0x84, 
0x65, 0xff, 0xcc, 0x7f, 0x63, 0xff, 0xc1, 0x83, 0x6a, 0xff, 0xa2, 0x70, 0x57, 0xff, 0xbc, 
0x84, 0x69, 0xff, 0xd6, 0x8e, 0x75, 0xff, 0xd7, 0x87, 0x70, 0xff, 0xd2, 0x87, 0x70, 0xff, 
0xca, 0x8a, 0x6f, 0xff, 0xcf, 0x87, 0x71, 0xff, 0xc8, 0x91, 0x73, 0xff, 0xdc, 0xcb, 0xbb, 
0xff, 0xda, 0xdc, 0xd1, 0xff, 0xc5, 0xc0, 0xba, 0xff, 0xbd, 0xc8, 0xc2, 0xff, 0xdc, 0xe2, 
0xd8, 0xff, 0xd8, 0xae, 0xaf, 0xff, 0xc7, 0x83, 0x70, 0xff, 0xca, 0x81, 0x6e, 0xff, 0xc6, 
0x7e, 0x65, 0xff, 0xca, 0x85, 0x66, 0xff, 0xbb, 0x79, 0x5f, 0xff, 0xc7, 0x84, 0x71, 0xff, 
0xc5, 0x7f, 0x66, 0xff, 0xcb, 0x82, 0x61, 0xff, 0xc9, 0x82, 0x64, 0xff, 0xc8, 0x81, 0x63, 
0xff, 0xc8, 0x81, 0x65, 0xff, 0xcb, 0x82, 0x61, 0xff, 0xcc, 0x83, 0x62, 0xff, 0xc9, 0x7e, 
0x61, 0xff, 0xcc, 0x7f, 0x61, 0xff, 0xcc, 0x7e, 0x58, 0xff, 0xc6, 0x7b, 0x54, 0xff, 0xce, 
0x85, 0x62, 0xff, 0xcc, 0x7d, 0x5f, 0xff, 0xc4, 0x7e, 0x64, 0xff, 0xd5, 0xa2, 0x8d, 0xff, 
0xe9, 0xe7, 0xec, 0xff, 0xe5, 0xe3, 0xe8, 0xff, 0xd0, 0xa0, 0x8c, 0xff, 0xb8, 0x76, 0x60, 
0xff, 0xca, 0x80, 0x67, 0xff, 0xc6, 0x7b, 0x5e, 0xff, 0xc5, 0x7d, 0x64, 0xff, 0xbc, 0x79, 
0x5e, 0xff, 0xbf, 0x7e, 0x5e, 0xff, 0xbd, 0x77, 0x5d, 0xff, 0xcc, 0x81, 0x6e, 0xff, 0xc6, 
0x7a, 0x62, 0xff, 0xcc, 0x82, 0x5d, 0xff, 0xcd, 0x7a, 0x6a, 0xff, 0xca, 0x80, 0x63, 0xff, 
0xc8, 0x89, 0x66, 0xff, 0xb8, 0x7d, 0x5f, 0xff, 0xc1, 0x84, 0x68, 0xff, 0xca, 0x89, 0x6b, 
0xff, 0xca, 0x87, 0x6a, 0xff, 0xc8, 0x81, 0x6b, 0xff, 0xd0, 0x80, 0x69, 0xff, 0xd4, 0x83, 
0x68, 0xff, 0xd0, 0x82, 0x6c, 0xff, 0xda, 0xa6, 0x98, 0xff, 0xe1, 0xe1, 0xdf, 0xff, 0xe4, 
0xd9, 0xdf, 0xff, 0xce, 0x8f, 0x7e, 0xff, 0xc6, 0x86, 0x62, 0xff, 0xca, 0x80, 0x67, 0xff, 
0xc9, 0x84, 0x67, 0xff, 0xc9, 0x82, 0x64, 0xff, 0xca, 0x83, 0x67, 0xff, 0xc6, 0x84, 0x6a, 
0xff, 0xc7, 0x8b, 0x6f, 0xff, 0xc6, 0x83, 0x66, 0xff, 0xcc, 0x7d, 0x5f, 0xff, 0xcf, 0x85, 
0x68, 0xff, 0xce, 0x85, 0x65, 0xff, 0xd0, 0x7f, 0x6a, 0xff, 0xc8, 0x85, 0x6a, 0xff, 0xcb, 
0x8b, 0x68, 0xff, 0xce, 0x85, 0x62, 0xff, 0xc9, 0x80, 0x5d, 0xff, 0xc7, 0x82, 0x61, 0xff, 
0xcb, 0x82, 0x61, 0xff, 0xcd, 0x79, 0x54, 0xff, 0xc4, 0x83, 0x59, 0xff, 0xd1, 0x81, 0x66, 
0xff, 0xc5, 0x7b, 0x60, 0xff, 0xda, 0xca, 0xbd, 0xff, 0xdd, 0xcf, 0xc4, 0xff, 0xca, 0x82, 
0x6a, 0xff, 0xcf, 0x83, 0x6b, 0xff, 0xc7, 0x89, 0x62, 0xff, 0xc6, 0x7a, 0x63, 0xff, 0xca, 
0x84, 0x6a, 0xff, 0xc4, 0x81, 0x64, 0xff, 0xc5, 0x7b, 0x60, 0xff, 0xce, 0x81, 0x67, 0xff, 
0xc5, 0x78, 0x5c, 0xff, 0xc9, 0x7c, 0x60, 0xff, 0xcd, 0x7b, 0x63, 0xff, 0xc8, 0x83, 0x5a, 
0xff, 0xd0, 0x84, 0x6c, 0xff, 0xcc, 0x7f, 0x6b, 0xff, 0xce, 0x85, 0x65, 0xff, 0xd0, 0x86, 
0x5f, 0xff, 0xc8, 0x7a, 0x56, 0xff, 0xd6, 0x8d, 0x6c, 0xff, 0xc9, 0x89, 0x65, 0xff, 0xc6, 
0x86, 0x62, 0xff, 0xcf, 0x7e, 0x69, 0xff, 0xc2, 0x80, 0x5d, 0xff, 0xbd, 0x81, 0x5f, 0xff, 
0xdd, 0xc5, 0xc5, 0xff, 0xd9, 0xcc, 0xc4, 0xff, 0xc3, 0x87, 0x63, 0xff, 0xc7, 0x84, 0x67, 
0xff, 0xc6, 0x84, 0x61, 0xff, 0xc9, 0x88, 0x68, 0xff, 0xc0, 0x7e, 0x66, 0xff, 0xcd, 0x86, 
0x74, 0xff, 0xc9, 0x81, 0x6b, 0xff, 0xc9, 0x82, 0x62, 0xff, 0xbf, 0x78, 0x58, 0xff, 0xca, 
0x80, 0x63, 0xff, 0xcb, 0x81, 0x64, 0xff, 0xcb, 0x80, 0x61, 0xff, 0xc7, 0x85, 0x62, 0xff, 
0xd7, 0x88, 0x6a, 0xff, 0xd8, 0x82, 0x69, 0xff, 0xcc, 0x7f, 0x63, 0xff, 0xca, 0x81, 0x61, 
0xff, 0xd3, 0x88, 0x69, 0xff, 0xd5, 0x88, 0x6c, 0xff, 0xc6, 0x7b, 0x64, 0xff, 0xc9, 0x7f, 
0x5c, 0xff, 0xcd, 0x7d, 0x64, 0xff, 0xc2, 0x7a, 0x61, 0xff, 0xcd, 0xbe, 0xb9, 0xff, 0xd3, 
0xc6, 0xc0, 0xff, 0xc1, 0x7b, 0x61, 0xff, 0xca, 0x7d, 0x63, 0xff, 0xcc, 0x83, 0x60, 0xff, 
0xc8, 0x81, 0x61, 0xff, 0xce, 0x85, 0x65, 0xff, 0xd1, 0x88, 0x67, 0xff, 0xc8, 0x7f, 0x5e, 
0xff, 0xd2, 0x87, 0x67, 0xff, 0xcc, 0x80, 0x60, 0xff, 0xcb, 0x84, 0x64, 0xff, 0xc6, 0x87, 
0x68, 0xff, 0xcf, 0x88, 0x6a, 0xff, 0xc9, 0x85, 0x62, 0xff, 0xce, 0x88, 0x64, 0xff, 0xd0, 
0x85, 0x66, 0xff, 0xca, 0x7e, 0x64, 0xff, 0xcf, 0x88, 0x6a, 0xff, 0xca, 0x85, 0x66, 0xff, 
0xcb, 0x84, 0x68, 0xff, 0xcb, 0x8c, 0x6b, 0xff, 0xcb, 0x80, 0x69, 0xff, 0xc2, 0x81, 0x65, 
0xff, 0xc4, 0x82, 0x6c, 0xff, 0xdb, 0xc1, 0xc0, 0xff, 0xd6, 0xc3, 0xbf, 0xff, 0xd0, 0x7b, 
0x66, 0xff, 0xca, 0x85, 0x68, 0xff, 0xce, 0x86, 0x70, 0xff, 0xcc, 0x86, 0x6c, 0xff, 0xc2, 
0x80, 0x60, 0xff, 0xc6, 0x81, 0x62, 0xff, 0xcc, 0x7e, 0x67, 0xff, 0xcd, 0x7a, 0x68, 0xff, 
0xc7, 0x7b, 0x64, 0xff, 0xc0, 0x81, 0x62, 0xff, 0xcc, 0x81, 0x64, 0xff, 0xcb, 0x80, 0x63, 
0xff, 0xd0, 0x80, 0x65, 0xff, 0xce, 0x7f, 0x61, 0xff, 0xce, 0x7f, 0x61, 0xff, 0xcf, 0x83, 
0x63, 0xff, 0xce, 0x82, 0x62, 0xff, 0xcf, 0x84, 0x64, 0xff, 0xcb, 0x82, 0x62, 0xff, 0xc8, 
0x7f, 0x5f, 0xff, 0xd2, 0x89, 0x69, 0xff, 0xd4, 0x80, 0x66, 0xff, 0xc4, 0x7e, 0x65, 0xff, 
0xcd, 0xbe, 0xb9, 0xff, 0xd1, 0xc0, 0xb9, 0xff, 0xd1, 0x82, 0x64, 0xff, 0xc8, 0x81, 0x61, 
0xff, 0xce, 0x81, 0x65, 0xff, 0xcc, 0x85, 0x65, 0xff, 0xca, 0x81, 0x61, 0xff, 0xc8, 0x7f, 
0x5f, 0xff, 0xcc, 0x83, 0x63, 0xff, 0xcc, 0x83, 0x63, 0xff, 0xc6, 0x7d, 0x5d, 0xff, 0xc8, 
0x7e, 0x61, 0xff, 0xc9, 0x82, 0x64, 0xff, 0xca, 0x83, 0x65, 0xff, 0xc8, 0x81, 0x63, 0xff, 
0xc3, 0x7c, 0x5e, 0xff, 0xcc, 0x85, 0x65, 0xff, 0xcf, 0x86, 0x66, 0xff, 0xcd, 0x84, 0x64, 
0xff, 0xcf, 0x86, 0x66, 0xff, 0xd3, 0x8a, 0x6a, 0xff, 0xca, 0x85, 0x64, 0xff, 0xcc, 0x81, 
0x64, 0xff, 0xcd, 0x84, 0x64, 0xff, 0xcb, 0x86, 0x65, 0xff, 0xcb, 0xbd, 0xba, 0xff, 0xd5, 
0xc2, 0xbb, 0xff, 0xc9, 0x81, 0x69, 0xff, 0xcf, 0x8a, 0x6d, 0xff, 0xd2, 0x8f, 0x74, 0xff, 
0xc5, 0x7f, 0x65, 0xff, 0xc4, 0x7e, 0x65, 0xff, 0xc9, 0x81, 0x69, 0xff, 0xca, 0x7f, 0x68, 
0xff, 0xc9, 0x7f, 0x66, 0xff, 0xc7, 0x7d, 0x62, 0xff, 0xc7, 0x7d, 0x62, 0xff, 0xce, 0x82, 
0x62, 0xff, 0xd1, 0x85, 0x65, 0xff, 0xcc, 0x81, 0x64, 0xff, 0xd0, 0x83, 0x65, 0xff, 0xce, 
0x81, 0x63, 0xff, 0xd1, 0x85, 0x65, 0xff, 0xce, 0x82, 0x62, 0xff, 0xd3, 0x86, 0x68, 0xff, 
0xcd, 0x80, 0x62, 0xff, 0xcb, 0x7e, 0x62, 0xff, 0xce, 0x81, 0x65, 0xff, 0xcc, 0x82, 0x5f, 
0xff, 0xbe, 0x79, 0x58, 0xff, 0xd2, 0xbd, 0xbc, 0xff, 0xd8, 0xcf, 0xca, 0xff, 0xcc, 0x87, 
0x66, 0xff, 0xcc, 0x8a, 0x67, 0xff, 0xc5, 0x7b, 0x62, 0xff, 0xca, 0x83, 0x63, 0xff, 0xcb, 
0x82, 0x62, 0xff, 0xc8, 0x7f, 0x5f, 0xff, 0xca, 0x81, 0x61, 0xff, 0xcb, 0x82, 0x62, 0xff, 
0xc9, 0x80, 0x60, 0xff, 0xcb, 0x81, 0x64, 0xff, 0xca, 0x83, 0x65, 0xff, 0xc9, 0x82, 0x64, 
0xff, 0xc4, 0x7d, 0x5f, 0xff, 0xd3, 0x89, 0x6c, 0xff, 0xd2, 0x89, 0x69, 0xff, 0xcc, 0x83, 
0x63, 0xff, 0xd0, 0x87, 0x67, 0xff, 0xcc, 0x83, 0x63, 0xff, 0xc6, 0x7d, 0x5d, 0xff, 0xc5, 
0x7f, 0x5d, 0xff, 0xc9, 0x7c, 0x5e, 0xff, 0xcb, 0x80, 0x61, 0xff, 0xc6, 0x81, 0x60, 0xff, 
0xce, 0xc0, 0xbd, 0xff, 0xd8, 0xc5, 0xbe, 0xff, 0xcb, 0x83, 0x6b, 0xff, 0xce, 0x89, 0x6c, 
0xff, 0xcb, 0x85, 0x6b, 0xff, 0xc7, 0x81, 0x67, 0xff, 0xce, 0x86, 0x6d, 0xff, 0xd1, 0x87, 
0x6e, 0xff, 0xcd, 0x83, 0x6a, 0xff, 0xcb, 0x81, 0x66, 0xff, 0xc8, 0x7e, 0x63, 0xff, 0xc9, 
0x7f, 0x62, 0xff, 0xca, 0x7f, 0x60, 0xff, 0xca, 0x7f, 0x60, 0xff, 0xcb, 0x82, 0x62, 0xff, 
0xc7, 0x7e, 0x5e, 0xff, 0xd0, 0x85, 0x66, 0xff, 0xcc, 0x7f, 0x61, 0xff, 0xc9, 0x7c, 0x5e, 
0xff, 0xce, 0x81, 0x65, 0xff, 0xc2, 0x75, 0x59, 0xff, 0xcb, 0x7e, 0x64, 0xff, 0xd0, 0x80, 
0x67, 0xff, 0xcc, 0x83, 0x62, 0xff, 0xc4, 0x7f, 0x60, 0xff, 0xd0, 0xb7, 0xbb, 0xff, 0xd5, 
0xca, 0xc6, 0xff, 0xd2, 0x86, 0x64, 0xff, 0xd0, 0x82, 0x5e, 0xff, 0xcd, 0x85, 0x6d, 0xff, 
0xc6, 0x7f, 0x5f, 0xff, 0xc9, 0x80, 0x60, 0xff, 0xc7, 0x7e, 0x5e, 0xff, 0xc7, 0x7e, 0x5e, 
0xff, 0xc8, 0x7f, 0x5f, 0xff, 0xca, 0x81, 0x61, 0xff, 0xcd, 0x83, 0x66, 0xff, 0xca, 0x83, 
0x65, 0xff, 0xc8, 0x7f, 0x5f, 0xff, 0xcd, 0x84, 0x64, 0xff, 0xcb, 0x82, 0x62, 0xff, 0xc3, 
0x7a, 0x5a, 0xff, 0xc8, 0x7f, 0x5f, 0xff, 0xc5, 0x7c, 0x5c, 0xff, 0xc7, 0x7e, 0x5e, 0xff, 
0xc6, 0x7d, 0x5d, 0xff, 0xc5, 0x7f, 0x5d, 0xff, 0xca, 0x7d, 0x5f, 0xff, 0xcb, 0x80, 0x61, 
0xff, 0xc3, 0x7e, 0x5d, 0xff, 0xd3, 0xc6, 0xc0, 0xff, 0xd9, 0xc5, 0xbe, 0xff, 0xca, 0x82, 
0x6a, 0xff, 0xc9, 0x84, 0x67, 0xff, 0xc7, 0x7f, 0x66, 0xff, 0xc6, 0x7e, 0x65, 0xff, 0xc8, 
0x7e, 0x65, 0xff, 0xc4, 0x7a, 0x61, 0xff, 0xc4, 0x78, 0x5e, 0xff, 0xc8, 0x7d, 0x60, 0xff, 
0xcb, 0x80, 0x63, 0xff, 0xcf, 0x84, 0x65, 0xff, 0xd2, 0x88, 0x6b, 0xff, 0xd3, 0x89, 0x6c, 
0xff, 0xce, 0x85, 0x65, 0xff, 0xd1, 0x88, 0x68, 0xff, 0xcd, 0x83, 0x66, 0xff, 0xca, 0x80, 
0x63, 0xff, 0xcc, 0x82, 0x65, 0xff, 0xcb, 0x81, 0x66, 0xff, 0xcb, 0x81, 0x66, 0xff, 0xc9, 
0x7f, 0x66, 0xff, 0xb9, 0x7c, 0x5f, 0xff, 0xbe, 0x81, 0x65, 0xff, 0xac, 0x74, 0x5d, 0xff, 
0xcc, 0xc3, 0xc4, 0xff, 0xd5, 0xd3, 0xc7, 0xff, 0xd2, 0x86, 0x62, 0xff, 0xcd, 0x83, 0x60, 
0xff, 0xc0, 0x80, 0x64, 0xff, 0xc9, 0x82, 0x62, 0xff, 0xcc, 0x83, 0x63, 0xff, 0xc9, 0x80, 
0x60, 0xff, 0xc9, 0x80, 0x60, 0xff, 0xcc, 0x83, 0x63, 0xff, 0xce, 0x85, 0x65, 0xff, 0xd0, 
0x87, 0x67, 0xff, 0xcb, 0x84, 0x66, 0xff, 0xcd, 0x84, 0x64, 0xff, 0xcc, 0x83, 0x63, 0xff, 
0xca, 0x81, 0x61, 0xff, 0xcc, 0x83, 0x63, 0xff, 0xc9, 0x80, 0x60, 0xff, 0xc3, 0x7a, 0x5a, 
0xff, 0xc9, 0x80, 0x60, 0xff, 0xc3, 0x7a, 0x5a, 0xff, 0xc6, 0x80, 0x5e, 0xff, 0xcb, 0x7e, 
0x60, 0xff, 0xcc, 0x81, 0x62, 0xff, 0xc3, 0x7e, 0x5d, 0xff, 0xdb, 0xce, 0xc8, 0xff, 0xd9, 
0xc5, 0xbe, 0xff, 0xc9, 0x81, 0x69, 0xff, 0xc7, 0x82, 0x65, 0xff, 0xc8, 0x7e, 0x65, 0xff, 
0xc9, 0x7f, 0x66, 0xff, 0xc8, 0x7e, 0x63, 0xff, 0xc6, 0x7a, 0x60, 0xff, 0xc8, 0x7d, 0x60, 
0xff, 0xcb, 0x80, 0x63, 0xff, 0xcd, 0x82, 0x63, 0xff, 0xcf, 0x84, 0x65, 0xff, 0xcd, 0x82, 
0x63, 0xff, 0xd0, 0x85, 0x66, 0xff, 0xcb, 0x81, 0x64, 0xff, 0xce, 0x84, 0x67, 0xff, 0xc3, 
0x7c, 0x5e, 0xff, 0xc4, 0x7d, 0x61, 0xff, 0xbf, 0x79, 0x5f, 0xff, 0xce, 0x8b, 0x70, 0xff, 
0xc0, 0x7f, 0x63, 0xff, 0xb9, 0x79, 0x5e, 0xff, 0xc5, 0x84, 0x68, 0xff, 0xc5, 0x7e, 0x68, 
0xff, 0xb5, 0x77, 0x62, 0xff, 0xdb, 0xc9, 0xc7, 0xff, 0xdf, 0xd1, 0xce, 0xff, 0xc3, 0x7f, 
0x6a, 0xff, 0xbf, 0x82, 0x66, 0xff, 0xcc, 0x81, 0x64, 0xff, 0xc8, 0x81, 0x63, 0xff, 0xc9, 
0x80, 0x60, 0xff, 0xc6, 0x7d, 0x5d, 0xff, 0xc8, 0x7f, 0x5f, 0xff, 0xca, 0x81, 0x61, 0xff, 
0xca, 0x81, 0x61, 0xff, 0xca, 0x81, 0x61, 0xff, 0xc5, 0x7e, 0x5e, 0xff, 0xc8, 0x7f, 0x5f, 
0xff, 0xc2, 0x79, 0x59, 0xff, 0xc9, 0x7f, 0x62, 0xff, 0xce, 0x84, 0x67, 0xff, 0xcb, 0x81, 
0x64, 0xff, 0xcb, 0x81, 0x64, 0xff, 0xc4, 0x7a, 0x5d, 0xff, 0xc3, 0x79, 0x5c, 0xff, 0xc3, 
0x7d, 0x5b, 0xff, 0xc7, 0x7a, 0x5c, 0xff, 0xca, 0x7f, 0x60, 0xff, 0xc5, 0x80, 0x5f, 0xff, 
0xe0, 0xd3, 0xcd, 0xff, 0xd9, 0xc5, 0xbe, 0xff, 0xcb, 0x83, 0x6b, 0xff, 0xcb, 0x86, 0x69, 
0xff, 0xc7, 0x7d, 0x64, 0xff, 0xca, 0x80, 0x65, 0xff, 0xca, 0x80, 0x65, 0xff, 0xcb, 0x80, 
0x63, 0xff, 0xcb, 0x80, 0x63, 0xff, 0xc9, 0x7e, 0x61, 0xff, 0xc9, 0x80, 0x60, 0xff, 0xcc, 
0x83, 0x63, 0xff, 0xce, 0x83, 0x64, 0xff, 0xd0, 0x85, 0x66, 0xff, 0xcb, 0x81, 0x64, 0xff, 
0xca, 0x83, 0x67, 0xff, 0xc5, 0x7e, 0x62, 0xff, 0xc4, 0x7e, 0x64, 0xff, 0xc5, 0x83, 0x69, 
0xff, 0xb6, 0x76, 0x5b, 0xff, 0xbf, 0x82, 0x66, 0xff, 0xb0, 0x74, 0x58, 0xff, 0xb6, 0x75, 
0x57, 0xff, 0xc4, 0x81, 0x66, 0xff, 0xc1, 0x82, 0x61, 0xff, 0xd2, 0xbc, 0xb1, 0xff, 0xdf, 
0xd7, 0xd5, 0xff, 0x97, 0x6f, 0x63, 0xff, 0x89, 0x65, 0x4f, 0xff, 0xce, 0x83, 0x66, 0xff, 
0xcb, 0x84, 0x66, 0xff, 0xcb, 0x81, 0x64, 0xff, 0xc9, 0x80, 0x60, 0xff, 0xcc, 0x83, 0x63, 
0xff, 0xcd, 0x84, 0x64, 0xff, 0xca, 0x81, 0x61, 0xff, 0xca, 0x81, 0x61, 0xff, 0xc5, 0x7e, 
0x5e, 0xff, 0xc6, 0x7c, 0x5f, 0xff, 0xca, 0x80, 0x63, 0xff, 0xc8, 0x7e, 0x61, 0xff, 0xc8, 
0x81, 0x63, 0xff, 0xca, 0x83, 0x65, 0xff, 0xcc, 0x85, 0x67, 0xff, 0xc5, 0x7e, 0x60, 0xff, 
0xc7, 0x80, 0x62, 0xff, 0xc3, 0x7e, 0x5d, 0xff, 0xc6, 0x7b, 0x5e, 0xff, 0xca, 0x80, 0x63, 
0xff, 0xc7, 0x82, 0x63, 0xff, 0xe2, 0xd4, 0xd1, 0xff, 0xd6, 0xc2, 0xbb, 0xff, 0xc9, 0x81, 
0x69, 0xff, 0xca, 0x83, 0x67, 0xff, 0xcd, 0x83, 0x6a, 0xff, 0xca, 0x80, 0x65, 0xff, 0xc6, 
0x7c, 0x61, 0xff, 0xc9, 0x7f, 0x62, 0xff, 0xca, 0x80, 0x63, 0xff, 0xca, 0x80, 0x63, 0xff, 
0xcd, 0x83, 0x66, 0xff, 0xcc, 0x85, 0x67, 0xff, 0xd1, 0x86, 0x67, 0xff, 0xd0, 0x85, 0x66, 
0xff, 0xca, 0x83, 0x67, 0xff, 0xc8, 0x80, 0x67, 0xff, 0xc4, 0x7e, 0x64, 0xff, 0xd0, 0x8a, 
0x71, 0xff, 0xbc, 0x79, 0x5f, 0xff, 0xbd, 0x7b, 0x61, 0xff, 0xb8, 0x78, 0x5d, 0xff, 0xba, 
0x7d, 0x61, 0xff, 0xcd, 0x81, 0x67, 0xff, 0xc7, 0x80, 0x64, 0xff, 0xc5, 0x80, 0x61, 0xff, 
0xdc, 0xbd, 0xb8, 0xff, 0xde, 0xd3, 0xd1, 0xff, 0xa7, 0x74, 0x63, 0xff, 0xb4, 0x82, 0x6b, 
0xff, 0xd3, 0x86, 0x6a, 0xff, 0xc4, 0x7d, 0x5f, 0xff, 0xc8, 0x7e, 0x61, 0xff, 0xc8, 0x7f, 
0x5f, 0xff, 0xca, 0x81, 0x61, 0xff, 0xc9, 0x80, 0x60, 0xff, 0xc7, 0x7e, 0x5e, 0xff, 0xc8, 
0x7f, 0x5f, 0xff, 0xc5, 0x7e, 0x5e, 0xff, 0xc4, 0x7d, 0x5f, 0xff, 0xc6, 0x7f, 0x61, 0xff, 
0xc7, 0x80, 0x62, 0xff, 0xca, 0x83, 0x65, 0xff, 0xc2, 0x7b, 0x5f, 0xff, 0xc0, 0x7b, 0x5e, 
0xff, 0xc8, 0x83, 0x66, 0xff, 0xbd, 0x78, 0x5b, 0xff, 0xc3, 0x82, 0x64, 0xff, 0xc9, 0x7f, 
0x64, 0xff, 0xcb, 0x84, 0x68, 0xff, 0xc8, 0x86, 0x66, 0xff, 0xdf, 0xd4, 0xd0, 0xff, 0xd6, 
0xc2, 0xbb, 0xff, 0xca, 0x80, 0x67, 0xff, 0xc6, 0x7f, 0x61, 0xff, 0xc6, 0x7c, 0x63, 0xff, 
0xc3, 0x79, 0x5e, 0xff, 0xc1, 0x77, 0x5c, 0xff, 0xc7, 0x7d, 0x60, 0xff, 0xc9, 0x7f, 0x62, 
0xff, 0xc6, 0x7f, 0x63, 0xff, 0xc8, 0x81, 0x65, 0xff, 0xc2, 0x7a, 0x61, 0xff, 0xcc, 0x82, 
0x65, 0xff, 0xca, 0x80, 0x63, 0xff, 0xc9, 0x83, 0x69, 0xff, 0xc7, 0x81, 0x67, 0xff, 0xca, 
0x84, 0x6b, 0xff, 0xc9, 0x83, 0x6a, 0xff, 0xc3, 0x7c, 0x66, 0xff, 0xc5, 0x82, 0x68, 0xff, 
0xcb, 0x88, 0x6e, 0xff, 0xc2, 0x7f, 0x65, 0xff, 0xc5, 0x7f, 0x65, 0xff, 0xbc, 0x7c, 0x63, 
0xff, 0xbe, 0x80, 0x6b, 0xff, 0xcc, 0xb8, 0xb9, 0xff, 0xda, 0xd1, 0xc8, 0xff, 0xca, 0x81, 
0x61, 0xff, 0xc5, 0x7e, 0x5e, 0xff, 0xc2, 0x81, 0x61, 0xff, 0xbf, 0x78, 0x5a, 0xff, 0xc6, 
0x7c, 0x5f, 0xff, 0xc8, 0x7f, 0x5f, 0xff, 0xc9, 0x80, 0x60, 0xff, 0xc7, 0x7e, 0x5e, 0xff, 
0xc6, 0x7d, 0x5d, 0xff, 0xc9, 0x80, 0x60, 0xff, 0xc7, 0x80, 0x60, 0xff, 0xc4, 0x7d, 0x5f, 
0xff, 0xc5, 0x7e, 0x60, 0xff, 0xc9, 0x84, 0x67, 0xff, 0xc2, 0x7d, 0x60, 0xff, 0xc2, 0x7d, 
0x60, 0xff, 0xc3, 0x7e, 0x61, 0xff, 0xc7, 0x82, 0x65, 0xff, 0xcd, 0x88, 0x6b, 0xff, 0xc2, 
0x81, 0x63, 0xff, 0xc7, 0x7f, 0x66, 0xff, 0xcb, 0x84, 0x68, 0xff, 0xc9, 0x86, 0x69, 0xff, 
0xe0, 0xd5, 0xd1, 0xff, 0xda, 0xc6, 0xbf, 0xff, 0xd0, 0x86, 0x6d, 0xff, 0xcb, 0x81, 0x64, 
0xff, 0xc6, 0x7e, 0x65, 0xff, 0xc6, 0x7f, 0x63, 0xff, 0xc9, 0x7f, 0x64, 0xff, 0xcc, 0x82, 
0x67, 0xff, 0xc6, 0x7f, 0x63, 0xff, 0xc4, 0x7d, 0x61, 0xff, 0xc7, 0x81, 0x67, 0xff, 0xc0, 
0x7a, 0x60, 0xff, 0xc3, 0x7d, 0x63, 0xff, 0xc5, 0x7f, 0x65, 0xff, 0xc9, 0x7d, 0x66, 0xff, 
0xc8, 0x87, 0x6b, 0xff, 0xc9, 0x88, 0x6c, 0xff, 0xcd, 0x8a, 0x70, 0xff, 0xc1, 0x7e, 0x64, 
0xff, 0xcc, 0x89, 0x6c, 0xff, 0xd5, 0x8e, 0x70, 0xff, 0xcb, 0x86, 0x69, 0xff, 0xd4, 0x89, 
0x74, 0xff, 0xbe, 0x7f, 0x60, 0xff, 0xa7, 0x76, 0x58, 0xff, 0xd1, 0xc3, 0xc3, 0xff, 0xe3, 
0xd4, 0xd1, 0xff, 0xbf, 0x89, 0x67, 0xff, 0xc8, 0x83, 0x5c, 0xff, 0xcf, 0x80, 0x61, 0xff, 
0xc0, 0x76, 0x5d, 0xff, 0xc9, 0x80, 0x60, 0xff, 0xc9, 0x7e, 0x5e, 0xff, 0xb3, 0x7d, 0x5b, 
0xff, 0xbb, 0x7b, 0x5f, 0xff, 0xd2, 0x7c, 0x63, 0xff, 0xc8, 0x83, 0x62, 0xff, 0xce, 0x87, 
0x69, 0xff, 0xd4, 0x8a, 0x65, 0xff, 0xd0, 0x84, 0x64, 0xff, 0xcf, 0x88, 0x6c, 0xff, 0xc9, 
0x87, 0x6d, 0xff, 0xbb, 0x75, 0x5b, 0xff, 0xc8, 0x7b, 0x61, 0xff, 0xcf, 0x80, 0x62, 0xff, 
0xcf, 0x86, 0x66, 0xff, 0xd0, 0x87, 0x66, 0xff, 0xbe, 0x81, 0x65, 0xff, 0xbe, 0x7b, 0x5e, 
0xff, 0xcc, 0x84, 0x6b, 0xff, 0xdb, 0xd2, 0xd3, 0xff, 0xda, 0xc4, 0xb9, 0xff, 0xca, 0x7e, 
0x67, 0xff, 0xca, 0x7f, 0x60, 0xff, 0xc4, 0x79, 0x64, 0xff, 0xca, 0x7f, 0x68, 0xff, 0xc3, 
0x79, 0x60, 0xff, 0xc7, 0x7d, 0x64, 0xff, 0xc6, 0x7c, 0x63, 0xff, 0xc8, 0x7e, 0x65, 0xff, 
0xc8, 0x80, 0x67, 0xff, 0xc0, 0x7c, 0x65, 0xff, 0xbc, 0x7a, 0x60, 0xff, 0xc7, 0x81, 0x68, 
0xff, 0xc4, 0x85, 0x5a, 0xff, 0xb9, 0x6d, 0x55, 0xff, 0xcb, 0x82, 0x6f, 0xff, 0xc2, 0x80, 
0x66, 0xff, 0xcb, 0x81, 0x68, 0xff, 0xca, 0x82, 0x6a, 0xff, 0xb8, 0x7b, 0x5f, 0xff, 0xc8, 
0x8c, 0x6a, 0xff, 0xb4, 0x7d, 0x5e, 0xff, 0xbd, 0x7f, 0x68, 0xff, 0xc7, 0x7f, 0x66, 0xff, 
0xe7, 0xd3, 0xdc, 0xff, 0xe7, 0xd6, 0xdc, 0xff, 0xca, 0x83, 0x63, 0xff, 0xc3, 0x80, 0x63, 
0xff, 0xcb, 0x85, 0x61, 0xff, 0xc9, 0x85, 0x62, 0xff, 0xbf, 0x7f, 0x63, 0xff, 0xc9, 0x7d, 
0x65, 0xff, 0xd3, 0x86, 0x6a, 0xff, 0xc5, 0x84, 0x68, 0xff, 0xc8, 0x84, 0x71, 0xff, 0xce, 
0x82, 0x6b, 0xff, 0xc4, 0x85, 0x5a, 0xff, 0xca, 0x85, 0x64, 0xff, 0xca, 0x85, 0x66, 0xff, 
0xc4, 0x7f, 0x62, 0xff, 0xb7, 0x76, 0x56, 0xff, 0xc2, 0x83, 0x62, 0xff, 0xc3, 0x82, 0x62, 
0xff, 0xc8, 0x87, 0x69, 0xff, 0xc9, 0x87, 0x6d, 0xff, 0xbb, 0x7d, 0x64, 0xff, 0xc6, 0x82, 
0x5f, 0xff, 0xc5, 0x80, 0x61, 0xff, 0xca, 0x7b, 0x5a, 0xff, 0xe6, 0xd3, 0xcd, 0xff, 0xd7, 
0xc5, 0xc1, 0xff, 0xcf, 0x83, 0x6b, 0xff, 0xc3, 0x7e, 0x5f, 0xff, 0xc5, 0x7a, 0x5a, 0xff, 
0xc6, 0x7b, 0x5c, 0xff, 0xc5, 0x7e, 0x62, 0xff, 0xc5, 0x80, 0x63, 0xff, 0xc4, 0x7f, 0x60, 
0xff, 0xc1, 0x7a, 0x5c, 0xff, 0xc2, 0x7c, 0x63, 0xff, 0xc1, 0x7c, 0x6c, 0xff, 0xb8, 0x78, 
0x5d, 0xff, 0xc2, 0x7f, 0x65, 0xff, 0xc1, 0x7b, 0x62, 0xff, 0xb1, 0x70, 0x52, 0xff, 0xc3, 
0x7b, 0x62, 0xff, 0xca, 0x7f, 0x6a, 0xff, 0xc5, 0x88, 0x6b, 0xff, 0xc7, 0x87, 0x63, 0xff, 
0xd2, 0x85, 0x67, 0xff, 0xc2, 0x74, 0x5e, 0xff, 0xd1, 0x89, 0x73, 0xff, 0xc3, 0x7a, 0x5a, 
0xff, 0xb5, 0x80, 0x5e, 0xff, 0xe0, 0xdb, 0xd7, 0xff, 0xe2, 0xd7, 0xd3, 0xff, 0xc8, 0x86, 
0x66, 0xff, 0xcb, 0x7b, 0x5a, 0xff, 0xc7, 0x7d, 0x64, 0xff, 0xc9, 0x7c, 0x60, 0xff, 0xcd, 
0x88, 0x6b, 0xff, 0xc5, 0x84, 0x68, 0xff, 0xc8, 0x85, 0x68, 0xff, 0xc6, 0x83, 0x66, 0xff, 
0xc6, 0x83, 0x68, 0xff, 0xc4, 0x7e, 0x65, 0xff, 0xcc, 0x86, 0x6d, 0xff, 0xc7, 0x7d, 0x62, 
0xff, 0xc9, 0x7e, 0x5f, 0xff, 0xcd, 0x81, 0x61, 0xff, 0xc5, 0x78, 0x5a, 0xff, 0xc2, 0x76, 
0x5e, 0xff, 0xc6, 0x7e, 0x68, 0xff, 0xbf, 0x7a, 0x5d, 0xff, 0xc3, 0x81, 0x5f, 0xff, 0xc9, 
0x86, 0x69, 0xff, 0xc6, 0x81, 0x62, 0xff, 0xcf, 0x85, 0x68, 0xff, 0xce, 0x84, 0x61, 0xff, 
0xde, 0xd5, 0xcc, 0xff, 0xd2, 0xcb, 0xc1, 0xff, 0xd1, 0x87, 0x6a, 0xff, 0xcb, 0x83, 0x6a, 
0xff, 0xc9, 0x7e, 0x5e, 0xff, 0xbf, 0x7c, 0x5f, 0xff, 0xc1, 0x7c, 0x5d, 0xff, 0xcc, 0x80, 
0x60, 0xff, 0xce, 0x7f, 0x61, 0xff, 0xc3, 0x78, 0x61, 0xff, 0xc7, 0x81, 0x67, 0xff, 0xbf, 
0x79, 0x57, 0xff, 0xb9, 0x79, 0x5e, 0xff, 0xc2, 0x81, 0x65, 0xff, 0xbe, 0x79, 0x5a, 0xff, 
0xc7, 0x7b, 0x61, 0xff, 0xc7, 0x7a, 0x60, 0xff, 0xc9, 0x7e, 0x61, 0xff, 0xc5, 0x7e, 0x62, 
0xff, 0xba, 0x78, 0x5e, 0xff, 0xbb, 0x78, 0x5d, 0xff, 0xce, 0x81, 0x65, 0xff, 0xcb, 0x84, 
0x64, 0xff, 0xcd, 0x83, 0x6a, 0xff, 0xd1, 0xa1, 0x97, 0xff, 0xe8, 0xe6, 0xe9, 0xff, 0xdf, 
0xe3, 0xe4, 0xff, 0xc9, 0xa3, 0x96, 0xff, 0xca, 0x82, 0x69, 0xff, 0xc9, 0x7a, 0x5c, 0xff, 
0xca, 0x7e, 0x64, 0xff, 0x9d, 0x78, 0x65, 0xff, 0xaf, 0x73, 0x69, 0xff, 0xc7, 0x82, 0x65, 
0xff, 0xc7, 0x84, 0x5a, 0xff, 0xd3, 0x7c, 0x61, 0xff, 0xcb, 0x79, 0x61, 0xff, 0xc8, 0x82, 
0x5e, 0xff, 0xca, 0x89, 0x6b, 0xff, 0xc8, 0x7d, 0x60, 0xff, 0xce, 0x81, 0x63, 0xff, 0xd0, 
0x8a, 0x68, 0xff, 0xc9, 0x89, 0x65, 0xff, 0xbf, 0x7a, 0x59, 0xff, 0xc8, 0x7e, 0x61, 0xff, 
0xc4, 0x79, 0x5c, 0xff, 0xc0, 0x78, 0x60, 0xff, 0xc7, 0x7b, 0x57, 0xff, 0xbe, 0x82, 0x66, 
0xff, 0xc8, 0xaa, 0x9f, 0xff, 0xe8, 0xea, 0xe5, 0xff, 0xe0, 0xe4, 0xe5, 0xff, 0xca, 0xae, 
0xab, 0xff, 0xbe, 0x82, 0x6a, 0xff, 0xc7, 0x79, 0x62, 0xff, 0xcd, 0x7d, 0x66, 0xff, 0xcf, 
0x81, 0x6a, 0xff, 0xc9, 0x82, 0x66, 0xff, 0xc4, 0x85, 0x66, 0xff, 0xc1, 0x80, 0x62, 0xff, 
0xc9, 0x7e, 0x61, 0xff, 0xd4, 0x7d, 0x61, 0xff, 0xb8, 0x77, 0x5b, 0xff, 0xbf, 0x7c, 0x61, 
0xff, 0xc5, 0x7a, 0x5b, 0xff, 0xc8, 0x80, 0x68, 0xff, 0xbc, 0x78, 0x63, 0xff, 0xc6, 0x7b, 
0x64, 0xff, 0xcd, 0x7b, 0x63, 0xff, 0xcb, 0x7e, 0x62, 0xff, 0xc1, 0x7c, 0x5d, 0xff, 0xc3, 
0x7e, 0x5f, 0xff, 0xbe, 0x8a, 0x75, 0xff, 0xc9, 0xc2, 0xbc, 0xff, 0xd9, 0xdb, 0xd0, 0xff, 
0xcc, 0xc5, 0xbf, 0xff, 0xda, 0xd1, 0xcc, 0xff, 0xe3, 0xe0, 0xd9, 0xff, 0xc0, 0xc1, 0xbb, 
0xff, 0xbe, 0x9b, 0x85, 0xff, 0xca, 0x84, 0x6a, 0xff, 0x97, 0x7d, 0x64, 0xff, 0xb8, 0x7c, 
0x71, 0xff, 0xce, 0x79, 0x64, 0xff, 0xc5, 0x7f, 0x5d, 0xff, 0xc6, 0x83, 0x69, 0xff, 0xc3, 
0x81, 0x67, 0xff, 0xcc, 0x7d, 0x5c, 0xff, 0xc4, 0x84, 0x61, 0xff, 0xc8, 0x7f, 0x5e, 0xff, 
0xd0, 0x85, 0x65, 0xff, 0xc8, 0x83, 0x64, 0xff, 0xcc, 0x85, 0x69, 0xff, 0xd6, 0x84, 0x6c, 
0xff, 0xcc, 0x7d, 0x5e, 0xff, 0xc8, 0x87, 0x5f, 0xff, 0xcf, 0x7f, 0x66, 0xff, 0xc7, 0x8f, 
0x78, 0xff, 0xd8, 0xc3, 0xc0, 0xff, 0xd9, 0xd9, 0xe3, 0xff, 0xc5, 0xbb, 0xb9, 0xff, 0xcf, 
0xc4, 0xc2, 0xff, 0xe0, 0xda, 0xdc, 0xff, 0xd2, 0xcb, 0xc3, 0xff, 0xc7, 0x96, 0x91, 0xff, 
0xbc, 0x7c, 0x63, 0xff, 0xcb, 0x85, 0x63, 0xff, 0xc8, 0x87, 0x6b, 0xff, 0xbe, 0x7c, 0x66, 
0xff, 0xcf, 0x83, 0x6b, 0xff, 0xca, 0x7e, 0x64, 0xff, 0xc3, 0x7f, 0x68, 0xff, 0xc2, 0x7c, 
0x62, 0xff, 0xc8, 0x81, 0x65, 0xff, 0xc5, 0x7d, 0x64, 0xff, 0xc2, 0x78, 0x5d, 0xff, 0xcb, 
0x7f, 0x67, 0xff, 0xc4, 0x79, 0x62, 0xff, 0xbf, 0x78, 0x5a, 0xff, 0xcb, 0x7a, 0x5d, 0xff, 
0xc5, 0x86, 0x74, 0xff, 0xc6, 0xb0, 0xa5, 0xff, 0xdf, 0xd9, 0xd9, 0xff, 0xd9, 0xc3, 0xc5, 
0xff, 0xc7, 0x92, 0x8a, 0xff, 0xc5, 0x82, 0x72, 0xff, 0xc8, 0x8b, 0x79, 0xff, 0xc7, 0xa3, 
0x97, 0xff, 0xdf, 0xd7, 0xd5, 0xff, 0xda, 0xda, 0xdc, 0xff, 0xc4, 0xb4, 0xa7, 0xff, 0xae, 
0x84, 0x76, 0xff, 0xc6, 0x7f, 0x6b, 0xff, 0xcc, 0x7f, 0x63, 0xff, 0xc4, 0x7f, 0x60, 0xff, 
0xbb, 0x7e, 0x62, 0xff, 0xc1, 0x81, 0x68, 0xff, 0xc5, 0x7e, 0x68, 0xff, 0xca, 0x81, 0x61, 
0xff, 0xcb, 0x7e, 0x62, 0xff, 0xcb, 0x7e, 0x64, 0xff, 0xd0, 0x8d, 0x70, 0xff, 0xbd, 0x7c, 
0x5c, 0xff, 0xc6, 0x81, 0x64, 0xff, 0xcb, 0x83, 0x6d, 0xff, 0xc6, 0x82, 0x6f, 0xff, 0xb8, 
0xb8, 0xac, 0xff, 0xdc, 0xdd, 0xd8, 0xff, 0xd5, 0xbd, 0xbb, 0xff, 0xc1, 0x8a, 0x75, 0xff, 
0xb4, 0x72, 0x52, 0xff, 0xa8, 0x6a, 0x53, 0xff, 0xd4, 0x9a, 0x84, 0xff, 0xd7, 0xc3, 0xcf, 
0xff, 0xd7, 0xda, 0xcf, 0xff, 0xcb, 0xbd, 0xb4, 0xff, 0xb5, 0x87, 0x78, 0xff, 0xbd, 0x76, 
0x60, 0xff, 0xc9, 0x7c, 0x60, 0xff, 0xc6, 0x7c, 0x61, 0xff, 0xbe, 0x79, 0x5c, 0xff, 0xc8, 
0x83, 0x66, 0xff, 0xc4, 0x7a, 0x5f, 0xff, 0xc9, 0x7d, 0x63, 0xff, 0xcb, 0x79, 0x54, 0xff, 
0xc7, 0x7b, 0x5b, 0xff, 0xc8, 0x7f, 0x5f, 0xff, 0xd0, 0x7c, 0x60, 0xff, 0xcc, 0x81, 0x6e, 
0xff, 0xc4, 0xa6, 0x9c, 0xff, 0xd9, 0xd5, 0xd2, 0xff, 0xde, 0xcf, 0xd2, 0xff, 0xce, 0xa5, 
0xa3, 0xff, 0xcb, 0x93, 0x7c, 0xff, 0xc1, 0x8a, 0x75, 0xff, 0xc6, 0x87, 0x75, 0xff, 0xcb, 
0x8a, 0x78, 0xff, 0xb9, 0x80, 0x6d, 0xff, 0xbc, 0x88, 0x73, 0xff, 0xd6, 0xb7, 0xb5, 0xff, 
0xe5, 0xde, 0xe5, 0xff, 0xce, 0xd2, 0xd1, 0xff, 0xc4, 0xa7, 0x9f, 0xff, 0xc6, 0x79, 0x67, 
0xff, 0xcb, 0x7c, 0x5d, 0xff, 0xc4, 0x82, 0x62, 0xff, 0xc5, 0x7e, 0x62, 0xff, 0xca, 0x80, 
0x5d, 0xff, 0xc7, 0x81, 0x67, 0xff, 0xb8, 0x82, 0x68, 0xff, 0xb9, 0x87, 0x6e, 0xff, 0xc1, 
0x7c, 0x5f, 0xff, 0xce, 0x80, 0x5c, 0xff, 0xc3, 0x88, 0x68, 0xff, 0xc3, 0xa9, 0x9c, 0xff, 
0xd8, 0xd3, 0xd9, 0xff, 0xda, 0xcb, 0xc4, 0xff, 0xc0, 0x95, 0x84, 0xff, 0xb3, 0x6a, 0x59, 
0xff, 0xaf, 0x65, 0x4c, 0xff, 0xbf, 0x76, 0x55, 0xff, 0xa8, 0x72, 0x58, 0xff, 0xbf, 0x7b, 
0x4c, 0xff, 0xcd, 0x7f, 0x59, 0xff, 0xcd, 0xa3, 0x8b, 0xff, 0xe3, 0xd8, 0xd6, 0xff, 0xd1, 
0xd4, 0xdb, 0xff, 0xc6, 0xb0, 0xa5, 0xff, 0xc4, 0x89, 0x6b, 0xff, 0xc3, 0x78, 0x58, 0xff, 
0xcd, 0x82, 0x65, 0xff, 0xc2, 0x76, 0x5c, 0xff, 0xc2, 0x77, 0x5a, 0xff, 0xc6, 0x79, 0x5d, 
0xff, 0xc4, 0x7c, 0x66, 0xff, 0xcd, 0x7c, 0x61, 0xff, 0xc8, 0x7d, 0x60, 0xff, 0xc0, 0x9b, 
0x88, 0xff, 0xcb, 0xcb, 0xc3, 0xff, 0xda, 0xd5, 0xd2, 0xff, 0xd4, 0xb2, 0xa8, 0xff, 0xc5, 
0x91, 0x7b, 0xff, 0xbe, 0x8c, 0x71, 0xff, 0xc2, 0x88, 0x72, 0xff, 0xcb, 0x8a, 0x76, 0xff, 
0xc9, 0x90, 0x7c, 0xff, 0xc2, 0x89, 0x76, 0xff, 0xcc, 0x8b, 0x79, 0xff, 0xc6, 0x89, 0x77, 
0xff, 0xbb, 0x83, 0x6c, 0xff, 0xc5, 0x8c, 0x85, 0xff, 0xdc, 0xcb, 0xc3, 0xff, 0xe2, 0xe3, 
0xde, 0xff, 0xc8, 0xc8, 0xc0, 0xff, 0xc4, 0x96, 0x87, 0xff, 0xcd, 0x77, 0x5e, 0xff, 0xc9, 
0x82, 0x62, 0xff, 0xd0, 0x83, 0x6f, 0xff, 0xd5, 0x84, 0x6f, 0xff, 0xc0, 0x7e, 0x5c, 0xff, 
0xc2, 0x82, 0x5f, 0xff, 0xc8, 0x85, 0x72, 0xff, 0xc7, 0xa0, 0x9b, 0xff, 0xd7, 0xd3, 0xd0, 
0xff, 0xdf, 0xdb, 0xd2, 0xff, 0xd0, 0xae, 0xa5, 0xff, 0xb2, 0x6a, 0x54, 0xff, 0xa6, 0x6a, 
0x50, 0xff, 0xb6, 0x6c, 0x49, 0xff, 0xae, 0x71, 0x52, 0xff, 0xbf, 0x75, 0x58, 0xff, 0xb8, 
0x6d, 0x5a, 0xff, 0xbc, 0x76, 0x54, 0xff, 0xbd, 0x74, 0x54, 0xff, 0xc2, 0x78, 0x5d, 0xff, 
0xbb, 0x7f, 0x67, 0xff, 0xd5, 0xb5, 0xaa, 0xff, 0xe2, 0xdd, 0xe1, 0xff, 0xd4, 0xcb, 0xce, 
0xff, 0xc2, 0x9d, 0x8d, 0xff, 0xc6, 0x83, 0x68, 0xff, 0xca, 0x79, 0x5c, 0xff, 0xc6, 0x7b, 
0x5e, 0xff, 0xca, 0x7d, 0x61, 0xff, 0xc4, 0x76, 0x60, 0xff, 0xc0, 0x92, 0x82, 0xff, 0xc9, 
0xc2, 0xbc, 0xff, 0xd8, 0xda, 0xd9, 0xff, 0xd8, 0xc1, 0xbb, 0xff, 0xca, 0x92, 0x83, 0xff, 
0xcb, 0x89, 0x79, 0xff, 0xc4, 0x8a, 0x7e, 0xff, 0xbb, 0x82, 0x6f, 0xff, 0xbc, 0x81, 0x6f, 
0xff, 0xc3, 0x89, 0x75, 0xff, 0xc1, 0x87, 0x73, 0xff, 0xc1, 0x87, 0x73, 0xff, 0xc2, 0x85, 
0x72, 0xff, 0xc1, 0x84, 0x71, 0xff, 0xbf, 0x82, 0x6f, 0xff, 0xc0, 0x86, 0x72, 0xff, 0xbd, 
0x82, 0x72, 0xff, 0xcd, 0x9c, 0x95, 0xff, 0xdb, 0xd4, 0xcc, 0xff, 0xdd, 0xe3, 0xe1, 0xff, 
0xcc, 0xb7, 0xb4, 0xff, 0xbc, 0x8d, 0x7b, 0xff, 0xc6, 0x84, 0x64, 0xff, 0xbe, 0x82, 0x5e, 
0xff, 0xcd, 0x86, 0x70, 0xff, 0xc3, 0x95, 0x86, 0xff, 0xcb, 0xc8, 0xc3, 0xff, 0xdd, 0xde, 
0xe3, 0xff, 0xe2, 0xbf, 0xb9, 0xff, 0xc8, 0x86, 0x6c, 0xff, 0xc3, 0x76, 0x58, 0xff, 0xb9, 
0x74, 0x55, 0xff, 0xbf, 0x78, 0x58, 0xff, 0xcc, 0x82, 0x65, 0xff, 0xbc, 0x72, 0x55, 0xff, 
0xb7, 0x70, 0x54, 0xff, 0xb6, 0x6c, 0x4f, 0xff, 0xc1, 0x76, 0x56, 0xff, 0xc9, 0x7d, 0x59, 
0xff, 0xc3, 0x7e, 0x55, 0xff, 0xc5, 0x7f, 0x5b, 0xff, 0xc2, 0x79, 0x59, 0xff, 0xc7, 0x8d, 
0x79, 0xff, 0xdf, 0xc6, 0xc1, 0xff, 0xd6, 0xda, 0xdd, 0xff, 0xca, 0xc2, 0xc0, 0xff, 0xc0, 
0x91, 0x81, 0xff, 0xc8, 0x7e, 0x61, 0xff, 0xc3, 0x79, 0x5c, 0xff, 0xc2, 0xc0, 0xc5, 0xff, 
0xdd, 0xe0, 0xd9, 0xff, 0xdf, 0xce, 0xc4, 0xff, 0xd1, 0x9c, 0x94, 0xff, 0xd0, 0x8b, 0x7b, 
0xff, 0xc6, 0x8b, 0x6b, 0xff, 0xc4, 0x8e, 0x6c, 0xff, 0xc7, 0x85, 0x6f, 0xff, 0xc2, 0x88, 
0x74, 0xff, 0xc0, 0x86, 0x72, 0xff, 0xc3, 0x89, 0x75, 0xff, 0xbe, 0x84, 0x70, 0xff, 0xc0, 
0x83, 0x70, 0xff, 0xc0, 0x83, 0x70, 0xff, 0xc2, 0x85, 0x72, 0xff, 0xc2, 0x85, 0x72, 0xff, 
0xc9, 0x82, 0x6c, 0xff, 0xc9, 0x8c, 0x70, 0xff, 0xc6, 0x84, 0x6c, 0xff, 0xbb, 0x82, 0x6f, 
0xff, 0xd1, 0xaf, 0xa3, 0xff, 0xe9, 0xda, 0xd5, 0xff, 0xe1, 0xd9, 0xd7, 0xff, 0xcd, 0xb4, 
0xba, 0xff, 0xc3, 0x9e, 0x95, 0xff, 0xcd, 0xbb, 0xbb, 0xff, 0xe5, 0xdd, 0xdb, 0xff, 0xe5, 
0xcf, 0xc4, 0xff, 0xc7, 0x91, 0x79, 0xff, 0xba, 0x71, 0x46, 0xff, 0xc3, 0x76, 0x48, 0xff, 
0xc4, 0x78, 0x5e, 0xff, 0xb7, 0x72, 0x53, 0xff, 0xba, 0x73, 0x55, 0xff, 0xc2, 0x7b, 0x5d, 
0xff, 0xb6, 0x6f, 0x53, 0xff, 0xb5, 0x6e, 0x52, 0xff, 0xb4, 0x6d, 0x4f, 0xff, 0xc6, 0x7b, 
0x5b, 0xff, 0xca, 0x7e, 0x5c, 0xff, 0xc8, 0x76, 0x60, 0xff, 0xb6, 0x6a, 0x52, 0xff, 0xbd, 
0x72, 0x53, 0xff, 0xcb, 0x7c, 0x5b, 0xff, 0xc4, 0x79, 0x5a, 0xff, 0xcd, 0x98, 0x86, 0xff, 
0xdd, 0xca, 0xc4, 0xff, 0xd9, 0xdf, 0xdb, 0xff, 0xce, 0xb1, 0xa9, 0xff, 0xc5, 0xa7, 0x9f, 
0xff, 0xe3, 0xde, 0xe2, 0xff, 0xd2, 0xad, 0xa7, 0xff, 0xca, 0x88, 0x78, 0xff, 0xca, 0x87, 
0x74, 0xff, 0xc6, 0x89, 0x74, 0xff, 0xc2, 0x85, 0x70, 0xff, 0xc9, 0x8d, 0x75, 0xff, 0xc1, 
0x8a, 0x75, 0xff, 0xc4, 0x8a, 0x76, 0xff, 0xc2, 0x88, 0x74, 0xff, 0xc8, 0x8b, 0x78, 0xff, 
0xc3, 0x86, 0x73, 0xff, 0xc2, 0x85, 0x72, 0xff, 0xc2, 0x85, 0x70, 0xff, 0xc3, 0x86, 0x71, 
0xff, 0xc3, 0x85, 0x70, 0xff, 0xc1, 0x88, 0x77, 0xff, 0xc0, 0x83, 0x71, 0xff, 0xc4, 0x81, 
0x70, 0xff, 0xb9, 0x83, 0x6b, 0xff, 0xc4, 0x8b, 0x78, 0xff, 0xcb, 0x8e, 0x89, 0xff, 0xd4, 
0xbb, 0xb7, 0xff, 0xdc, 0xe5, 0xe0, 0xff, 0xe9, 0xe5, 0xe6, 0xff, 0xe4, 0xdc, 0xda, 0xff, 
0xcc, 0xa8, 0x98, 0xff, 0xb7, 0x72, 0x53, 0xff, 0xc1, 0x77, 0x54, 0xff, 0xb4, 0x7a, 0x54, 
0xff, 0xae, 0x72, 0x4d, 0xff, 0xb9, 0x6b, 0x54, 0xff, 0xb0, 0x6e, 0x4e, 0xff, 0xb1, 0x6c, 
0x4d, 0xff, 0xb7, 0x70, 0x52, 0xff, 0xaf, 0x68, 0x4c, 0xff, 0xaf, 0x67, 0x4e, 0xff, 0xb0, 
0x69, 0x4d, 0xff, 0xc6, 0x7d, 0x5d, 0xff, 0xc5, 0x7b, 0x58, 0xff, 0xbf, 0x7a, 0x53, 0xff, 
0xc9, 0x7c, 0x5e, 0xff, 0xbd, 0x73, 0x5a, 0xff, 0xba, 0x77, 0x5a, 0xff, 0xb5, 0x71, 0x4e, 
0xff, 0xb4, 0x69, 0x4a, 0xff, 0xb0, 0x71, 0x5f, 0xff, 0xd4, 0xac, 0xac, 0xff, 0xe5, 0xe6, 
0xe8, 0xff, 0xe1, 0xe1, 0xe3};

const int brick_50_43_argb8888_length = 8600;

#endif    // BRICK_50_43_ARGB8888__
