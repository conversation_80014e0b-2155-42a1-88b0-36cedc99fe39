#include "hynitron_core.h"

static uint32_t get_u32_from_ptr(const void *ptr)
{
    uint32_t temp = 0;
    uint8_t *data = (uint8_t *)ptr;
    temp |= *data;
    data++;
    temp |= (uint32_t)(*data) << 8;
    data++;
    temp |= (uint32_t)(*data) << 16;
    data++;
    temp |= (uint32_t)(*data) << 24;
    data++;
    return temp;
}

static uint32_t firmware_verify_128bytes_bin_data(uint8_t *pdata, uint16_t order)
{
    uint32_t sum = 0;
    uint16_t data_len = 0;
    uint16_t i;

    if (!pdata)
    {
        HYNITRON_ERROR("pdata error return");
        return 0;
    }
    data_len = 128;
    if (order == 254)
        data_len = (128 - 20);
    for (i = 0; i < data_len; i += 4)
    {
        sum += get_u32_from_ptr(pdata + i);
    }
    return sum;
}
int16_t enter_bootloader(void)
{
    int16_t ret = 0;
    uint8_t i2c_buf[4] = {0};
    uint8_t check_cnt = 0;

    HYN_FUNC_ENTER;
    for (uint8_t i = 10;; i += 2)
    {
        if (i > 20)
        {
            HYNITRON_ERROR("enter_bootloader:try timeout");
            return -1;
        }

        p_g_chip_obj->reset_ic();

        p_g_chip_obj->delay1ms(i);

        for (check_cnt = 0; check_cnt < 5; check_cnt++)
        {
            i2c_buf[0] = 0xA0;
            i2c_buf[1] = 0x01;
            i2c_buf[2] = 0xAA;
            ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 3);
            if (ret)
            {
                continue;
            }

            p_g_chip_obj->delay1ms(2);

            i2c_buf[0] = 0xA0;
            i2c_buf[1] = 0x02;
            ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 2);
            if (ret)
            {
                continue;
            }
            ret = p_g_chip_obj->i2c_read(I2C_BOOT_ADDR, i2c_buf, 2);
            if (ret)
            {
                continue;
            }
            if ((i2c_buf[0] == 0x55) && (i2c_buf[1] == 0xB0))
            {
                break;
            }
        }
        if ((i2c_buf[0] == 0x55) && (i2c_buf[1] == 0xB0))
        {
            break;
        }
    }

    i2c_buf[0] = 0xA0;
    i2c_buf[1] = 0x01;
    i2c_buf[2] = 0x00;
    ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 3);
    if (ret)
    {
        HYNITRON_ERROR("enter_bootloader exit error");
        return -1;
    }
    HYN_FUNC_EXIT;
    return 0;
}

static int16_t read_word_from_mem(uint8_t type, uint16_t addr, uint32_t *value)
{
    int16_t ret = 0;
    uint8_t i2c_buf[4] = {0};

    i2c_buf[0] = 0xA0;
    i2c_buf[1] = 0x10;
    i2c_buf[2] = type;
    ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 3);
    if (ret)
    {
        return -1;
    }

    i2c_buf[0] = 0xA0;
    i2c_buf[1] = 0x0C;
    i2c_buf[2] = addr;
    i2c_buf[3] = addr >> 8;
    ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 4);
    if (ret)
    {
        return -1;
    }

    i2c_buf[0] = 0xA0;
    i2c_buf[1] = 0x04;
    i2c_buf[2] = 0xE4;
    ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 3);
    if (ret)
    {
        return -1;
    }

    for (uint8_t t = 0;; t++)
    {
        if (t >= 100)
        {
            return -1;
        }

        i2c_buf[0] = 0xA0;
        i2c_buf[1] = 0x04;
        ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 2);
        if (ret)
        {
            return -1;
        }
        ret = p_g_chip_obj->i2c_read(I2C_BOOT_ADDR, i2c_buf, 1);
        if (ret)
        {
            return -1;
        }
        if (i2c_buf[0] == 0x00)
        {
            break;
        }
    }

    i2c_buf[0] = 0xA0;
    i2c_buf[1] = 0x18;
    ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 2);
    if (ret)
    {
        return -1;
    }
    ret = p_g_chip_obj->i2c_read(I2C_BOOT_ADDR, i2c_buf, 4);
    if (ret)
    {
        return -1;
    }
    *value = ((uint32_t)(i2c_buf[0])) |
             (((uint32_t)(i2c_buf[1])) << 8) |
             (((uint32_t)(i2c_buf[2])) << 16) |
             (((uint32_t)(i2c_buf[3])) << 24);

    return 0;
}

static int16_t erase_all_mem(void)
{
    int16_t ret = 0;
    uint8_t i2c_buf[4] = {0};

    i2c_buf[0] = 0xA0;
    i2c_buf[1] = 0x14;
    i2c_buf[2] = 0x00;
    i2c_buf[3] = 0x00;
    ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 4);
    if (ret)
    {
        return -1;
    }

    i2c_buf[0] = 0xA0;
    i2c_buf[1] = 0x0C;
    i2c_buf[2] = 0x80;
    i2c_buf[3] = 0x7F;
    ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 4);
    if (ret)
    {
        return -1;
    }

    i2c_buf[0] = 0xA0;
    i2c_buf[1] = 0x04;
    i2c_buf[2] = 0xEC;
    ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 3);
    if (ret)
    {
        return -1;
    }
    p_g_chip_obj->delay1ms(300);
    for (uint16_t t = 0;; t += 10)
    {
        if (t >= 1000)
        {
            return -1;
        }

        p_g_chip_obj->delay1ms(10);

        i2c_buf[0] = 0xA0;
        i2c_buf[1] = 0x05;
        ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 2);
        if (ret)
        {
            continue;
        }
        ret = p_g_chip_obj->i2c_read(I2C_BOOT_ADDR, i2c_buf, 1);
        if (ret)
        {
            continue;
        }
        if (i2c_buf[0] == 0x88)
        {
            break;
        }
    }

    return 0;
}

static int16_t write_sram(uint8_t *buf, uint16_t len)
{
#if (HYNITRON_I2C_TRAN_PER_SIZE < HYNITRON_PROGRAM_PAGE_SIZE + 2)
    uint8_t i2c_buf[HYNITRON_I2C_TRAN_PER_SIZE] = {0};
#else
    uint8_t i2c_buf[HYNITRON_PROGRAM_PAGE_SIZE + 2] = {0};
#endif

    int16_t ret = 0;
    uint16_t reg = 0xA018;
    uint16_t per_len = sizeof(i2c_buf) - 2;

    while (len > 0)
    {
        uint16_t cur_len = len;
        if (cur_len > per_len)
        {
            cur_len = per_len;
        }

        i2c_buf[0] = reg >> 8;
        i2c_buf[1] = reg;
        memcpy(i2c_buf + 2, buf, cur_len);
        ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, cur_len + 2);
        if (ret)
        {
            return -1;
        }

        reg += cur_len;
        buf += cur_len;
        len -= cur_len;
    }

    return 0;
}

static int16_t write_mem_page(uint16_t addr, uint8_t *buf, uint16_t len)
{
    int16_t ret = 0;
    uint8_t i2c_buf[4] = {0};

    i2c_buf[0] = 0xA0;
    i2c_buf[1] = 0x0C;
    i2c_buf[2] = len;
    i2c_buf[3] = len >> 8;
    ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 4);
    if (ret)
    {
        return -1;
    }

    i2c_buf[0] = 0xA0;
    i2c_buf[1] = 0x14;
    i2c_buf[2] = addr;
    i2c_buf[3] = addr >> 8;
    ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 4);
    if (ret)
    {
        return -1;
    }

    ret = write_sram(buf, len);
    if (ret)
    {
        return -1;
    }

    i2c_buf[0] = 0xA0;
    i2c_buf[1] = 0x04;
    i2c_buf[2] = 0xEE;
    ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 3);
    if (ret)
    {
        return -1;
    }

    for (uint16_t t = 0;; t += 10)
    {
        if (t >= 1000)
        {
            return -1;
        }

        p_g_chip_obj->delay1ms(10);

        i2c_buf[0] = 0xA0;
        i2c_buf[1] = 0x05;
        ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 2);
        if (ret)
        {
            continue;
        }
        ret = p_g_chip_obj->i2c_read(I2C_BOOT_ADDR, i2c_buf, 1);
        if (ret)
        {
            continue;
        }
        if (i2c_buf[0] == 0x55)
        {
            break;
        }
    }

    return 0;
}

static int16_t write_mem_all(void)
{
    uint8_t *data;
    uint16_t addr = 0;
    uint16_t remain_len = MEM_SIZE;

    if (p_g_chip_obj->bin_data.data)
        data = p_g_chip_obj->bin_data.data;

    while (remain_len > 0)
    {
        uint16_t cur_len = remain_len;
        if (cur_len > HYNITRON_PROGRAM_PAGE_SIZE)
        {
            cur_len = HYNITRON_PROGRAM_PAGE_SIZE;
        }
#if HYN_ENABLE_UPGRADE_128_BYTE_WRITE
        // if write fw 128 bytes every time,need update point
        if (p_g_chip_obj->get_bin_addr(((MEM_SIZE - remain_len) / 128), MEM_128_SIZE) < 0)
        {
            HYNITRON_ERROR("get_bin_addr fail");
            return -1;
        }
        data = p_g_chip_obj->bin_data.data;
#endif
        if (write_mem_page(addr, data, cur_len) < 0)
        {
            return -1;
        }
        data += cur_len; // update bin point every 128 bytes
        addr += cur_len; // update bin point every 128 bytes
        remain_len -= cur_len;
    }
    return 0;
}

static int16_t calculate_verify_checksum(void)
{
    int16_t ret = 0;
    uint8_t i2c_buf[4] = {0};
    uint32_t checksum = 0;

    i2c_buf[0] = 0xA0;
    i2c_buf[1] = 0x03;
    i2c_buf[2] = 0x00;
    ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 3);
    if (ret)
    {
        return -1;
    }

    for (uint16_t t = 0;; t += 10)
    {
        if (t >= 1000)
        {
            return -1;
        }

        p_g_chip_obj->delay1ms(10);

        i2c_buf[0] = 0xA0;
        i2c_buf[1] = 0x00;
        ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 2);
        if (ret)
        {
            return -1;
        }
        ret = p_g_chip_obj->i2c_read(I2C_BOOT_ADDR, i2c_buf, 1);
        if (ret)
        {
            return -1;
        }
        if (i2c_buf[0] == 0x01)
        {
            break;
        }
        if (i2c_buf[0] == 0x02)
        {
            return -1;
        }
    }

    i2c_buf[0] = 0xA0;
    i2c_buf[1] = 0x08;
    ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 2);
    if (ret)
    {
        return -1;
    }
    ret = p_g_chip_obj->i2c_read(I2C_BOOT_ADDR, i2c_buf, 4);
    if (ret)
    {
        return -1;
    }
    checksum = ((uint32_t)(i2c_buf[0])) |
               (((uint32_t)(i2c_buf[1])) << 8) |
               (((uint32_t)(i2c_buf[2])) << 16) |
               (((uint32_t)(i2c_buf[3])) << 24);

    if (checksum != p_g_chip_obj->bin_data.checksum)
    {
        return -1;
    }

    return 0;
}

int16_t upgrade_firmware(void)
{
    int16_t ret = 0;
    uint8_t retry = 3;
    p_g_chip_obj->status.update_fw_running = 1;
    while (retry--)
    {
        ret = enter_bootloader();
        if (ret)
        {
            HYNITRON_ERROR("enter_bootloader fail.%d", retry);
            continue;
        }
        p_g_chip_obj->chip_ic_workmode = ENUM_MODE_UPDATE_FW;
        ret = erase_all_mem();
        if (ret)
        {
            HYNITRON_ERROR("erase_all_mem fail.%d", retry);
            continue;
        }
        ret = write_mem_all();
        if (ret)
        {
            HYNITRON_ERROR("write_mem_all fail.%d", retry);
            continue;
        }
        ret = calculate_verify_checksum();
        if (ret)
        {
            HYNITRON_ERROR("calculate_verify_checksum fail.%d", retry);
            continue;
        }
        else
        {
            break;
        }
    }

    p_g_chip_obj->chip_ic_workmode = ENUM_MODE_NORMAL;
    p_g_chip_obj->reset_ic();
    p_g_chip_obj->delay1ms(40);
    p_g_chip_obj->status.update_fw_running = 0;
    if ((retry == 0) && (ret))
    {
        HYNITRON_ERROR("upgrade_firmware fail exit.%d", retry);
        return -1;
    }
    return 0;
}

int16_t bin_firmware_parse(void)
{
    uint16_t i;
    // int16_t ret;
    uint32_t sum;
    uint8_t *pdata;

    HYN_FUNC_ENTER;
    if (!p_g_chip_obj->bin_data.data)
    {
        HYNITRON_ERROR("bin_firmware_parse data error return");
        return -1;
    }

    pdata = p_g_chip_obj->bin_data.data;
    p_g_chip_obj->bin_data.checksum = 0;
    // 0x7F6C-32620=32k-128-20=checksum
    // 0x7F80-32640=32k-128
    sum = 0x55;
    for (i = 0; i < 255; i++)
    {
#if HYN_ENABLE_UPGRADE_128_BYTE_WRITE
        if (p_g_chip_obj->get_bin_addr(i, MEM_128_SIZE) < 0)
        {
            HYNITRON_ERROR("get_bin_addr fail.");
            return -1;
        }
        pdata = p_g_chip_obj->bin_data.data;
#endif

        sum += firmware_verify_128bytes_bin_data(pdata, i);
        pdata += 128; // update bin point every 128 bytes
        if (i == 254)
        {
            pdata -= 20; // 0x7F6C
            if (sum != get_u32_from_ptr(pdata))
            {
                HYNITRON_ERROR("main checksum data error 0x%04x 0x%04x", sum, get_u32_from_ptr(pdata));
                return -1;
            }
            sum = 0;
            sum += get_u32_from_ptr(pdata - 4);  // 0x7F68
            sum += get_u32_from_ptr(pdata - 8);  // 0x7F64
            sum += get_u32_from_ptr(pdata - 12); // 0x7F60
            sum += get_u32_from_ptr(pdata - 16); // 0x7F5C

            if (sum != get_u32_from_ptr(pdata + 16)) // 0x7F7C
            {
                HYNITRON_ERROR("info checksum data error 0x%04x 0x%04x", sum, get_u32_from_ptr(pdata + 16));
                return -1;
            }
            p_g_chip_obj->bin_data.ok = TRUE;
            p_g_chip_obj->bin_data.checksum = get_u32_from_ptr(pdata + 0x7F6C - 0x7F6C);
            p_g_chip_obj->bin_data.chip_type = (get_u32_from_ptr(pdata + 0x7F64 - 0x7F6C) >> 16);
            p_g_chip_obj->bin_data.version = get_u32_from_ptr(pdata + 0x7F68 - 0x7F6C);
            p_g_chip_obj->bin_data.project_id = (get_u32_from_ptr(pdata + 0x7F64 - 0x7F6C) & 0x0000FFFF);
        }
    }

    HYN_FUNC_EXIT;
    return 0;
}

int16_t read_chip_info(void)
{
    int16_t ret = 0;
    uint8_t i2c_buf[4] = {0};
    uint32_t u32temp = 0;
    uint32_t sum = 0;

    HYN_FUNC_ENTER;
    p_g_chip_obj->IC_firmware.firmware_info_ok = 0;
    ret = enter_bootloader();
    if (ret)
    {
        HYNITRON_ERROR("enter_bootloader error");
        return -1;
    }
    p_g_chip_obj->delay1ms(10);
    // firmware ok
    i2c_buf[0] = 0xA0;
    i2c_buf[1] = 0x00;
    ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 2);
    ret |= p_g_chip_obj->i2c_read(I2C_BOOT_ADDR, i2c_buf, 1);
    if (ret)
    {
        HYNITRON_ERROR("read 0xA000 error");
        return -1;
    }
    if (i2c_buf[0] != 0x01)
    {
        HYNITRON_ERROR("get 0xA000 checksum error,maybe chip null");
        return -1;
    }
    // firmware checksum
    i2c_buf[0] = 0xA0;
    i2c_buf[1] = 0x08;
    ret = p_g_chip_obj->i2c_write(I2C_BOOT_ADDR, i2c_buf, 2);
    ret |= p_g_chip_obj->i2c_read(I2C_BOOT_ADDR, i2c_buf, 4);
    if (ret)
    {
        HYNITRON_ERROR("read 0xA008 error");
        return -1;
    }
    p_g_chip_obj->IC_firmware.firmware_checksum = ((uint32_t)(i2c_buf[0])) |
                                                  (((uint32_t)(i2c_buf[1])) << 8) |
                                                  (((uint32_t)(i2c_buf[2])) << 16) |
                                                  (((uint32_t)(i2c_buf[3])) << 24);
    // chip type
    ret = read_word_from_mem(0, 0x7F64, &p_g_chip_obj->IC_firmware.firmware_ic_type);
    if (ret)
    {
        HYNITRON_ERROR("read 0x7F64 error");
        return -1;
    }
    sum += p_g_chip_obj->IC_firmware.firmware_ic_type;
    // version
    ret = read_word_from_mem(0, 0x7F68, &p_g_chip_obj->IC_firmware.firmware_version);
    if (ret)
    {
        HYNITRON_ERROR("read 0x7F68 error");
        return -1;
    }
    sum += p_g_chip_obj->IC_firmware.firmware_version;
    // reserval
    ret = read_word_from_mem(0, 0x7F60, &u32temp);
    if (ret)
    {
        HYNITRON_ERROR("read 0x7F60 error");
        return -1;
    }
    sum += u32temp;
    // reserval
    ret = read_word_from_mem(0, 0x7F5C, &u32temp);
    if (ret)
    {
        HYNITRON_ERROR("read 0x7F5C error");
        return -1;
    }
    sum += u32temp;
    ret = read_word_from_mem(0, 0x7F7C, &u32temp);
    if (ret)
    {
        HYNITRON_ERROR("read 0x7F7C error");
        return -1;
    }
    if (sum == u32temp)
    {
        p_g_chip_obj->IC_firmware.firmware_info_ok = 1;
    }
    p_g_chip_obj->IC_firmware.firmware_project_id = p_g_chip_obj->IC_firmware.firmware_ic_type & 0x0000FFFF;
    p_g_chip_obj->IC_firmware.firmware_ic_type >>= 16;
    HYN_FUNC_EXIT;
    return 0;
}

int16_t read_point(void)
{
    int16_t ret = 0;
    uint8_t data_buf[HYNITRON_FINGER_NUM * 5 + 5] = {0};
    uint8_t finger_num = 0;

    // HYN_FUNC_ENTER;
    if (1)
    {
        uint8_t reg_buf[4] = {0};
        uint8_t len = sizeof(data_buf);
        uint8_t *data = data_buf;
        uint16_t reg = 0xD000;

        while (len > 0)
        {
            uint8_t cur_len = len;
            // if (cur_len > HYNITRON_I2C_TRAN_PER_SIZE) {
            //     cur_len = HYNITRON_I2C_TRAN_PER_SIZE;
            // }

            reg_buf[0] = reg >> 8;
            reg_buf[1] = reg;
            ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, reg_buf, 2);
            ret |= p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, reg_buf, 2);
            if (ret)
            {
                HYNITRON_ERROR("write 0xD000 error");
                return -1;
            }
            ret = p_g_chip_obj->i2c_read(HYNITRON_I2C_ADDR, data, cur_len);
            if (ret)
            {
                HYNITRON_ERROR("read 0xD000 error");
                return -1;
            }

            len -= cur_len;
            data += cur_len;
            reg += cur_len;
        }

        reg_buf[0] = 0xD0;
        reg_buf[1] = 0x24; // 00
        reg_buf[2] = 0xAB;
        ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, reg_buf, 3);
        if (ret)
        {
            HYNITRON_ERROR("write 0xD024AB error");
            return -1;
        }
    }

    if (data_buf[6] != 0xAB)
    {
        HYNITRON_ERROR("read data_buf[6] error 0x%x", data_buf[6]);
        return -1;
    }

    // palm + gesture
    if (data_buf[4] > 0)
    {
        HYNITRON_DEBUG("read point data_buf[4]=0x%x.\n", data_buf[4]);
        if (data_buf[4] & 0xF0)
        {
            if ((data_buf[4] >> 7) == 0X01)
            {
                p_g_chip_obj->touch_info.valid = TRUE;
                p_g_chip_obj->touch_info.palm = 0x01;
            }
            else if (data_buf[4] >> 4)
            {
                p_g_chip_obj->touch_info.valid = TRUE;
                p_g_chip_obj->touch_info.gesture = data_buf[4] >> 4;
            }
        }
    }

    finger_num = data_buf[5] & 0x7F;
    if (finger_num > HYNITRON_FINGER_NUM)
    {
        HYNITRON_ERROR("read finger_num error %d", finger_num);
        return -1;
    }
    p_g_chip_obj->touch_info.finger_num = finger_num;

    // button
    if ((data_buf[5] & 0x80) == 0x80)
    {
        uint8_t *data = data_buf + finger_num * 5;
        if (finger_num > 0)
        {
            data += 2;
        }
        p_g_chip_obj->touch_info.valid = TRUE;
        p_g_chip_obj->touch_info.key_id = data[0];
        p_g_chip_obj->touch_info.key_status = data[1];
    }

    if (finger_num > 0)
    {
        uint8_t *data = data_buf;
        uint8_t id = data[0] >> 4;
        uint8_t switch_ = data[0] & 0x0F;
        uint16_t x = ((uint16_t)(data[1]) << 4) | (data[3] >> 4);
        uint16_t y = ((uint16_t)(data[2]) << 4) | (data[3] & 0x0F);

#if (HYN_REPORT_TOUCH_CHECKSUM_EN)
        uint8_t checksum;
        checksum = 0;
        checksum = *(data + 0);
        checksum += *(data + 1);
        checksum += *(data + 2);
        checksum += *(data + 3);
        if ((checksum & 0x0f) != (*(data + 4) & 0x0f))
        {
            HYNITRON_ERROR("get point checksum error");
            return -1;
        }
#endif
        //HYNITRON_DEBUG("get raw point x %d,y %d,id %d,switch_ %d.", x, y, id, switch_);
        if (id < HYNITRON_FINGER_NUM)
        {
            p_g_chip_obj->point_info[id].evt = (switch_ == 0) ? 0 : 1;
            p_g_chip_obj->point_info[id].x = x;
            p_g_chip_obj->point_info[id].y = y;
            p_g_chip_obj->point_info[id].valid = TRUE;
        }
    }

    for (uint8_t i = 1; i < finger_num; i++)
    {
        uint8_t *data = data_buf + 5 * i + 2;
        uint8_t id = data[0] >> 4;
        uint8_t switch_ = data[0] & 0x0F;
        uint16_t x = ((uint16_t)(data[1]) << 4) | (data[3] >> 4);
        uint16_t y = ((uint16_t)(data[2]) << 4) | (data[3] & 0x0F);
#if (HYN_REPORT_TOUCH_CHECKSUM_EN)
        uint8_t checksum;
        checksum = 0;
        checksum = *(data + 0);
        checksum += *(data + 1);
        checksum += *(data + 2);
        checksum += *(data + 3);
        if ((checksum & 0x0f) != (*(data + 4) & 0x0f))
        {
            HYNITRON_ERROR("get point checksum error");
            return -1;
        }
#endif
        if (id < HYNITRON_FINGER_NUM)
        {
            p_g_chip_obj->point_info[id].evt = (switch_ == 0) ? 0 : 1;
            p_g_chip_obj->point_info[id].x = x;
            p_g_chip_obj->point_info[id].y = y;
            p_g_chip_obj->point_info[id].valid = TRUE;
        }
    }
    // HYN_FUNC_EXIT;
    return 0;
}

int16_t enter_sleep(uint8_t sleep_type)
{
    int16_t ret = -1;
    uint8_t i2c_buf[4] = {0};
    uint8_t read_buf[4] = {0};

    HYN_FUNC_ENTER;
    if (p_g_chip_obj->chip_ic_workmode != ENUM_MODE_NORMAL)
    {
        HYNITRON_ERROR("enter_sleep chip_ic_workmode error");
        return -1;
    }
    p_g_chip_obj->set_work_mode(0XFF);
    switch (sleep_type)
    {
    case DEEP_SLEEP:
        i2c_buf[0] = 0xD1;
        i2c_buf[1] = 0x05;
        p_g_chip_obj->chip_ic_workmode = ENUM_MODE_DEEP_SLEEP;
        p_g_chip_obj->status.esd_enable = FALSE;
        HYNITRON_DEBUG("enter ENUM_MODE_DEEP_SLEEP");
        break;
    case IDLE_SLEEP:
        i2c_buf[0] = 0xD1;
        i2c_buf[1] = 0x07;
        p_g_chip_obj->chip_ic_workmode = ENUM_MODE_LOW_POWER;
        HYNITRON_DEBUG("enter ENUM_MODE_LOW_POWER");
        break;
    case GESTURE_SLEEP:
        i2c_buf[0] = 0xD1;
        i2c_buf[1] = 0x04;
        p_g_chip_obj->chip_ic_workmode = ENUM_MODE_WAKEUP;
        HYNITRON_DEBUG("enter ENUM_MODE_WAKEUP");
        break;
    }
    ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
    ret |= p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
    if (ret)
    {
        HYNITRON_ERROR("enter_sleep write sleep cmd error");
        return -1;
    }
    read_buf[0] = 0x00;
    read_buf[1] = 0x02;
    ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, read_buf, 2);
    ret |= p_g_chip_obj->i2c_read(HYNITRON_I2C_ADDR, read_buf, 2);
    if (ret)
    {
        HYNITRON_ERROR("enter_sleep read 0x0002 error");
        return -1;
    }
    if (read_buf[1] != i2c_buf[1])
    {
        HYNITRON_ERROR("enter_sleep check sleep cmd error");
        return -1;
    }
    HYN_FUNC_EXIT;
    return 0;
}
int16_t wake_up(void)
{
    HYN_FUNC_ENTER;
    if (p_g_chip_obj->chip_ic_workmode == ENUM_MODE_UPDATE_FW)
    {
        HYNITRON_ERROR("wake_up chip_ic_workmode error");
        return -1;
    }
    p_g_chip_obj->reset_ic();
    p_g_chip_obj->delay1ms(30);
    if (p_g_chip_obj->get_firmware_info())
    {
        HYNITRON_ERROR("wake_up get_firmware_info error");
        p_g_chip_obj->chip_ic_workmode = ENUM_MODE_NORMAL;
        return -1;
    }
    p_g_chip_obj->chip_ic_workmode = ENUM_MODE_NORMAL;
    HYN_FUNC_EXIT;
    return 0;
}

int16_t read_chip_id(void)
{
    int16_t ret = 0;
    uint8_t retry = 3;
    // uint8_t i2c_buf[4] = {0};
    HYN_FUNC_ENTER;
    ret = enter_bootloader();
    if (ret)
    {
        HYNITRON_ERROR("enter_bootloader error");
        return -1;
    }
    for (; retry > 0; retry--)
    {
        // partno
        ret = read_word_from_mem(1, 0x077C, &p_g_chip_obj->partno_chip_type);
        if (ret)
        {
            continue;
        }
        // module id
        ret = read_word_from_mem(0, 0x7FC0, &p_g_chip_obj->module_id);
        if (ret)
        {
            continue;
        }
        if ((p_g_chip_obj->partno_chip_type >> 16) == 0xCACA)
        {
            p_g_chip_obj->partno_chip_type &= 0xffff;
            break;
        }
    }
    p_g_chip_obj->reset_ic();
    p_g_chip_obj->delay1ms(30);

    HYNITRON_DEBUG("partno_chip_type: 0x%04x", p_g_chip_obj->partno_chip_type);
    HYNITRON_DEBUG("module_id: 0x%04x", p_g_chip_obj->module_id);
    if ((p_g_chip_obj->partno_chip_type != 0x9217) && (p_g_chip_obj->partno_chip_type != 0x9220))
    {
        HYNITRON_ERROR("partno_chip_type error 0x%04x", p_g_chip_obj->partno_chip_type);
        return -1;
    }
    HYN_FUNC_EXIT;
    return 0;
}

int16_t get_firmware_info(void)
{
    uint8_t i2c_buf[6] = {0};
    uint32_t version = 0;
    int16_t ret = -1;

    HYN_FUNC_ENTER;
    p_g_chip_obj->set_work_mode(ENUM_MODE_DEBUG_INFO);
    p_g_chip_obj->delay1ms(1);
    i2c_buf[0] = 0xD1;
    i2c_buf[1] = 0x01;
    ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
    ret |= p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
    if (ret)
    {
        HYNITRON_ERROR("write 0xD101 error");
        return -1;
    }
    // firmware_project_id   firmware_ic_type
    i2c_buf[0] = 0xD2;
    i2c_buf[1] = 0x04;
    ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
    ret |= p_g_chip_obj->i2c_read(HYNITRON_I2C_ADDR, i2c_buf, 4);
    if (ret)
    {
        HYNITRON_ERROR("read 0xD204 error");
        return -1;
    }
    p_g_chip_obj->IC_firmware.firmware_project_id = ((uint16_t)i2c_buf[1] << 8) + i2c_buf[0];
    p_g_chip_obj->IC_firmware.firmware_ic_type = ((uint16_t)i2c_buf[3] << 8) + i2c_buf[2];

    // firmware_version
    i2c_buf[0] = 0xD2;
    i2c_buf[1] = 0x08;
    ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
    ret |= p_g_chip_obj->i2c_read(HYNITRON_I2C_ADDR, i2c_buf, 4);
    if (ret)
    {
        HYNITRON_ERROR("read 0xD208 error");
        return -1;
    }
    version |= i2c_buf[3];
    version <<= 8;
    version |= i2c_buf[2];
    version <<= 8;
    version |= i2c_buf[1];
    version <<= 8;
    version |= i2c_buf[0];
    p_g_chip_obj->IC_firmware.firmware_version = version;

    // firmware_checksum
    i2c_buf[0] = 0xD2;
    i2c_buf[1] = 0x0C;
    ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
    ret |= p_g_chip_obj->i2c_read(HYNITRON_I2C_ADDR, i2c_buf, 4);
    if (ret)
    {
        HYNITRON_ERROR("read 0xD20c error");
        return -1;
    }
    p_g_chip_obj->IC_firmware.firmware_checksum = ((uint32_t)i2c_buf[3] << 24) + ((uint32_t)i2c_buf[2] << 16) + ((uint32_t)i2c_buf[1] << 8) + i2c_buf[0];
    // tx_num   rx_num   key_num
    i2c_buf[0] = 0xD1;
    i2c_buf[1] = 0xF4;
    ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
    ret |= p_g_chip_obj->i2c_read(HYNITRON_I2C_ADDR, i2c_buf, 4);
    if (ret)
    {
        HYNITRON_ERROR("read 0xD1F4 error");
        return -1;
    }
    p_g_chip_obj->IC_firmware.tx_num = ((uint16_t)i2c_buf[1] << 8) + i2c_buf[0];
    p_g_chip_obj->IC_firmware.rx_num = i2c_buf[2];
    p_g_chip_obj->IC_firmware.key_num = i2c_buf[3];

    // go back normal mode
    p_g_chip_obj->set_work_mode(ENUM_MODE_NORMAL);

    HYNITRON_DEBUG("chip firmware_ic_type: 0x%04x", p_g_chip_obj->IC_firmware.firmware_ic_type);
    HYNITRON_DEBUG("chip firmware_version: 0x%04x", p_g_chip_obj->IC_firmware.firmware_version);
    HYNITRON_DEBUG("chip firmware_project_id: 0x%04x", p_g_chip_obj->IC_firmware.firmware_project_id);
    HYNITRON_DEBUG("chip checksum: 0x%04x", p_g_chip_obj->IC_firmware.firmware_checksum);
    HYNITRON_DEBUG("chip tx_num: %d", p_g_chip_obj->IC_firmware.tx_num);
    HYNITRON_DEBUG("chip rx_num: %d", p_g_chip_obj->IC_firmware.rx_num);
    HYNITRON_DEBUG("chip key_num: %d", p_g_chip_obj->IC_firmware.key_num);
    HYN_FUNC_EXIT;
    return 0;
}

int16_t set_work_mode(uint8_t mode)
{
    uint8_t i2c_buf[4] = {0};
    uint8_t i = 0;
    int16_t ret = -1;

    HYN_FUNC_ENTER;
    p_g_chip_obj->chip_ic_workmode = mode;
    for (i = 0; i < 5; i++)
    {
        i2c_buf[0] = 0xD1;
        i2c_buf[1] = 0x1e;
        ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
        if (ret)
        {
            continue;
        }
        i2c_buf[0] = 0xD1;
        i2c_buf[1] = 0x1E;
        ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
        if (ret)
        {
            continue;
        }
        i2c_buf[0] = 0x00;
        i2c_buf[1] = 0x02;
        ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
        ret |= p_g_chip_obj->i2c_read(HYNITRON_I2C_ADDR, i2c_buf, 2);
        if (ret)
        {
            continue;
        }
        if (i2c_buf[1] == 0x1E)
            break;
    }
    if (mode == ENUM_MODE_DEBUG_DIFF)
    {
        HYNITRON_DEBUG("set_work_mode: ENUM_MODE_DEBUG_DIFF");
        i2c_buf[0] = 0xD1;
        i2c_buf[1] = 0x0D;
        ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
        if (ret)
        {
            HYNITRON_ERROR("set_work_mode ENUM_MODE_DEBUG_DIFF error");
            return -1;
        }
    }
    else if (mode == ENUM_MODE_DEBUG_RAWDATA)
    {
        HYNITRON_DEBUG("set_work_mode: ENUM_MODE_DEBUG_RAWDATA");
        i2c_buf[0] = 0xD1;
        i2c_buf[1] = 0x0A;
        ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
        if (ret)
        {
            HYNITRON_ERROR("set_work_mode ENUM_MODE_DEBUG_RAWDATA error");
            return -1;
        }
    }
    else if (mode == ENUM_MODE_FACTORY)
    {
        HYNITRON_DEBUG("set_work_mode: ENUM_MODE_FACTORY");
        for (i = 0; i < 20; i++)
        {
            i2c_buf[0] = 0xD1;
            i2c_buf[1] = 0x14;
            ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
            if (ret)
            {
                continue;
            }

            i2c_buf[0] = 0x00;
            i2c_buf[1] = 0x09;
            ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
            ret |= p_g_chip_obj->i2c_read(HYNITRON_I2C_ADDR, i2c_buf, 1);
            if (ret)
            {
                continue;
            }
            p_g_chip_obj->delay1ms(1);
            if (i2c_buf[0] == 0x14)
                break;
        }
        for (i = 0; i < 2; i++)
        {
            i2c_buf[0] = 0xD1;
            i2c_buf[1] = 0x19;
            ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
            if (ret)
            {
                HYNITRON_ERROR("set_work_mode ENUM_MODE_FACTORY error");
                return -1;
            }
            p_g_chip_obj->delay1ms(1);
        }
    }
    else if (mode == TEST_SNS_OPEN_LOWDRV)
    {
        HYNITRON_DEBUG("set_work_mode: TEST_SNS_OPEN_LOWDRV");
        p_g_chip_obj->delay1ms(2);
        i2c_buf[0] = 0xD1;
        i2c_buf[1] = 0x11;
        ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
        if (ret)
        {
            HYNITRON_ERROR("set_work_mode TEST_SNS_OPEN_LOWDRV error");
            return -1;
        }
    }
    else if (mode == TEST_SNS_OPEN_HIGHDRV)
    {
        HYNITRON_DEBUG("set_work_mode: TEST_SNS_OPEN_HIGHDRV");
        p_g_chip_obj->delay1ms(2);
        i2c_buf[0] = 0xD1;
        i2c_buf[1] = 0x10;
        ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
        if (ret)
        {
            HYNITRON_ERROR("set_work_mode TEST_SNS_OPEN_HIGHDRV error");
            return -1;
        }
    }
    else if (mode == TEST_SNS_SHORT)
    {
        HYNITRON_DEBUG("set_work_mode: TEST_SNS_SHORT");
        p_g_chip_obj->delay1ms(2);
        i2c_buf[0] = 0xD1;
        i2c_buf[1] = 0x12;
        ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
        if (ret)
        {
            HYNITRON_ERROR("set_work_mode TEST_SNS_SHORT error");
            return -1;
        }
    }
    else if (mode == ENUM_MODE_DEBUG_INFO)
    {
        HYNITRON_DEBUG("set_work_mode: ENUM_MODE_DEBUG_INFO");
        p_g_chip_obj->delay1ms(2);
        i2c_buf[0] = 0xD1;
        i2c_buf[1] = 0x01;
        ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
        if (ret)
        {
            HYNITRON_ERROR("set_work_mode ENUM_MODE_DEBUG_INFO error");
            return -1;
        }
    }
    else if (mode == ENUM_MODE_NORMAL)
    {
        HYNITRON_DEBUG("set_work_mode: ENUM_MODE_NORMAL");
        i2c_buf[0] = 0xD1;
        i2c_buf[1] = 0x09;
        ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
        if (ret)
        {
            HYNITRON_ERROR("set_work_mode ENUM_MODE_NORMAL error");
            return -1;
        }
    }
    p_g_chip_obj->delay1ms(30);
    HYN_FUNC_EXIT;
    return 0;
}

int16_t get_irq_signal(void)
{
    // HYNITRON_DEBUG("touch_int_flag %d ",p_g_chip_obj->touch_int_flag);
    if (p_g_chip_obj->status.int_trig)
    {
        p_g_chip_obj->status.int_trig = 0;
        HYNITRON_DEBUG("get_irq_signal get irq event");
        return 0;
    }
    else
    {
        HYNITRON_DEBUG("get_irq_signal no irq event");
        return -1;
    }
}

int16_t get_diff_data(uint8_t *buff, uint16_t data_len)
{
    int16_t ret = -1;
    uint8_t i2c_buf[4];
    uint16_t i = 0;
    uint16_t j = 0;
    uint16_t node_num = 0;
    uint16_t time_out;
    union hyn_core
    {
        uint8_t buff_u8[TRX_NUM * 2];
        int16_t buff_s16[TRX_NUM];
    } diff = {.buff_s16 = 0};

    HYN_FUNC_ENTER;
    if ((p_g_chip_obj->status.sleep_done > 0) || (p_g_chip_obj->status.update_fw_running == 1) || (p_g_chip_obj->status.ic_init_done == 0))
    {
        HYNITRON_DEBUG("get_diff_data status NA return");
        return -1;
    }
    p_g_chip_obj->reset_ic();
    p_g_chip_obj->delay1ms(40);
    ret = p_g_chip_obj->get_firmware_info();
    if (ret)
    {
        HYNITRON_ERROR("get_firmware_info failed");
        return -1;
    }
    p_g_chip_obj->set_work_mode(ENUM_MODE_DEBUG_DIFF);
    p_g_chip_obj->delay1ms(20);
    if (p_g_chip_obj->IC_firmware.key_num == 0)
    {
        node_num = p_g_chip_obj->IC_firmware.tx_num * p_g_chip_obj->IC_firmware.rx_num;
    }
    else
    {
        node_num = (p_g_chip_obj->IC_firmware.tx_num - 1) * p_g_chip_obj->IC_firmware.rx_num + p_g_chip_obj->IC_firmware.key_num;
    }
    time_out = 1000;
    while (time_out--)
    {
        if (!get_irq_signal())
            break;
        p_g_chip_obj->delay1ms(10);
    }
    i2c_buf[0] = 0x10;
    i2c_buf[1] = 0x00;
    ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
    ret |= p_g_chip_obj->i2c_read(HYNITRON_I2C_ADDR, diff.buff_u8, node_num * 2);
    if (ret)
    {
        HYNITRON_ERROR("read 0x1000 error");
        return -1;
    }
    i2c_buf[0] = 0x00;
    i2c_buf[1] = 0x05;
    i2c_buf[2] = 0x00;
    ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 3);
    if (ret)
    {
        HYNITRON_ERROR("write 0x000500 error");
        return -1;
    }
    for (i = 0, j = 0; i < node_num; i++)
    {
        HYNITRON_DEBUG("%d ", diff.buff_s16[i]);
        j++;
        if (j >= p_g_chip_obj->IC_firmware.rx_num)
        {
            j -= p_g_chip_obj->IC_firmware.rx_num;
            HYNITRON_DEBUG("");
        }
    }
    HYNITRON_DEBUG("");
    p_g_chip_obj->set_work_mode(ENUM_MODE_NORMAL);
    HYN_FUNC_EXIT;
    return 0;
}
int16_t get_rawdata_data(uint8_t *buff, uint16_t data_len)
{
    int16_t ret = -1;
    uint8_t i2c_buf[4];
    uint16_t i = 0;
    uint16_t j = 0;
    uint16_t node_num = 0;
    uint16_t time_out;

    union hyn_core
    {
        uint8_t buff_u8[TRX_NUM * 2];
        uint16_t buff_u16[TRX_NUM];
    } rawdata = {.buff_u16 = 0};

    HYN_FUNC_ENTER;
    if ((p_g_chip_obj->status.sleep_done > 0) || (p_g_chip_obj->status.update_fw_running == 1) || (p_g_chip_obj->status.ic_init_done == 0))
    {
        HYNITRON_ERROR("get_rawdata_data status NA return");
        return -1;
    }
    p_g_chip_obj->reset_ic();
    p_g_chip_obj->delay1ms(40);
    ret = p_g_chip_obj->get_firmware_info();
    if (ret)
    {
        HYNITRON_ERROR("get_firmware_info failed");
        return -1;
    }
    p_g_chip_obj->set_work_mode(ENUM_MODE_DEBUG_RAWDATA);
    p_g_chip_obj->delay1ms(20);
    if (p_g_chip_obj->IC_firmware.key_num == 0)
    {
        node_num = p_g_chip_obj->IC_firmware.tx_num * p_g_chip_obj->IC_firmware.rx_num;
    }
    else
    {
        node_num = (p_g_chip_obj->IC_firmware.tx_num - 1) * p_g_chip_obj->IC_firmware.rx_num + p_g_chip_obj->IC_firmware.key_num;
    }
    time_out = 1000;
    while (time_out--)
    {
        if (!get_irq_signal())
            break;
        p_g_chip_obj->delay1ms(10);
    }
    i2c_buf[0] = 0x10;
    i2c_buf[1] = 0x00;
    ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
    ret |= p_g_chip_obj->i2c_read(HYNITRON_I2C_ADDR, rawdata.buff_u8, node_num * 2);
    if (ret)
    {
        HYNITRON_ERROR("read 0x1000 error");
        return -1;
    }
    i2c_buf[0] = 0x00;
    i2c_buf[1] = 0x05;
    i2c_buf[2] = 0x00;
    ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 3);
    if (ret)
    {
        HYNITRON_ERROR("write 0x000500 error");
        return -1;
    }
    for (i = 0, j = 0; i < node_num; i++)
    {
        HYNITRON_DEBUG("%d ", rawdata.buff_u16[i]);
        j++;
        if (j >= p_g_chip_obj->IC_firmware.rx_num)
        {
            j -= p_g_chip_obj->IC_firmware.rx_num;
            HYNITRON_DEBUG("");
        }
    }
    HYNITRON_DEBUG("");
    p_g_chip_obj->set_work_mode(ENUM_MODE_NORMAL);
    HYN_FUNC_EXIT;
    return 0;
}

int get_factory_test_data(uint8_t mode, uint8_t *buff, uint16_t data_len)
{
    uint8_t i2c_buf[4];
    int16_t ret = -1;

    HYN_FUNC_ENTER;
    if (mode == TEST_SNS_OPEN_LOWDRV)
    {
        i2c_buf[0] = 0x10;
        i2c_buf[1] = 0x00;
        ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
        ret |= p_g_chip_obj->i2c_read(HYNITRON_I2C_ADDR, buff, data_len);
        if (ret)
        {
            HYNITRON_ERROR("read 0x1000 error");
            return -1;
        }
    }
    else if (mode == TEST_SNS_OPEN_HIGHDRV)
    {
        i2c_buf[0] = 0x30;
        i2c_buf[1] = 0x00;
        ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
        ret |= p_g_chip_obj->i2c_read(HYNITRON_I2C_ADDR, buff, data_len);
        if (ret)
        {
            HYNITRON_ERROR("read 0x3000 error");
            return -1;
        }
    }
    else if (mode == TEST_SNS_SHORT)
    {
        i2c_buf[0] = 0x50;
        i2c_buf[1] = 0x00;
        ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
        ret |= p_g_chip_obj->i2c_read(HYNITRON_I2C_ADDR, buff, data_len);
        if (ret)
        {
            HYNITRON_ERROR("read 0x5000 error");
            return -1;
        }
    }

    i2c_buf[0] = 0x00;
    i2c_buf[1] = 0x05;
    i2c_buf[2] = 0x00;
    ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 3);
    if (ret)
    {
        HYNITRON_ERROR("write 0x000500 error");
        return -1;
    }
    HYN_FUNC_EXIT;
    return 0;
}

// add test threshold array
const uint16_t factory_test_threshold[TRX_NUM] =
    {
        400, 400, 400, 400, 400, 400, 400, 400, 400, 400,
        400, 400, 400, 400, 400, 400, 400, 400, 400, 400,
        400, 400, 400, 400, 400, 400, 400, 400, 400, 400,
        400, 400, 400, 400, 400, 400, 400, 400, 400, 400,
        400, 400, 400, 400, 400, 400, 400, 400, 400, 400,
        400, 400, 400, 400, 400, 400, 400, 400, 400, 400,
        400, 400, 400, 400, 400, 400, 400, 400, 400, 400,
        400, 400, 400, 400, 400, 400, 400, 400, 400, 400,
        400, 400, 400, 400, 400, 400, 400, 400, 400, 400,
        400, 400, 400, 400, 400, 400, 400, 400, 400, 400};
#define HYN_CHECK_FACTORY_RATIO_MIN 50
#define HYN_CHECK_FACTORY_RATIO_MAX 150

#define HYN_CHECK_FACTORY_SPECIAL_SENSOR_ENABLE 0

#if HYN_CHECK_FACTORY_SPECIAL_SENSOR_ENABLE
uint8_t white_node[12] = {0, 1, 7, 8, 9, 17, 63, 71, 72, 73, 79, 80};
int16_t check_white_node(uint8_t i)
{
    uint8_t idx;
    for (idx = 0; idx < 12; idx++)
    {
        if ((white_node[idx] == i) && (white_node[idx] < 100))
        {
            return -1;
        }
    }
    return 0;
}
#endif
int16_t get_factory_test_result(void)
{
    uint16_t node_num = 0;
    uint16_t time_out = 1000;
    uint16_t i = 0;
    uint16_t j = 0;
    uint8_t total_sensor;
    int16_t ret;

    union
    {
        uint8_t buff_u8[TRX_NUM * 2];
        int16_t buff_u16[TRX_NUM];
    } open_higdrv = {.buff_u16 = 0};

    union
    {
        uint8_t buff_u8[(TRX_KEY_NUM) * 2];
        int16_t buff_u16[TRX_KEY_NUM];
    } sns_short = {.buff_u16 = 0};

    HYN_FUNC_ENTER;

    if ((p_g_chip_obj->status.sleep_done > 0) || (p_g_chip_obj->status.update_fw_running == 1) || (p_g_chip_obj->status.ic_init_done == 0))
    {
        HYNITRON_DEBUG("get_factory_test_result status NA return");
        return -1;
    }
    p_g_chip_obj->reset_ic();
    p_g_chip_obj->delay1ms(40);
    ret = p_g_chip_obj->get_firmware_info();
    if (ret)
    {
        HYNITRON_ERROR("get_firmware_info failed");
        return -1;
    }

    total_sensor = (p_g_chip_obj->IC_firmware.tx_num + p_g_chip_obj->IC_firmware.rx_num + p_g_chip_obj->IC_firmware.key_num);
    if (p_g_chip_obj->IC_firmware.key_num == 0)
    {
        node_num = p_g_chip_obj->IC_firmware.tx_num * p_g_chip_obj->IC_firmware.rx_num;
    }
    else
    {
        node_num = (p_g_chip_obj->IC_firmware.tx_num - 1) * p_g_chip_obj->IC_firmware.rx_num + p_g_chip_obj->IC_firmware.key_num;
    }

    if ((p_g_chip_obj->IC_firmware.tx_num > 10) || (p_g_chip_obj->IC_firmware.rx_num > 10))
    {
        HYNITRON_ERROR("IC_firmware.tx_num/rx_num ERROR");
        return -1;
    }
    // get open hight result
    set_work_mode(TEST_SNS_OPEN_HIGHDRV);
    time_out = 400;
    while (time_out--)
    {
        if (!get_irq_signal())
            break;
        p_g_chip_obj->delay1ms(10);
    }
    get_factory_test_data(TEST_SNS_OPEN_HIGHDRV, open_higdrv.buff_u8, node_num * 2);

    HYNITRON_DEBUG("---open_higdrv---");
    for (i = 0, j = 0; i < node_num; i++)
    {
        HYNITRON_DEBUG("%d ", open_higdrv.buff_u16[i]);
        j++;
        if (j >= p_g_chip_obj->IC_firmware.rx_num)
        {
            j -= p_g_chip_obj->IC_firmware.rx_num;
            HYNITRON_DEBUG("");
        }
    }

    // get short result
    set_work_mode(TEST_SNS_SHORT);
    time_out = 400;
    while (time_out--)
    {
        if (!get_irq_signal())
            break;
        p_g_chip_obj->delay1ms(10);
    }
    get_factory_test_data(TEST_SNS_SHORT, sns_short.buff_u8, (total_sensor * 2));
    HYNITRON_DEBUG("---sns_short---");
    for (i = 0, j = 0; i < (total_sensor); i++)
    {
        HYNITRON_DEBUG("%d ", sns_short.buff_u16[i]);
        j++;
        if ((j == p_g_chip_obj->IC_firmware.rx_num) ||
            (j == (p_g_chip_obj->IC_firmware.rx_num + p_g_chip_obj->IC_firmware.tx_num)) ||
            (j == total_sensor))
        {
            HYNITRON_DEBUG("");
        }
    }

    // check open result
    for (i = 0; i < node_num; i++)
    {
        uint16_t factory_test_threshold_min;
        uint16_t factory_test_threshold_max;
#if HYN_CHECK_FACTORY_SPECIAL_SENSOR_ENABLE
        if (check_white_node(i))
        {
            HYNITRON_DEBUG("check_white_node %d continue", i);
            continue;
        }
#endif
        factory_test_threshold_min = factory_test_threshold[i] * HYN_CHECK_FACTORY_RATIO_MIN / 100;
        factory_test_threshold_max = factory_test_threshold[i] * HYN_CHECK_FACTORY_RATIO_MAX / 100;
        if (open_higdrv.buff_u16[i] < factory_test_threshold_min) //
        {
            HYNITRON_ERROR("check open_higdrv MIN ERROR:%d-%d-min-%d", i, open_higdrv.buff_u16[i], factory_test_threshold_max);
            ret = -1; // return -1;
        }
        if (open_higdrv.buff_u16[i] > factory_test_threshold_max) //
        {
            HYNITRON_ERROR("check open_higdrv MAX ERROR:%d-%d-max-%d", i, open_higdrv.buff_u16[i], factory_test_threshold_max);
            ret = -1; // return -1;
        }
    }

    // check short result
    for (i = 0; i < total_sensor; i++)
    {
        double resis = 0;
        if (sns_short.buff_u16[i] == 0)
        {
            HYNITRON_ERROR("check short ERROR NULL:%d-%d", i, sns_short.buff_u16[i]);
            ret = -1; // return -1;
        }
        resis = 1.0 * 2000000 / sns_short.buff_u16[i];
        if (resis < SHORT_LOW_TH)
        {
            HYNITRON_ERROR("check short ERROR:%d-%d", i, sns_short.buff_u16[i]);
            ret = -1; // return -1;
        }
    }
    p_g_chip_obj->reset_ic();
    p_g_chip_obj->delay1ms(40);
    HYN_FUNC_EXIT;
    return ret;
}

int16_t upgrade_firmware_judge(void)
{

    HYN_FUNC_ENTER;
    if (!p_g_chip_obj->bin_data.ok)
    {
        HYNITRON_ERROR("p_g_chip_obj->bin_data.ok %d is not ok.", p_g_chip_obj->bin_data.ok);
        return -1;
    }
    if (p_g_chip_obj->partno_chip_type != p_g_chip_obj->bin_data.chip_type)
    {
        HYNITRON_ERROR("partno chip type != bin data chip type");
        return -1;
    }
    if (p_g_chip_obj->IC_firmware.firmware_info_ok == 0)
    {
        HYNITRON_DEBUG("IC_firmware.firmware_info_ok error,need force update.");
        return 0;
    }
    else
    {
        if (p_g_chip_obj->IC_firmware.firmware_project_id != p_g_chip_obj->bin_data.project_id)
        {
            HYNITRON_ERROR("firmware_project_id != bin_data.firmware_project_id,no need update");
            return -1;
        }
        if (p_g_chip_obj->IC_firmware.firmware_checksum == p_g_chip_obj->bin_data.checksum)
        {
            HYNITRON_ERROR("firmware_checksum == bin_data.checksum,no need update.");
            return -1;
        }
        else
        {

            if (p_g_chip_obj->IC_firmware.firmware_version <= p_g_chip_obj->bin_data.version)
            {
                HYNITRON_DEBUG("firmware_version is lower than bin_data.version,need update");
                return 0;
            }
            else
            {
                HYNITRON_ERROR("firmware_version is higher,no need update");
            }
        }
    }
    HYN_FUNC_EXIT;
    return -1;
}

void esd_check(void)
{
    int16_t ret = -1;
    uint8_t i2c_buf[6], retry;
    uint8_t flag = 0;
    uint16_t checksum = 0;

    // HYN_FUNC_ENTER;
    if (p_g_chip_obj->chip_ic_workmode == ENUM_MODE_DEEP_SLEEP)
    {
        HYNITRON_ERROR("esd_check ENUM_MODE_DEEP_SLEEP return ");
        return;
    }
    // 20231208- only close esd check when deepsleep
    if ((p_g_chip_obj->status.esd_enable == 0) || (p_g_chip_obj->status.factory_test == 1) || (p_g_chip_obj->status.update_fw_running == 1) || (p_g_chip_obj->status.ic_init_done == 0))
    {
        HYNITRON_DEBUG("esd_check status NA return");
        return;
    }
    retry = 4;
    flag = 0;
    while (retry--)
    {
        i2c_buf[0] = 0xD0;
        i2c_buf[1] = 0x40;
        ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
        if (ret)
        {
            p_g_chip_obj->delay1ms(1);
            continue;
        }
        p_g_chip_obj->delay1ms(1);
        ret = p_g_chip_obj->i2c_read(HYNITRON_I2C_ADDR, i2c_buf, 6);
        if (ret)
        {
            p_g_chip_obj->delay1ms(1);
            continue;
        }
        else
        {
            checksum = i2c_buf[0] + i2c_buf[1] + i2c_buf[2] + i2c_buf[3] + 0xA5;
            p_g_chip_obj->esd_value = (i2c_buf[0] << 24) + (i2c_buf[1] << 16) + (i2c_buf[2] << 8) + i2c_buf[3];
            flag = 1;
            if (((i2c_buf[4] << 8) + i2c_buf[5]) == checksum)
            {
                flag = 2;
                if (((p_g_chip_obj->esd_value - p_g_chip_obj->esd_value_pre) < 1) && (p_g_chip_obj->esd_value > 20))
                {
                    flag = 3;
                }
                break;
            }
            else
            {
                HYNITRON_DEBUG("ESD data checksum err:0x%x,0x%x", p_g_chip_obj->esd_value, checksum);
                continue;
            }
        }
    }
    HYNITRON_DEBUG("ESD data:%d,0x%04x,0x%04x", flag, p_g_chip_obj->esd_value, p_g_chip_obj->esd_value_pre);

    if ((flag == 0) || (flag == 3) || (flag == 1))
    {
        p_g_chip_obj->poweron_ic(FALSE);
        p_g_chip_obj->delay1ms(10);
        p_g_chip_obj->poweron_ic(TRUE);
        p_g_chip_obj->delay1ms(10);
        p_g_chip_obj->reset_ic();
        p_g_chip_obj->delay1ms(40);
        p_g_chip_obj->esd_value = 0;
        p_g_chip_obj->esd_value_pre = 0;
        HYNITRON_DEBUG("esd_check power reset ic");
        // 20231208- need to re write sleep mode cmd
        if (p_g_chip_obj->chip_ic_workmode == ENUM_MODE_WAKEUP)
        {
            ret = p_g_chip_obj->enter_sleep(ENUM_MODE_WAKEUP);
            if (ret)
            {
                HYNITRON_ERROR("enter_sleep failed");
            }
        }
    }
    p_g_chip_obj->esd_value_pre = p_g_chip_obj->esd_value;
    HYN_FUNC_EXIT;
}

void hyn_cst92xx_init_obj(struct hyn_chip *chip)
{
    chip->chip_type = HYN_CHIP_TYPE;
    chip->read_chip_id = read_chip_id;
    chip->read_chip_info = read_chip_info;
    chip->bin_data_parse = bin_firmware_parse;
    chip->upgrade_firmware_judge = upgrade_firmware_judge;
    chip->upgrade_firmware = upgrade_firmware;
    chip->read_point = read_point;
    chip->enter_sleep = enter_sleep;
    chip->wake_up = wake_up;
    chip->esd_check = esd_check;
    chip->get_firmware_info = get_firmware_info;
    chip->set_work_mode = set_work_mode;
    chip->get_diff_data = get_diff_data;
    chip->get_rawdata_data = get_rawdata_data;
    chip->get_factory_test_result = get_factory_test_result;
}
