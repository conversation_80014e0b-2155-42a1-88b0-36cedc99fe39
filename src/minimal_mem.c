//*****************************************************************************
//
//! @file minimal_driver_example.c
//!
//! @brief Minimal driver implementation example
//!
//*****************************************************************************

#include <stdlib.h> // For standard malloc/free
#include "lv_conf.h"

#include "minimal_mem.h"
#include "am_mcu_apollo.h"
#include "am_bsp.h"
#include "am_util.h"

#define PSRAM_BASE_ADDR         0x14000000
#define PSRAM_SIZE              (8 * 1024 * 1024)

static uint32_t g_psramOffset = 0;

void* minimal_psram_malloc(size_t size)
{
    // Simple bump allocator - not suitable for production!
    // Implement proper memory management for real use
    
    // Align to 32-byte boundary for cache efficiency
    size = (size + 31) & ~31;
    
    // Check if we have enough memory
    if (g_psramOffset + size > PSRAM_SIZE) {
        am_util_stdio_printf("PSRAM out of memory! Requested: %d bytes, Available: %d bytes, Used: %d bytes\n", 
                            size, PSRAM_SIZE - g_psramOffset, g_psramOffset);
        return NULL;
    }
    
    void* ptr = (void*)(PSRAM_BASE_ADDR + g_psramOffset);
    g_psramOffset += size;
    
    am_util_stdio_printf("PSRAM alloc: %d bytes at 0x%08X\n", size, (uint32_t)ptr);
    return ptr;
}

void minimal_psram_free(void* ptr)
{
    // Simple allocator doesn't support free
    // Implement proper free() for production use
    am_util_stdio_printf("PSRAM free at 0x%08X\n", (uint32_t)ptr);
    (void)ptr;
}

void* minimal_psram_realloc(void* ptr, size_t new_size)
{
    // Simple allocator doesn't support realloc properly
    // For now, just allocate new memory and copy data
    // This is not efficient but works for prototyping
    am_util_stdio_printf("PSRAM realloc: 0x%08X -> %d bytes\n", (uint32_t)ptr, new_size);
    
    // Allocate new memory
    void* new_ptr = minimal_psram_malloc(new_size);
    if (new_ptr == NULL) {
        return NULL;
    }
    
    // Note: We can't copy old data because we don't track sizes
    // This is a limitation of the simple bump allocator
    am_util_stdio_printf("Warning: realloc data not preserved (bump allocator limitation)\n");
    
    return new_ptr;
}

// Define custom memory functions only if LV_MEM_CUSTOM is enabled.
#if LV_MEM_CUSTOM

void lv_mem_custom_init(void) {
    am_util_stdio_printf("lv_mem_custom_init\n");
}

void *lv_mem_custom_alloc(size_t size) {
    am_util_stdio_printf("lv_mem_custom_alloc\n");
    return minimal_psram_malloc(size);
}

void lv_mem_custom_free(void *ptr) {
    am_util_stdio_printf("lv_mem_custom_free\n");
    minimal_psram_free(ptr);
}

#endif