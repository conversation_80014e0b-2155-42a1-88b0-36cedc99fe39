#include "am_mcu_apollo.h"
#include "am_bsp.h"
#include "am_util.h"
#include "am_hal_global.h"

#include "board_init.h"

#include "AMOLED.h"
#include "aps12808l.h"
#include "hynitron_core.h"
#include "I2C.h"
#include "sh8601_apollo4.h"
#include "sh8601_example.h"
#include "UART.h"


#define PWM_CLK     AM_HAL_TIMER_CLOCK_HFRC_DIV16
#define PWM_FREQ    200
#define FREQ_FRAC_SCALING 128
#define LFRC_TCIKS_SEC 900
//
//! using this timer for the lower freq duty cycle update timer
//
#define LED_CYCLE_TIMER 2

#define PSRAM_BASE   0x14000000UL
#define TEST_ADDR    (PSRAM_BASE + 0x1000)   // pick an offset

volatile uint8_t timer_flag=0;
volatile uint8_t timer_acc=0;

uart_async_local_vars_t g_localv;

static void psram_test(void) {
    volatile uint32_t *ptr = (uint32_t *)TEST_ADDR;

    *ptr = 0xA5A5A5A5;             // write test pattern
    uint32_t val = *ptr;           // read it back

    if (val == 0xA5A5A5A5) {
        am_util_stdio_printf("PSRAM XIP access OK (0x%08X)\n", val);
    } else {
        am_util_stdio_printf("PSRAM XIP access FAILED (read 0x%08X)\n", val);
    }
}

void
am_timer02_isr(void)
{
    am_hal_timer_interrupt_clear(AM_HAL_TIMER_MASK(LED_CYCLE_TIMER, AM_HAL_TIMER_COMPARE1));
    am_hal_timer_clear(LED_CYCLE_TIMER);
    //
    // set flag so background loop can take action
    //
    g_localv.bTimerIntOccured = true;
		//timer_flag=1;
		timer_acc+=1;
		if (timer_acc > 2)//设置N次中断后允许GPIO中断响应触摸屏幕中断读取数据，否则点击屏幕一次触发多次中断。
		{
			timer_acc=0;
			timer_flag=1;
		}
		else
		{
			//timer_flag=0;
		}
}

//*****************************************************************************
//
//! @brief Start Update Timer
//! This timer is used to update the duty cycle
//!
//! @param ui32TimerNumber the pwm freq * 128, the fractional scaling
//!
//! @return standard hal status
//
//*****************************************************************************
static uint32_t
init_timer(uint32_t ui32TimerNumber)
{
    am_hal_timer_config_t tTimerConfig;

    am_hal_timer_default_config_set(&tTimerConfig);
    tTimerConfig.eInputClock = AM_HAL_TIMER_CLOCK_LFRC;     // ~900Hz

    //
    // Modify the default parameters.
    // Configure the timer to a suitable rate
    // dividy by 4 will scroll throught the sinewave table at 4 hz
    // with 64 entries it will run though the table in 16 seconds
    //
    tTimerConfig.eFunction = AM_HAL_TIMER_FN_UPCOUNT;
    tTimerConfig.ui32Compare1 = LFRC_TCIKS_SEC / 4;

    //
    // Configure the timer
    //
    uint32_t ui32Status = am_hal_timer_config(ui32TimerNumber, &tTimerConfig);
    if (ui32Status != AM_HAL_STATUS_SUCCESS)
    {
        am_util_stdio_printf("Failed to configure TIMER%d.\n", ui32TimerNumber);
        return ui32Status;
    }

    //
    // Clear the timer and its interrupt
    //
    am_hal_timer_clear(ui32TimerNumber);
    am_hal_timer_interrupt_clear(AM_HAL_TIMER_MASK(ui32TimerNumber, AM_HAL_TIMER_COMPARE1));
    //
    // Clear the timer Interrupt
    //
    NVIC_SetPriority((IRQn_Type) (ui32TimerNumber + TIMER0_IRQn), AM_IRQ_PRIORITY_DEFAULT);
    NVIC_EnableIRQ((IRQn_Type)(ui32TimerNumber + TIMER0_IRQn));

    //
    // Enable the timer Interrupt.
    //
    am_hal_timer_interrupt_enable(AM_HAL_TIMER_MASK(ui32TimerNumber, AM_HAL_TIMER_COMPARE1));

    am_hal_timer_enable(ui32TimerNumber);

    return AM_HAL_STATUS_SUCCESS;
} // init_timer()

void gpio_interrupt_init(int GPIO_PIN)
{
    // 1. 配置 GPIO 作为输入 + 使能中断
    am_hal_gpio_pincfg_t gpio_input_config =
    {
        .GP.cfg_b.uFuncSel            = 3,    //
        .GP.cfg_b.eDriveStrength      = AM_HAL_GPIO_PIN_DRIVESTRENGTH_0P1X,
        .GP.cfg_b.ePullup             = AM_HAL_GPIO_PIN_PULLUP_100K,
        .GP.cfg_b.ePowerSw            = AM_HAL_GPIO_PIN_POWERSW_NONE,
        .GP.cfg_b.eGPInput            = AM_HAL_GPIO_PIN_INPUT_ENABLE, // ???
        .GP.cfg_b.eGPOutCfg          	= AM_HAL_GPIO_PIN_OUTCFG_DISABLE, // ????
        .GP.cfg_b.eIntDir             = AM_HAL_GPIO_PIN_INTDIR_LO2HI, // ?????
    };
		
		am_hal_gpio_mask_t status;

    am_hal_gpio_pinconfig(GPIO_PIN, gpio_input_config);

    // 2. 清除中断标志位（防止误触发）
    //am_hal_gpio_interrupt_clear(AM_HAL_GPIO_INT_CHANNEL_0,&status);
		NVIC_ClearPendingIRQ((IRQn_Type)(GPIO0_001F_IRQn)); //clear pending flag

    // 3. 使能指定 GPIO 引脚中断
//    am_hal_gpio_interrupt_enable(AM_HAL_GPIO_BIT(GPIO_PIN));
		int num=GPIO_PIN;
		am_hal_gpio_interrupt_control(AM_HAL_GPIO_INT_CHANNEL_0,AM_HAL_GPIO_INT_CTRL_INDV_ENABLE,&num);//这个使能中断的方式真操蛋，直接传递引脚号多好，非得搞指针
		

    // 4. NVIC 中断向量使能（GPIO 为 GPIO_IRQn）
		NVIC_SetPriority((IRQn_Type)(GPIO0_001F_IRQn), AM_IRQ_PRIORITY_DEFAULT);
    //NVIC_EnableIRQ((IRQn_Type)(GPIO0_001F_IRQn));
}

void am_gpio0_001f_isr(void)
{
	am_hal_gpio_mask_t status;

  // 1. 读取中断状态
  am_hal_gpio_interrupt_status_get(AM_HAL_GPIO_INT_CHANNEL_0,true, &status);

  // 2. 判断目标 GPIO 是否触发中断
  if (status.U.Msk_b.b3 && timer_flag)
  {
    // ✅ 用户处理代码
		am_hal_gpio_interrupt_clear(AM_HAL_GPIO_INT_CHANNEL_0,&status);
		am_util_stdio_printf("GPIO中断触发！\r\n");
		hyn_tp_irq_handler();
		timer_flag=0;

    // 3. 清除中断标志
    
  }
	//am_util_stdio_printf("gpio0_001f_isr！\r\n");
}

static void gpio_init() {
	am_util_stdio_printf("Initializing GPIOs...\r\n");

    // ??GPIO5?????
    am_hal_gpio_pincfg_t gpio_output_config =
    {
        .GP.cfg_b.uFuncSel            = 3,    //
        .GP.cfg_b.eDriveStrength      = AM_HAL_GPIO_PIN_DRIVESTRENGTH_0P1X,
        .GP.cfg_b.ePullup             = AM_HAL_GPIO_PIN_PULLUP_NONE,
        .GP.cfg_b.ePowerSw            = AM_HAL_GPIO_PIN_POWERSW_NONE,
        .GP.cfg_b.eGPInput            = AM_HAL_GPIO_PIN_INPUT_NONE, // ???
        .GP.cfg_b.eGPOutCfg          	= AM_HAL_GPIO_PIN_OUTCFG_PUSHPULL, // ????
        .GP.cfg_b.eIntDir             = AM_HAL_GPIO_PIN_INTDIR_NONE, // ?????
    };
		
    am_hal_gpio_pincfg_t gpio_input_config =
    {
        .GP.cfg_b.uFuncSel            = 3,    //
        .GP.cfg_b.eDriveStrength      = AM_HAL_GPIO_PIN_DRIVESTRENGTH_0P1X,
        .GP.cfg_b.ePullup             = AM_HAL_GPIO_PIN_PULLUP_100K,
        .GP.cfg_b.ePowerSw            = AM_HAL_GPIO_PIN_POWERSW_NONE,
        .GP.cfg_b.eGPInput            = AM_HAL_GPIO_PIN_INPUT_ENABLE, // ???
        .GP.cfg_b.eGPOutCfg          	= AM_HAL_GPIO_PIN_OUTCFG_DISABLE, // ????
        .GP.cfg_b.eIntDir             = AM_HAL_GPIO_PIN_INTDIR_NONE, // ?????
    };

    // ?????GPIO5
    am_hal_gpio_pinconfig(79, gpio_output_config);//IO pin for the test LED
		am_hal_gpio_pinconfig(24, gpio_output_config);//IO pin for the DRV2605 IC PWM input
		//am_hal_gpio_pinconfig(19, gpio_output_config);//IO pin for the AMOLED power IC enable 
		am_hal_gpio_pinconfig(18, gpio_output_config);//IO pin for the APS6408 IC enable 
		am_hal_gpio_pinconfig(15, gpio_output_config);//IO pin for the DRV2605 IC enable 
//		am_hal_gpio_pinconfig(11, gpio_output_config);//IO pin for the OVOI IC SY5320 enable 
		am_hal_gpio_pinconfig(1, gpio_output_config);//LCD_RST,0-EN,1-DISABLE
		am_hal_gpio_pinconfig(7, gpio_output_config);//TP_RST,0-EN,1-DISABLE
		
		am_hal_gpio_pinconfig(17, gpio_input_config);//IO pin for the charger IC SY6105 int 
		am_hal_gpio_pinconfig(16, gpio_input_config);//IO pin for the gas gauge IC SY6302 int 
		am_hal_gpio_pinconfig(13, gpio_input_config);//IO pin for the  IC LSM6D int1 
		am_hal_gpio_pinconfig(12, gpio_input_config);//IO pin for the the  IC LSM6D int2 
		am_hal_gpio_pinconfig(10, gpio_input_config);//IO pin for the OVOI IC SY5320 WRO signal.
		am_hal_gpio_pinconfig(3, gpio_input_config);//TP_INT,
		am_hal_gpio_pinconfig(2, gpio_input_config);//SWIRE,

    // ??GPIO5?????
    am_hal_gpio_state_write(79, AM_HAL_GPIO_OUTPUT_SET);//turn on LED
		am_hal_gpio_state_write(24, AM_HAL_GPIO_OUTPUT_SET);//IO pin for the DRV2605 IC PWM input
		am_hal_gpio_state_write(19, AM_HAL_GPIO_OUTPUT_SET);// enable AMOLED power IC. 4.6V and -2.4V should be enabled.
		am_hal_gpio_state_write(18, AM_HAL_GPIO_OUTPUT_SET);// RESET_N=1, ENABLE THE PSRAM CHIP.
		am_hal_gpio_state_write(15, AM_HAL_GPIO_OUTPUT_SET);//IO pin for the DRV2605 IC enable 
//		am_hal_gpio_state_write(11, AM_HAL_GPIO_OUTPUT_CLEAR);// enable SY5320
//		am_hal_gpio_state_write(30, AM_HAL_GPIO_OUTPUT_CLEAR);
//		am_hal_gpio_state_write(91, AM_HAL_GPIO_OUTPUT_CLEAR);

		am_hal_gpio_state_write(1, AM_HAL_GPIO_OUTPUT_SET);//LCD_RST,0-EN,1-DISABLE
		am_hal_gpio_state_write(7, AM_HAL_GPIO_OUTPUT_SET);//TP_RST,0-EN,1-DISABLE


		am_util_stdio_printf("GPIOs Initialized!\r\n");

}

void psram_init() {
		uint32_t status;
		am_util_stdio_printf("测试PSRAM。。。\r\n");
				
			void *g_psramHandle = NULL;
			void *g_mspiHandle = NULL;
			
			
			uint8_t write_data[32] = "123412345678Hello PSRAM!";
			uint8_t read_data[32] = {0};

			am_devices_mspi_psram_config_t psramConfig = {
					.eDeviceConfig = AM_HAL_MSPI_FLASH_OCTAL_DDR_CE0,  // CE0 ?(???MSPI0)
					.eClockFreq = AM_HAL_MSPI_CLK_96MHZ,               // ????
					.pNBTxnBuf = (uint32_t*)write_data,
					.ui32NBTxnBufLength = 2,
					.ui32ScramblingStartAddr = 0,
					.ui32ScramblingEndAddr = 0
			};
			
			am_devices_mspi_psram_ddr_timing_config_t timing_config_t;
		
		status=am_devices_mspi_psram_aps12808l_ddr_init(0,
                                         &psramConfig,
                                         &g_psramHandle,
                                         &g_mspiHandle);
    // qingsi: start XIP
    if (am_devices_mspi_psram_aps12808l_ddr_enable_xip(g_psramHandle) != AM_DEVICES_MSPI_PSRAM_STATUS_SUCCESS) {
      am_util_stdio_printf("Failed to enable XIP mode!\n");
    }
		
		am_util_delay_ms(100);
		
		if (status != AM_DEVICES_MSPI_PSRAM_STATUS_SUCCESS)
    {
        am_util_stdio_printf("APS6408L 初始化失败\r\n");
    }
		else
		{
			uint32_t id = am_devices_mspi_psram_aps12808l_ddr_id(g_mspiHandle);
			am_util_stdio_printf("APS6408L ID 是：%x\r\n", id);
		}
		
//		am_devices_mspi_psram_aps12808l_ddr_init_timing_check(0,&psramConfig,&timing_config_t);
//		am_devices_mspi_psram_aps12808l_apply_ddr_timing(0,&timing_config_t);
		
		uint8_t psram_data[48] = "Hello PSRAM!";
		
		///prepare_test_pattern(0, psram_data, 48);
		
		uint32_t     ui32Rawdata;
		status=am_device_data_write(g_mspiHandle, AM_DEVICES_MSPI_PSRAM_OCTAL_DDR_WRITE, true, 0x400, (uint32_t*)(&write_data), 32);
		if (status != AM_DEVICES_MSPI_PSRAM_STATUS_SUCCESS)
    {
        am_util_stdio_printf("APS6408L 写入数据失败！\r\n");
    }
		
		status=am_device_data_read(g_mspiHandle, AM_DEVICES_MSPI_PSRAM_OCTAL_DDR_READ , true, 0x400, (uint32_t*)(&read_data), 32);
		if (status != AM_DEVICES_MSPI_PSRAM_STATUS_SUCCESS)
    {
        am_util_stdio_printf("APS6408L 读取数据失败！\r\n");
    }
		else
		{
			am_util_stdio_printf("APS6408L 读取的数据是：%s\r\n",read_data);
		}
		
		status=am_device_data_write(g_mspiHandle, AM_DEVICES_MSPI_PSRAM_OCTAL_DDR_WRITE, true, 0x400, (uint32_t*)(&psram_data), 32);
		if (status != AM_DEVICES_MSPI_PSRAM_STATUS_SUCCESS)
    {
        am_util_stdio_printf("APS6408L 写入数据失败！\r\n");
    }
		
		status=am_device_data_read(g_mspiHandle, AM_DEVICES_MSPI_PSRAM_OCTAL_DDR_READ , true, 0x400, (uint32_t*)(&read_data), 32);
		if (status != AM_DEVICES_MSPI_PSRAM_STATUS_SUCCESS)
    {
        am_util_stdio_printf("APS6408L 读取数据失败！\r\n");
    }
		else
		{
			am_util_stdio_printf("APS6408L 读取的数据是：%s\r\n",read_data);
		}
	
    psram_test();
}

void display_init() {
		am_util_stdio_printf("测试AMOLED。。。\r\n");
		
		am_hal_gpio_state_write(1, AM_HAL_GPIO_OUTPUT_CLEAR);//LCD_RST,0-EN,1-DISABLE
		am_hal_gpio_state_write(7, AM_HAL_GPIO_OUTPUT_CLEAR);//TP_RST,0-EN,1-DISABLE

    example_sh8601_init();
		
		uint32_t ui32ReadValue = 0;
		am_hal_gpio_state_read(2,AM_HAL_GPIO_INPUT_READ, &ui32ReadValue);
		if(ui32ReadValue)
		{
			AMOLED_POWER_EN();
			am_util_stdio_printf("AMOLED 初始化成功！\r\n");
		}
		else
		{
			//AMOLED_POWER_EN();
			am_util_stdio_printf("AMOLED 初始化失败！\r\n");
		}
		// show_photo(image_340x340);
		
		am_util_delay_ms(500);
   sh8601_set_brightness(0xFF);
	am_util_stdio_printf("降低亮度\n");

   am_util_delay_ms(500);
   sh8601_set_brightness(0xAA);
	 	am_util_stdio_printf("降低亮度\n");
		
		am_util_stdio_printf("绘制示例\n");

  
    example_draw_test_pattern(0, 0, 0xffff);
    am_util_delay_ms(10);
		example_draw_test_pattern(0, 100, 0xffff);
    am_util_delay_ms(10);
		example_draw_test_pattern(0, 200, 0xffff);
    am_util_delay_ms(10);
		example_draw_test_pattern(0, 239, 0xffff);
    am_util_delay_ms(10);
		example_draw_test_pattern(100, 0, 0xffff);
    am_util_delay_ms(10);
		example_draw_test_pattern(100, 100, 0xffff);
    am_util_delay_ms(10);
		example_draw_test_pattern(100, 200, 0xffff);
    am_util_delay_ms(10);
		example_draw_test_pattern(100, 239, 0xffff);
    am_util_delay_ms(10);
		example_draw_test_pattern(200, 0, 0xffff);
    am_util_delay_ms(10);
		example_draw_test_pattern(200, 100, 0xffff);
    am_util_delay_ms(10);
		example_draw_test_pattern(200, 200, 0xffff);
    am_util_delay_ms(10);
		example_draw_test_pattern(200, 239, 0xffff);
    am_util_delay_ms(10);
		example_draw_test_pattern(239, 0, 0xffff);
    am_util_delay_ms(10);
		example_draw_test_pattern(239, 100, 0xffff);
    am_util_delay_ms(10);
		example_draw_test_pattern(239, 200, 0xffff);
    am_util_delay_ms(10);
		example_draw_test_pattern(239, 239, 0xffff);
    am_util_delay_ms(10);


	//  example_draw_test_pattern(100, 100, 0xFFFF);
	  example_draw_test_pattern(150, 150, 0xF800);
    am_util_delay_ms(10);
	  example_draw_test_pattern(200, 200, 0x00F8);
    am_util_delay_ms(10);
   
}

void i2c_init() {
		uint8_t LED_EN=0;

		am_util_stdio_printf("Starting I2C devices test...\r\n");
		
		am_util_stdio_printf("Starting SY6302 defalut value read...\r\n");
		LED_EN=I2C_read_test(I2C0_MODULE,&g_pIOMHandle_0,I2C0_SCL_PIN, I2C0_SDA_PIN,0x55,0x80, 0X3D);// test done. SY6302 is working
		if(LED_EN)
		{
			am_util_stdio_printf("SY6302 read value correct!\r\n");
		}
		else
		{
			am_util_stdio_printf("SY6302 read back value is not the default value!\r\n");
		}
		
		//init_i2c(I2C1_MODULE,&g_pIOMHandle_1,I2C1_SCL_PIN, I2C1_SDA_PIN);
		//i2c_write_register(&g_pIOMHandle_1,0x07,0x50,0X40);
		am_util_stdio_printf("Starting SY6105 defalut value read...\r\n");
		LED_EN=I2C_read_test(I2C1_MODULE,&g_pIOMHandle_1,I2C1_SCL_PIN, I2C1_SDA_PIN,0x07,0x50, 0X40);//SY6105 not working, because of no battery.
		if(LED_EN)
		{
			am_util_stdio_printf("SY6105 read value correct!\r\n");
		}
		else
		{
			am_util_stdio_printf("SY6105 read back value is not the default value!\r\n");
		}
		am_util_delay_ms(10);
		
		am_util_stdio_printf("Starting LSM6D defalut value read...\r\n");
		LED_EN=I2C_read_test(I2C2_MODULE,&g_pIOMHandle_2,I2C2_SCL_PIN, I2C2_SDA_PIN,0x6a,0x0f, 0X6c);//test done. LSM6D is working.
		if(LED_EN)
		{
			am_util_stdio_printf("LSM6D read value correct!\r\n");
		}
		else
		{
			am_util_stdio_printf("LSM6D read back value is not the default value!\r\n");
		}
		am_util_delay_ms(10);
		am_util_stdio_printf("Starting DRV2605 defalut value read...\r\n");
		LED_EN=I2C_read_test(I2C3_MODULE,&g_pIOMHandle_3,I2C3_SCL_PIN, I2C3_SDA_PIN,0x5a,0x00, 0X60);//test done. DRV2605 is working.
		if(LED_EN)
		{
			am_util_stdio_printf("DRV2605 reg 0x00 default value read correct!\r\n");
		}
		else
		{
			am_util_stdio_printf("DRV2605 reg 0x00 default value changed!\r\n");
		}
		
		am_util_stdio_printf("Starting SIP1221LR1S ID value read...\r\n");
		//am_hal_gpio_state_write(7, AM_HAL_GPIO_OUTPUT_CLEAR);//TP_RST,0-EN,1-DISABLE
		am_util_delay_ms(100);
		LED_EN=I2C_read_test(I2C7_MODULE,&g_pIOMHandle_7,I2C7_SCL_PIN, I2C7_SDA_PIN,0x58,0x03, 0X06);//test done. DRV2605 is working.
		if(LED_EN)
		{
			am_util_stdio_printf("SIP1221LR1S reg 0x03 ID value read correct!\r\n");
		}
		else
		{
			am_util_stdio_printf("SIP1221LR1S reg 0x03 ID value read wrong!\r\n");
		}
		
		i2c_write_byte(&g_pIOMHandle_7, 0X58,0X05, 0X3);//OSC enable
		i2c_write_byte(&g_pIOMHandle_7, 0X58,0X06, 0X06);//INT_EN=1,INT output HIGH
		i2c_write_byte(&g_pIOMHandle_7, 0X58,0X50, 0X39);//enable ALS
		i2c_write_byte(&g_pIOMHandle_7, 0X58,0X81, 0X01);//enable ALS data beyond thresholds
		
		am_util_stdio_printf("Starting CST9217 ID value read...\r\n");
		uint8_t buf[256]={0};
		uint8_t rx_buf[8]={0};
//		buf[0]=0x01;
//		buf[1]=0xaa;
//		buf[2]=0xca;
//		i2c_write_bytes(&g_pIOMHandle_7, 0X5a, 0xa0, buf, 2);
//		i2c_write_bytes(&g_pIOMHandle_7, 0X5a, 0xa0, buf, 1);
//		//i2c_read_bytes(&g_pIOMHandle_7, 0X5a, 0xaa, buf, 2);
//		i2c_read_bytes_no_reg(&g_pIOMHandle_7, 0X5a, rx_buf, 3);
//		i2c_write_bytes(&g_pIOMHandle_7, 0X5a, 0xa0, buf, 1);
//		i2c_read_bytes_no_reg(&g_pIOMHandle_7, 0X5a, rx_buf, 3);
//		//0i2c_read_bytes(&g_pIOMHandle_7, 0X5a, 0xaa, buf, 2);
//		buf[0]=0x10;
//		buf[1]=0x01;
//		i2c_write_bytes(&g_pIOMHandle_7, 0X5a, 0xa0, buf, 2);
//		
//		buf[0]=0x0c;
//		buf[1]=0x7c;
//		buf[2]=0x07;
//		i2c_write_bytes(&g_pIOMHandle_7, 0X5a, 0xa0, buf, 3);
//		
//		buf[0]=0x04;
//		buf[1]=0xe4;
//		buf[2]=0x07;
//		i2c_write_bytes(&g_pIOMHandle_7, 0X5a, 0xa0, buf, 2);
//		buf[0]=0x18;
//		i2c_write_bytes(&g_pIOMHandle_7, 0X5a, 0xa0, buf, 1);
//		
//		
//		//i2c_read_bytes(&g_pIOMHandle_7, 0X5a, 0x20, buf, 4);
//		i2c_read_bytes_no_reg(&g_pIOMHandle_7, 0X5a, rx_buf, 4);
		 
		buf[0]=0x01;
		i2c_write_bytes(&g_pIOMHandle_7, 0X5a, 0xd1, buf, 1);
		buf[0]=0x04;
		i2c_write_bytes(&g_pIOMHandle_7, 0X5a, 0xd2, buf, 1);
		
		i2c_read_bytes_no_reg(&g_pIOMHandle_7, 0X5a, rx_buf, 4);
		buf[0]=0x09;
		i2c_write_bytes(&g_pIOMHandle_7, 0X5a, 0xd2, buf, 1);
		
		am_util_stdio_printf("CST9217 ID value:\r\n");
		for(int i=0;i<4;i++)
		{
			am_util_stdio_printf("%x\r\n",rx_buf[i]);
		}
		
		
		am_util_stdio_printf("I2C devices test done!\r\n");
		
}

void touch_init() {
		am_util_stdio_printf("初始化触摸屏...\r\n");
		gpio_interrupt_init(3);
		
		hyn_tp_init();
		NVIC_EnableIRQ((IRQn_Type)(GPIO0_001F_IRQn));//初始化屏幕后使能中断，否则误触发容易卡死

		am_util_stdio_printf("初始化触摸屏完成！\r\n");

}

void board_init() {
    //
    // Set the default cache configuration
    //
    
		am_hal_cachectrl_config(&am_hal_cachectrl_defaults);
    am_hal_cachectrl_enable();
		
	
    //
    // Configure the board for low power operation.
    //
    am_bsp_low_power_init();
		
		am_hal_mcuctrl_device_t sDevice;
    //am_hal_mcuctrl_info_get(AM_HAL_MCUCTRL_DEVICE_INFO, &sDevice);
		
		// qingsi: init STIMER for FPS measurement
		//am_hal_stimer_config(AM_HAL_STIMER_CFG_FREEZE | AM_HAL_STIMER_XTAL_32KHZ);
		//am_hal_stimer_config(AM_HAL_STIMER_LFRC_1KHZ);
		am_hal_stimer_config(AM_HAL_STIMER_HFRC_6MHZ);
		
		UART_init();

        gpio_init();

		am_util_stdio_printf("Initializing the timer...\r\n");

	//
    // Init the interrupt timer
    //
    
		init_timer(LED_CYCLE_TIMER);

    //
    // enable global interrupts
    //
    am_hal_interrupt_master_enable();

    //
    // Start the timers.
    //
		
    //am_hal_timer_start(AM_BSP_PWM_LED_TIMER);
	
    am_hal_timer_start(LED_CYCLE_TIMER);
		
		am_util_stdio_printf("The timer initialized!\r\n");

    psram_init();

    display_init();

    i2c_init();

    // Touch needs to be initialized last:
    // 1. It depends on I2C.
    // 2. To avoid triggering interrupts prematurely.
    touch_init();
}