/*
 * SH8601 LCD Driver Example Functions
 * Header file for example and wrapper functions
 */

#ifndef SH8601_EXAMPLE_H
#define SH8601_EXAMPLE_H

#include "sh8601_apollo4.h"

/**
 * @brief Initialize the SH8601 display with example configuration
 * 
 * @return sh8601_err_t Error code
 */
sh8601_err_t example_sh8601_init(void);

/**
 * @brief Draw a test pattern to the display
 * 
 * @return sh8601_err_t Error code
 */
sh8601_err_t example_draw_test_pattern(int x_start, int y_start, uint16_t color);

/**
 * @brief Clean up SH8601 example resources
 */
void example_sh8601_cleanup(void);

#endif // SH8601_EXAMPLE_H
