#ifndef BOARD_INIT_H
#define BOARD_INIT_H

#include "am_mcu_apollo.h"

#define TABLE_ENTRIES  64
#define TABLE_SCALE 256  ///< max value any table element can have

//
//! define global variables used in this module
//
typedef struct
{
    //
    //! scaled version of sine wave lookup table
    //! scaled so value can simply be dropped into timer1 compare register
    //
    uint32_t pui8BrightnessAdjusted[TABLE_ENTRIES];

    //
    //! This is the compare0 value,
    //! when the counter hits this value and rests
    //! (the value in compare1), that would 100% duty cycle
    //
    uint64_t ui64EndCounts;

    //
    //! index into sine wave lookup talble
    //
    uint32_t ui32Index;
    am_hal_timer_config_t tPwmTimerConfig;
    am_hal_timer_config_t tInterruptTimerConfig;

    //
    //! set in timer isr, advances duty cycle table
    //
    volatile bool bTimerIntOccured;

}
uart_async_local_vars_t;

extern uart_async_local_vars_t g_localv;

void board_init();

#endif // BOARD_INIT_H