#ifndef HYNITRON_CORE_H
#define HYNITRON_CORE_H

#include <stdbool.h>
#include <stdint.h>
#include <string.h>

// Function to be modified for drive migration ------------------------------edit start
// #include "stm32f10x.h"
#include "I2C.h"

#define FALSE 0
#define TRUE 1
#ifndef bool
#define bool char
#endif
#ifndef uint8_t
#define uint8_t unsigned char
#endif
#ifndef int8_t
#define int8_t signed char
#endif
#ifndef uint16_t
#define uint16_t unsigned short
#endif
#ifndef int16_t
#define int16_t signed short
#endif
#ifndef uint32_t
#define uint32_t unsigned int
#endif
#ifndef int32_t
#define int32_t signed int
#endif

// write OK ,return 0
// write NG ,return -1
// read OK  ,return 0
// read NG  ,return -1
//extern int16_t HYN_IIC_2_WriteBytes(uint8_t iic_addr, uint8_t *buff, uint16_t buff_len);
//extern int16_t HYN_IIC_2_ReadBytes(uint8_t iic_addr, uint8_t *buff, uint16_t buff_len);

#define RST_PIN_OUT_HIGH am_hal_gpio_state_write(7, AM_HAL_GPIO_OUTPUT_SET);//TP_RST,0-EN,1-DISABLE  // set RST pin high
#define RST_PIN_OUT_LOW am_hal_gpio_state_write(7, AM_HAL_GPIO_OUTPUT_CLEAR);//TP_RST,0-EN,1-DISABLE // set RST pin low
#define DELAY_MS(ms) am_util_delay_ms(ms)                                                        // delay 1ms
#define hyn_i2c_write_port(addr, buf, len) i2c7_write_bytes_no_reg(addr, buf, len)           // edit iic wirte function
#define hyn_i2c_read_port(addr, buf, len) i2c7_read_bytes_no_reg(addr, buf, len)             // edit iic read function

#define HYNITRON_DEBUG(fmt, args...) am_util_stdio_printf("hyn92xx[%d]:" fmt "\r\n", __LINE__, ##args);
#define HYNITRON_ERROR(fmt, args...) am_util_stdio_printf("hyn92xx[%d]err:" fmt "\r\n", __LINE__, ##args);
// #define HYNITRON_DEBUG(fmt, args...)
// #define HYNITRON_ERROR(fmt, args...)

#define HYN_FUNC_ENTER HYNITRON_DEBUG("%s enter", __func__);
#define HYN_FUNC_EXIT // HYNITRON_DEBUG("%s exit",__func__ );
// Function to be modified for drive migration ---------------------------------edit end

#define HYNITRON_FINGER_NUM (2)
#define HYNITRON_ENABLE_UPGRADE (1) //1-enable upgrade,
#define HYNITRON_I2C_ADDR (0x5A)
#define HYN_CHIP_TYPE (CST9217)
#define HYN_ENABLE_UPGRADE_128_BYTE_WRITE (0)

// factory test threshold
#define OPEN_HIGDRV_LOW_TH (400)
#define OPEN_HIGH_TH (600)
#define OPEN_LOW_TH (200)
#define SHORT_LOW_TH (500000)

// The following does not need to be modified
#define HYNITRON_DRIVER_VERSION "CST92xx_MCU_Driver_V3.5_20231204"
#define HYNITRON_I2C_TRAN_PER_SIZE (130)
#define HYNITRON_PROGRAM_PAGE_SIZE (128)
#define CST9217 (0x9217)
#define CST9220 (0x9220)
#define HYN_REPORT_TOUCH_CHECKSUM_EN (0)

// work mode
#define ENUM_MODE_NORMAL (0x00)
#define ENUM_MODE_LOW_POWER (0X01)
#define ENUM_MODE_DEEP_SLEEP (0X02)
#define ENUM_MODE_WAKEUP (0x03)
#define ENUM_MODE_DEBUG_DIFF (0x04)
#define ENUM_MODE_DEBUG_RAWDATA (0X05)
#define ENUM_MODE_FACTORY (0x06)
#define ENUM_MODE_DEBUG_INFO (0x07)
#define ENUM_MODE_UPDATE_FW (0x08)

#define TEST_SNS_OPEN_HIGHDRV (0x10)
#define TEST_SNS_OPEN_LOWDRV (0x11)
#define TEST_SNS_SHORT (0x12)
#define TEST_SNS_LPSCAN (0x13)
#define TEST_SYS_IDLE (0x14)
#define TEST_SYS_DEEPSLEEP (0x15)
#define TEST_IRQPIN_HIGH (0x16)
#define TEST_IRQPIN_LOW (0x17)
#define TEST_SCAP_SCANDATA (0x18)

#define DEEP_SLEEP (0x01)
#define IDLE_SLEEP (0x02)
#define GESTURE_SLEEP (0x03)

#define I2C_BOOT_ADDR (0x5A)
#define MEM_SIZE (0x7F80)
#define MEM_128_SIZE (128)
#define TRX_NUM (10 * 10)
#define TRX_KEY_NUM (20 + 8)

struct hyn_chip
{
    struct
    {
        uint8_t int_trig : 1;
        uint8_t update_fw_running : 1;
        uint8_t ic_init_done : 1;
        uint8_t esd_enable : 1;
        uint8_t sleep_done : 2;
        uint8_t ges_report_done : 1;
        uint8_t factory_test : 1;
    } status;
    uint8_t chip_ic_workmode;
    uint32_t chip_type;
    uint32_t partno_chip_type; // only read,not changed
    uint32_t module_id;        // only read,not changed
    uint8_t esd_chk_flag;
    uint32_t esd_value;
    uint32_t esd_value_pre;

    struct
    {
        bool firmware_info_ok;
        uint32_t firmware_ic_type;
        uint32_t firmware_version;
        uint32_t firmware_checksum;
        uint32_t firmware_project_id;
        uint8_t tx_num;
        uint8_t rx_num;
        uint8_t key_num;
    } IC_firmware;

    // file
    struct
    {
        bool ok;
        uint8_t *data;
        uint8_t data_seq;
        uint16_t data_len;
        uint32_t checksum;
        uint32_t version;
        uint32_t project_id;
        uint32_t chip_type;
    } bin_data;

    struct
    {
        bool valid;
        uint8_t finger_id;
        uint8_t evt;
        uint16_t x;
        uint16_t y;
        uint16_t z;
        uint16_t checksum;
    } point_info[HYNITRON_FINGER_NUM];

    struct
    {
        bool valid;
        uint8_t gesture;
        uint8_t palm;
        uint8_t finger_num;
        uint8_t key_id;
        uint8_t key_status;
    } touch_info;

    int16_t (*i2c_write)(uint8_t addr, uint8_t *buf, uint16_t len);
    int16_t (*i2c_read)(uint8_t addr, uint8_t *buf, uint16_t len);

    int16_t (*read_chip_id)(void);
    int16_t (*read_chip_info)(void);
    int16_t (*upgrade_firmware)(void);
    int16_t (*upgrade_firmware_judge)(void);
    int16_t (*bin_data_parse)(void);

    int16_t (*read_point)(void);
    int16_t (*enter_sleep)(uint8_t sleep_type);
    int16_t (*wake_up)(void);
    int16_t (*poweron_ic)(bool on);

    void (*reset_ic)(void);
    void (*esd_check)(void);
    void (*delay1ms)(uint16_t ms);

    int16_t (*get_firmware_info)(void);
    int16_t (*get_bin_addr)(uint8_t data_seq, uint16_t data_len);

    int16_t (*get_factory_test_result)(void);
    int16_t (*set_work_mode)(uint8_t mode);
    int16_t (*get_diff_data)(uint8_t *buff, uint16_t data_len);
    int16_t (*get_rawdata_data)(uint8_t *buff, uint16_t data_len);
};
extern struct hyn_chip *p_g_chip_obj;
extern void hyn_cst92xx_init_obj(struct hyn_chip *chip);

extern void hyn_tp_irq_handler(void);
extern int16_t hyn_tp_init(void);
int16_t hyn_i2c_write(uint8_t addr, uint8_t *buf, uint16_t len);
int16_t hyn_i2c_read(uint8_t addr, uint8_t *buf, uint16_t len);

void hyn_report_big_palm();
void hyn_report_gesture(void);
void hyn_report_finger(void);

#endif
