/*
 * SH8601 LCD Panel Driver for Ambiq Apollo4
 * Ported from ESP-IDF to AmbiqSuite HAL
 * Fixed implementation based on ESP32 reference
 */

#include <stdlib.h>
#include <string.h>
#include <stdbool.h>

#include "am_mcu_apollo.h"
#include "am_bsp.h"
#include "am_util.h"

#include "sh8601_apollo4.h"

// SH8601 Protocol Opcodes (from ESP32 reference)
// These opcodes tell the SH8601 how to interpret the data, not hardware modes
#define LCD_OPCODE_WRITE_CMD        (0x02ULL)  // Register/command write
#define LCD_OPCODE_READ_CMD         (0x03ULL)  // Register read (not used in this driver)
// 0x02 is for 1-wire pixel data; 0x32 for 4-wire pixel data.
#define LCD_OPCODE_WRITE_COLOR      (0x32ULL)  // Pixel data write

// Standard LCD Commands
#define LCD_CMD_SWRESET             0x01
#define LCD_CMD_MADCTL              0x36
#define LCD_CMD_COLMOD              0x3A
#define LCD_CMD_CASET               0x2A
#define LCD_CMD_RASET               0x2B
#define LCD_CMD_RAMWR               0x2C
#define LCD_CMD_INVON               0x21
#define LCD_CMD_INVOFF              0x20
#define LCD_CMD_DISPON              0x29
#define LCD_CMD_DISPOFF             0x28

#define LCD_CMD_BGR_BIT             0x08

static const char* TAG = "sh8601_apollo4";

static uint8_t cmd_buffer[16 + 400*400];

// Static DMA buffers for nonblocking transfers
#define SH8601_DMA_TCB_SIZE 1024
#define SH8601_HP_TRANSACTIONS_SIZE 256
static uint32_t g_dma_tcb[SH8601_DMA_TCB_SIZE];
static uint32_t g_hp_transactions[SH8601_HP_TRANSACTIONS_SIZE];

// Performance-optimized macros for common operations
#define SH8601_SET_COLUMN_ADDRESS(x_start, x_end) do { \
    uint8_t caset_data[4] = { \
        ((x_start) >> 8) & 0xFF, \
        (x_start) & 0xFF, \
        (((x_end) - 1) >> 8) & 0xFF, \
        ((x_end) - 1) & 0xFF \
    }; \
    if (mspi_write_cmd(g_panel, LCD_CMD_CASET, caset_data, 4) != AM_HAL_STATUS_SUCCESS) { \
        return SH8601_ERR_HAL_ERROR; \
    } \
} while(0)

#define SH8601_SET_ROW_ADDRESS(y_start, y_end) do { \
    uint8_t raset_data[4] = { \
        ((y_start) >> 8) & 0xFF, \
        (y_start) & 0xFF, \
        (((y_end) - 1) >> 8) & 0xFF, \
        ((y_end) - 1) & 0xFF \
    }; \
    if (mspi_write_cmd(g_panel, LCD_CMD_RASET, raset_data, 4) != AM_HAL_STATUS_SUCCESS) { \
        return SH8601_ERR_HAL_ERROR; \
    } \
} while(0)

#define SH8601_CALCULATE_DATA_LEN(x_start, x_end, y_start, y_end) \
    ((x_end) - (x_start)) * ((y_end) - (y_start)) * g_panel->fb_bits_per_pixel / 8

#define SH8601_VALIDATE_PARAMS(x_start, x_end, y_start, y_end, color_data) do { \
    if (!g_panel || !g_panel->flags.initialized) { \
        return SH8601_ERR_NOT_INIT; \
    } \
    if (!(color_data) || (x_start) >= (x_end) || (y_start) >= (y_end)) { \
        return SH8601_ERR_INVALID_ARG; \
    } \
} while(0)

// Panel structure - simplified to use only MSPI
typedef struct {
    // MSPI handle
    void* mspi_handle;
    uint32_t mspi_module;
    
    // GPIO pins
    int reset_pin;
    int cs_pin;
    
    // Configuration
    int x_gap;
    int y_gap;
    uint8_t fb_bits_per_pixel;
    uint8_t madctl_val;
    uint8_t colmod_val;
    
    // Init commands
    const sh8601_init_cmd_t* init_cmds;
    uint16_t init_cmds_size;
    
    // DMA configuration (pointers to static buffers)
    uint32_t* dma_tcb;                    // DMA Transfer Control Buffer
    uint32_t dma_tcb_size;                // TCB size in words
    volatile uint32_t dma_status;         // DMA completion status
    am_hal_mspi_callback_t dma_callback;  // DMA callback function
    
    // High priority transaction support (pointers to static buffers)
    uint32_t* hp_transactions;            // High priority transaction buffer
    uint32_t hp_transactions_size;        // HP buffer size
    
    // Flags
    struct {
        unsigned int use_qspi_interface: 1;
        unsigned int reset_active_high: 1;
        unsigned int initialized: 1;
    } flags;
} sh8601_panel_t;

// Static allocation instead of dynamic malloc
static sh8601_panel_t g_panel_static;
static sh8601_panel_t* g_panel = &g_panel_static;

// Helper function prototypes
static am_hal_status_e mspi_write_cmd(sh8601_panel_t* panel, uint8_t cmd, const uint8_t* data, uint16_t len);
static am_hal_status_e mspi_write_data(sh8601_panel_t* panel, const uint8_t* data, uint32_t len);
static void gpio_set_level(uint32_t pin, bool level);

void am_mspi1_isr(void)
{
    uint32_t ui32Status;
    
    // DEBUG: Add this line to see if ISR is called at all
    // am_util_stdio_printf("MSPI1 ISR called!\n");
    
    am_hal_mspi_interrupt_status_get(g_panel->mspi_handle, &ui32Status, false);
    // am_util_stdio_printf("Interrupt status: 0x%08X\n", ui32Status);
    
    am_hal_mspi_interrupt_clear(g_panel->mspi_handle, ui32Status);
    am_hal_mspi_interrupt_service(g_panel->mspi_handle, ui32Status);
}

static am_hal_mspi_dev_config_t g_mspi_dev_config = {
    .eDeviceConfig = AM_HAL_MSPI_FLASH_QUAD_CE0,
    .eClockFreq = AM_HAL_MSPI_CLK_48MHZ,
    .eSpiMode = AM_HAL_MSPI_SPI_MODE_0,
    .ui8TurnAround = 0,
    .eInstrCfg = AM_HAL_MSPI_INSTR_1_BYTE,
    .eAddrCfg = AM_HAL_MSPI_ADDR_3_BYTE,
    .ui16ReadInstr = 0,
    .ui16WriteInstr = 0,
    .bSendAddr = false,
    .bSendInstr = false,
    .bTurnaround = false,
    .bEnWriteLatency = false,
    .ui8WriteLatency = 0,
    .bEmulateDDR = false,
    .ui16DMATimeLimit = 0,
    .eDMABoundary = AM_HAL_MSPI_BOUNDARY_NONE
};

sh8601_err_t sh8601_init(const sh8601_config_t* config)
{
    if (!config) {
        return SH8601_ERR_INVALID_ARG;
    }

    // qingsi: use fxl's approach to reset the display IC.
    //RESET Signal for display driver
    am_hal_gpio_state_write(1, AM_HAL_GPIO_OUTPUT_SET);//LCD_RST,0-EN,1-DISABLE RST=1;
    am_util_delay_ms(1);
    am_hal_gpio_state_write(1, AM_HAL_GPIO_OUTPUT_CLEAR);//RST=0;	
    am_util_delay_ms(1);
    am_hal_gpio_state_write(1, AM_HAL_GPIO_OUTPUT_SET);//RST=1;	
    am_util_delay_ms(50);	
    
    
    // Clear the static structure
    memset(g_panel, 0, sizeof(sh8601_panel_t));
    
    // Copy configuration
    g_panel->reset_pin = config->reset_pin;
    g_panel->cs_pin = config->cs_pin;
    g_panel->mspi_module = config->mspi_module;
    g_panel->flags.reset_active_high = config->reset_active_high;
    g_panel->init_cmds = config->init_cmds;
    g_panel->init_cmds_size = config->init_cmds_size;
    
    // Configure color format
    switch (config->bits_per_pixel) {
        case 16: // RGB565
            g_panel->colmod_val = 0x55;
            g_panel->fb_bits_per_pixel = 16;
            break;
        case 18: // RGB666
            g_panel->colmod_val = 0x66;
            g_panel->fb_bits_per_pixel = 24;
            break;
        case 24: // RGB888
            g_panel->colmod_val = 0x77;
            g_panel->fb_bits_per_pixel = 24;
            break;
        default:
            return SH8601_ERR_NOT_SUPPORTED;
    }
    
    // Configure RGB order
    switch (config->rgb_order) {
        case SH8601_RGB_ORDER_RGB:
            g_panel->madctl_val = 0;
            break;
        case SH8601_RGB_ORDER_BGR:
            g_panel->madctl_val |= LCD_CMD_BGR_BIT;
            break;
        default:
            return SH8601_ERR_NOT_SUPPORTED;
    }
    
    // qingsi: we should check if already initialized

    // Initialize all MPSI1 device pins.
    am_hal_gpio_pinconfig(AM_BSP_GPIO_MSPI1_CE0, g_AM_BSP_GPIO_MSPI1_CE0);
    am_hal_gpio_pinconfig(AM_BSP_GPIO_MSPI1_SCK, g_AM_BSP_GPIO_MSPI1_SCK);
    am_hal_gpio_pinconfig(AM_BSP_GPIO_MSPI1_D0, g_AM_BSP_GPIO_MSPI1_D0);
    am_hal_gpio_pinconfig(AM_BSP_GPIO_MSPI1_D1, g_AM_BSP_GPIO_MSPI1_D1);
	am_hal_gpio_pinconfig(AM_BSP_GPIO_MSPI1_D2, g_AM_BSP_GPIO_MSPI1_D2);
    am_hal_gpio_pinconfig(AM_BSP_GPIO_MSPI1_D3, g_AM_BSP_GPIO_MSPI1_D3);
    
    // Initialize MSPI controller
    am_util_stdio_printf("Initializing MSPI module %d...\n", g_panel->mspi_module);
    if (am_hal_mspi_initialize(g_panel->mspi_module, &g_panel->mspi_handle) != AM_HAL_STATUS_SUCCESS) {
        am_util_stdio_printf("MSPI initialization failed!\n");
        return SH8601_ERR_HAL_ERROR;
    }
    am_util_stdio_printf("MSPI initialization successful\n");
    
    if (am_hal_mspi_power_control(g_panel->mspi_handle, AM_HAL_SYSCTRL_WAKE, false) != AM_HAL_STATUS_SUCCESS) {
        am_hal_mspi_deinitialize(g_panel->mspi_handle);
        am_util_stdio_printf("MSPI power control failed!\n");
        return SH8601_ERR_HAL_ERROR;
    }

    // qingsi: no need to select or deselect the CS pin. The HAL am_hal_mspi_blocking_transfer
    // takes care of that.
    
    // Assign static DMA buffers for nonblocking transfers
    g_panel->dma_tcb = g_dma_tcb;
    g_panel->dma_tcb_size = SH8601_DMA_TCB_SIZE;
    
    // Assign static high priority transaction buffer
    g_panel->hp_transactions = g_hp_transactions;
    g_panel->hp_transactions_size = SH8601_HP_TRANSACTIONS_SIZE;
    
    // Configure MSPI controller for DMA transfers
    am_hal_mspi_config_t mspi_config = {
        .ui32TCBSize = SH8601_DMA_TCB_SIZE,
        .pTCB = g_panel->dma_tcb,
        .bClkonD4 = false
    };
    
    if (am_hal_mspi_configure(g_panel->mspi_handle, &mspi_config) != AM_HAL_STATUS_SUCCESS) {
        am_hal_mspi_deinitialize(g_panel->mspi_handle);
        return SH8601_ERR_HAL_ERROR;
    }
    
    // Configure MSPI device - start with SPI mode for sending commands.
    am_util_stdio_printf("Initializing MSPI device with SPI mode");
    if (am_hal_mspi_device_configure(g_panel->mspi_handle, &g_mspi_dev_config) != AM_HAL_STATUS_SUCCESS) {
        am_util_stdio_printf("MSPI device configuration failed!\n");
        am_hal_mspi_deinitialize(g_panel->mspi_handle);
        return SH8601_ERR_HAL_ERROR;
    }
    am_util_stdio_printf("MSPI device configuration successful\n");
    
    // Enable MSPI controller
    if (am_hal_mspi_enable(g_panel->mspi_handle) != AM_HAL_STATUS_SUCCESS) {
        am_hal_mspi_deinitialize(g_panel->mspi_handle);
        return SH8601_ERR_HAL_ERROR;
    }
    
    // Enable MSPI interrupts for DMA operations
		// qingsi: make sure we have correct interrupt bits.
    am_hal_mspi_interrupt_clear(g_panel->mspi_handle, AM_HAL_MSPI_INT_CMDCMP | AM_HAL_MSPI_INT_CQUPD | AM_HAL_MSPI_INT_ERR);
    if (am_hal_mspi_interrupt_enable(g_panel->mspi_handle, AM_HAL_MSPI_INT_CMDCMP | AM_HAL_MSPI_INT_CQUPD | AM_HAL_MSPI_INT_ERR) != AM_HAL_STATUS_SUCCESS) {
        am_hal_mspi_disable(g_panel->mspi_handle);
        am_hal_mspi_deinitialize(g_panel->mspi_handle);
        am_util_stdio_printf("Failed to enable MSPI interrupts!\n");
        return SH8601_ERR_HAL_ERROR;
    }
		
		NVIC_EnableIRQ(MSPI1_IRQn);
		am_hal_interrupt_master_enable();
    
    // Mark as initialized
    g_panel->flags.initialized = 1;
    
    am_util_stdio_printf("SH8601 driver initialized successfully\n");
    return SH8601_OK;
}

sh8601_err_t sh8601_use_spi_mode() {
    if (g_panel->flags.initialized != 1) {
        am_util_stdio_printf("MSPI device not initalized\n");
        return SH8601_ERR_HAL_ERROR;
    }
    g_mspi_dev_config.eDeviceConfig = AM_HAL_MSPI_FLASH_SERIAL_CE0;
    // am_util_stdio_printf("Reconfiguring MSPI device with SPI mode\n");
    if (am_hal_mspi_device_configure(g_panel->mspi_handle, &g_mspi_dev_config) != AM_HAL_STATUS_SUCCESS) {
        am_util_stdio_printf("MSPI device configuration failed!\n");
        am_hal_mspi_deinitialize(g_panel->mspi_handle);
        return SH8601_ERR_HAL_ERROR;
    }
    // am_util_stdio_printf("MSPI is reconfigured to SPI mode\n");

    return SH8601_OK;
}

sh8601_err_t sh8601_use_qspi_mode() {
    if (g_panel->flags.initialized != 1) {
        am_util_stdio_printf("MSPI device not initalized\n");
        return SH8601_ERR_HAL_ERROR;
    }
    // qingsi: AM_HAL_MSPI_FLASH_QUAD_CE0_1_1_4 seems buggy Apollo4p. D1-D3 
    // seem to always send 1.
    g_mspi_dev_config.eDeviceConfig = AM_HAL_MSPI_FLASH_QUAD_CE0;
    // am_util_stdio_printf("Reconfiguring MSPI device with QSPI mode\n");
    if (am_hal_mspi_device_configure(g_panel->mspi_handle, &g_mspi_dev_config) != AM_HAL_STATUS_SUCCESS) {
        am_util_stdio_printf("MSPI device configuration failed!\n");
        am_hal_mspi_deinitialize(g_panel->mspi_handle);
        return SH8601_ERR_HAL_ERROR;
    }
    // am_util_stdio_printf("MSPI is reconfigured to QSPI mode\n");

    return SH8601_OK;
}

sh8601_err_t sh8601_reset(void)
{
    if (!g_panel) {
        return SH8601_ERR_NOT_INIT;
    }
    
    // Hardware reset if pin is configured
    if (g_panel->reset_pin >= 0) {
        am_util_stdio_printf("Hardware reset: pin %d\n", g_panel->reset_pin);
        am_util_stdio_printf("Reset: setting pin %s\n", g_panel->flags.reset_active_high ? "HIGH" : "LOW");
        gpio_set_level(g_panel->reset_pin, g_panel->flags.reset_active_high);
        am_util_delay_ms(10);
        am_util_stdio_printf("Reset: setting pin %s\n", g_panel->flags.reset_active_high ? "LOW" : "HIGH");
        gpio_set_level(g_panel->reset_pin, !g_panel->flags.reset_active_high);
        am_util_delay_ms(150);
        am_util_stdio_printf("Hardware reset completed\n");
    } else {
        // Software reset
        am_util_stdio_printf("Software reset via MSPI\n");
        if (mspi_write_cmd(g_panel, LCD_CMD_SWRESET, NULL, 0) != AM_HAL_STATUS_SUCCESS) {
            return SH8601_ERR_HAL_ERROR;
        }
        am_util_delay_ms(80);
        am_util_stdio_printf("Software reset completed\n");
    }
    
    return SH8601_OK;
}

sh8601_err_t sh8601_panel_init(void)
{
    if (!g_panel) {
        return SH8601_ERR_NOT_INIT;
    }

    // Sleep out.
    if (mspi_write_cmd(g_panel, 0x11, NULL, 0) != AM_HAL_STATUS_SUCCESS) {
        return SH8601_ERR_HAL_ERROR;
    }
    am_util_delay_ms(80);

    if (mspi_write_cmd(g_panel, 0x44, (uint8_t[]){0x01, 0xBF}, 2) != AM_HAL_STATUS_SUCCESS) {
        return SH8601_ERR_HAL_ERROR;
    }
   
    // TE On.
    if (mspi_write_cmd(g_panel, 0x35, (uint8_t[]){0x00}, 1) != AM_HAL_STATUS_SUCCESS) {
        return SH8601_ERR_HAL_ERROR;
    }
    
    // Send MADCTL
    if (mspi_write_cmd(g_panel, LCD_CMD_MADCTL, &g_panel->madctl_val, 1) != AM_HAL_STATUS_SUCCESS) {
        return SH8601_ERR_HAL_ERROR;
    }

    // Send COLMOD
    if (mspi_write_cmd(g_panel, LCD_CMD_COLMOD, (uint8_t[]){0x55}, 1) != AM_HAL_STATUS_SUCCESS) {
        return SH8601_ERR_HAL_ERROR;
    }

    // Write CTRL Display 1: brightness control on, dim off.
    if (mspi_write_cmd(g_panel, 0x53, (uint8_t[]){0x20}, 1) != AM_HAL_STATUS_SUCCESS) {
        return SH8601_ERR_HAL_ERROR;
    }

    am_util_delay_ms(10);

    // Write display brightness.
    if (mspi_write_cmd(g_panel, 0x51, (uint8_t[]){0x00}, 1) != AM_HAL_STATUS_SUCCESS) {
        return SH8601_ERR_HAL_ERROR;
    }

    am_util_delay_ms(10);

    // Display on.
    if (mspi_write_cmd(g_panel, 0x29, NULL, 0) != AM_HAL_STATUS_SUCCESS) {
        return SH8601_ERR_HAL_ERROR;
    }
    
    am_util_delay_ms(10);

    // Write display brightness.
    if (mspi_write_cmd(g_panel, 0x51, (uint8_t[]){0xFF}, 1) != AM_HAL_STATUS_SUCCESS) {
        return SH8601_ERR_HAL_ERROR;
    }

    am_util_stdio_printf("SH8601 panel initialization completed\n");
    return SH8601_OK;
}

// qingsi: the display expects the most signicant byte of pixel color sent first.
// We do not invert the byte order in each pixel color in this function, and the caller
// should take care of that.

sh8601_err_t sh8601_invert_color(bool invert)
{
    if (!g_panel || !g_panel->flags.initialized) {
        return SH8601_ERR_NOT_INIT;
    }
    
    uint8_t cmd = invert ? LCD_CMD_INVON : LCD_CMD_INVOFF;
    
    if (mspi_write_cmd(g_panel, cmd, NULL, 0) != AM_HAL_STATUS_SUCCESS) {
        return SH8601_ERR_HAL_ERROR;
    }
    
    return SH8601_OK;
}

sh8601_err_t sh8601_set_brightness(uint8_t brightness) {
    if (!g_panel || !g_panel->flags.initialized) {
        return SH8601_ERR_NOT_INIT;
    }

    if (mspi_write_cmd(g_panel, 0x51, (uint8_t[]){brightness}, 1) != AM_HAL_STATUS_SUCCESS) {
        return SH8601_ERR_HAL_ERROR;
    }
    
    return SH8601_OK;   
}

sh8601_err_t sh8601_mirror_x(bool mirror)
{
    if (!g_panel || !g_panel->flags.initialized) {
        return SH8601_ERR_NOT_INIT;
    }
    
    if (mirror) {
        g_panel->madctl_val |= (1 << 6);
    } else {
        g_panel->madctl_val &= ~(1 << 6);
    }
    
    if (mspi_write_cmd(g_panel, LCD_CMD_MADCTL, &g_panel->madctl_val, 1) != AM_HAL_STATUS_SUCCESS) {
        return SH8601_ERR_HAL_ERROR;
    }
    
    return SH8601_OK;
}

sh8601_err_t sh8601_set_gap(int x_gap, int y_gap)
{
    if (!g_panel) {
        return SH8601_ERR_NOT_INIT;
    }
    
    g_panel->x_gap = x_gap;
    g_panel->y_gap = y_gap;
    
    return SH8601_OK;
}

sh8601_err_t sh8601_display_on_off(bool on)
{
    if (!g_panel || !g_panel->flags.initialized) {
        return SH8601_ERR_NOT_INIT;
    }
    
    uint8_t cmd = on ? LCD_CMD_DISPON : LCD_CMD_DISPOFF;
    
    am_util_stdio_printf("Display %s command: 0x%02X\n", on ? "ON" : "OFF", cmd);
    
    if (mspi_write_cmd(g_panel, cmd, NULL, 0) != AM_HAL_STATUS_SUCCESS) {
        am_util_stdio_printf("Display %s command failed!\n", on ? "ON" : "OFF");
        return SH8601_ERR_HAL_ERROR;
    }
    
    am_util_stdio_printf("Display %s command sent successfully\n", on ? "ON" : "OFF");
    return SH8601_OK;
}

sh8601_err_t sh8601_all_pixels_on_off(bool on) {
    if (!g_panel || !g_panel->flags.initialized) {
        return SH8601_ERR_NOT_INIT;
    }
    
    uint8_t cmd = on ? 0x23: 0x22;
    
    if (mspi_write_cmd(g_panel, cmd, NULL, 0) != AM_HAL_STATUS_SUCCESS) {
        am_util_stdio_printf("All pixels %s command failed!\n", on ? "ON" : "OFF");
        return SH8601_ERR_HAL_ERROR;
    }
    
    am_util_stdio_printf("All pixels %s command sent successfully\n", on ? "ON" : "OFF");
    return SH8601_OK;
}

sh8601_err_t sh8601_deinit(void)
{
    if (!g_panel || !g_panel->flags.initialized) {
        return SH8601_ERR_NOT_INIT;
    }
    
    // Disable and deinitialize MSPI
    if (g_panel->mspi_handle) {
        am_hal_mspi_disable(g_panel->mspi_handle);
        am_hal_mspi_power_control(g_panel->mspi_handle, AM_HAL_SYSCTRL_DEEPSLEEP, false);
        am_hal_mspi_deinitialize(g_panel->mspi_handle);
    }
    
    // Clear static buffer pointers (no need to free static memory)
    g_panel->dma_tcb = NULL;
    g_panel->hp_transactions = NULL;
    
    // Reset GPIO pins
    if (g_panel->reset_pin >= 0) {
        am_hal_gpio_pinconfig(g_panel->reset_pin, am_hal_gpio_pincfg_disabled);
    }
    
    if (g_panel->cs_pin >= 0) {
        am_hal_gpio_pinconfig(g_panel->cs_pin, am_hal_gpio_pincfg_disabled);
    }
    
    // Mark as not initialized instead of freeing
    g_panel->flags.initialized = 0;
    
    am_util_stdio_printf("SH8601 driver deinitialized\n");
    return SH8601_OK;
}

// Helper function implementations

// The real protocol is:
// |WRITE OPCODE 0x02| 0x00 | CMD | 0x00| then followed by bytes of parameters.
// For example, setting pixel format to 16bit/pixel.
// 0x02 x00 x3A 0x00 0x55
static am_hal_status_e mspi_write_cmd(sh8601_panel_t* panel, uint8_t cmd, const uint8_t* data, uint16_t len)
{
    // Switch to SPI mode first.
    sh8601_use_spi_mode();

    am_hal_mspi_pio_transfer_t transaction = {0};
    am_hal_status_e status;
    
    // am_util_stdio_printf("MSPI CMD: 0x%02X, data_len: %d\n", cmd, len);

    uint32_t addr = (0x00 << 16) | (cmd << 8) | 0x00;
    transaction.ui32NumBytes = len;
    transaction.bScrambling = false;
    transaction.eDirection = AM_HAL_MSPI_TX;
    transaction.bSendInstr = true;
    transaction.ui16DeviceInstr = 0x02;
    transaction.bSendAddr = true;
    transaction.ui32DeviceAddr = addr;
    transaction.pui32Buffer = (uint32_t*)data;
    transaction.bContinue = false;
    
    status = am_hal_mspi_blocking_transfer(panel->mspi_handle, &transaction, 10000);

    sh8601_use_qspi_mode();
    return status;
}

static am_hal_status_e mspi_write_data(sh8601_panel_t* panel, const uint8_t* data, uint32_t len)
{

    // am_hal_gpio_pincfg_t gpio_output_config =
    // {
    //     .GP.cfg_b.uFuncSel            = 3,    //
    //     .GP.cfg_b.eDriveStrength      = AM_HAL_GPIO_PIN_DRIVESTRENGTH_0P1X,
    //     .GP.cfg_b.ePullup             = AM_HAL_GPIO_PIN_PULLUP_NONE,
    //     .GP.cfg_b.ePowerSw            = AM_HAL_GPIO_PIN_POWERSW_NONE,
    //     .GP.cfg_b.eGPInput            = AM_HAL_GPIO_PIN_INPUT_NONE, // ???
    //     .GP.cfg_b.eGPOutCfg          	= AM_HAL_GPIO_PIN_OUTCFG_PUSHPULL, // ????
    //     .GP.cfg_b.eIntDir             = AM_HAL_GPIO_PIN_INTDIR_NONE, // ?????
    // };
	

    // Take manual control of CE pin so between switching SPI and QSPI modes, no
    // extra chip deselect/select pair and the CE pin can be kept constant.
    //
    // This is a workaround. Otherwise, the data after the send command can be
    // not received.
	// am_hal_gpio_pinconfig(AM_BSP_GPIO_MSPI1_CE0, gpio_output_config);
    // Select.
    // am_hal_gpio_state_write(AM_BSP_GPIO_MSPI1_CE0, AM_HAL_GPIO_OUTPUT_CLEAR);

    // if (sh8601_use_qspi_mode() != SH8601_OK) {
    //     am_util_stdio_printf("Failed to use QSPI mode for writing data!");
    //     return SH8601_ERR_HAL_ERROR;
    // }

    am_hal_mspi_pio_transfer_t transaction = {0};
    am_hal_status_e status;
    
    // uint32_t total_len = 16 + len;

    // cmd_buffer[0] = 0x00; 
    // cmd_buffer[1] = 0x11;
    // cmd_buffer[2] = 0x00;
    // cmd_buffer[3] = 0x10;

    // cmd_buffer[4] = 0x00;
    // cmd_buffer[5] = 0x00;
    // cmd_buffer[6] = 0x00;
    // cmd_buffer[7] = 0x00;
    
    // cmd_buffer[8] = 0x00;
    // cmd_buffer[9] = 0x10;
    // cmd_buffer[10] = 0x11;
    // cmd_buffer[11] = 0x00;

    // cmd_buffer[12] = 0x00;
    // cmd_buffer[13] = 0x00;
    // cmd_buffer[14] = 0x00;
    // cmd_buffer[15] = 0x00;

    // memcpy(&cmd_buffer[16], data, len);
    // memset(cmd_buffer, 0x00, len);
    // transaction.ui32NumBytes = total_len;
    // transaction.bScrambling = false;
    // transaction.eDirection = AM_HAL_MSPI_TX;
    // transaction.bSendInstr = false;
    // transaction.ui16DeviceInstr = 0; //0x32; 
    // transaction.bSendAddr = false;
    // transaction.ui32DeviceAddr = 0; //0x002c00;
    // transaction.pui32Buffer = (uint32_t*)cmd_buffer;
    // transaction.bContinue = false;

   transaction.ui32NumBytes = len;
   transaction.bScrambling = false;
   transaction.eDirection = AM_HAL_MSPI_TX;
   transaction.bSendInstr = false;
   transaction.ui16DeviceInstr = 0; 
   transaction.bSendAddr = false;
   transaction.ui32DeviceAddr = 0;
   transaction.pui32Buffer = (uint32_t*)data;
   transaction.bContinue = false;

    status = am_hal_mspi_blocking_transfer(panel->mspi_handle, &transaction, 10000);
		
	if (status != SH8601_OK) {
		  am_util_stdio_printf("Failed to start writing data!");
		  return SH8601_ERR_HAL_ERROR;
	}

    // am_hal_mspi_pio_transfer_t xfer = {0};
    // xfer.ui32NumBytes = len;
    // xfer.bScrambling = false;
    // xfer.eDirection = AM_HAL_MSPI_TX;
    // xfer.bSendInstr = false;
    // xfer.ui16DeviceInstr = 0x00; 
    // xfer.bSendAddr = false;  // No address for data
    // xfer.ui32DeviceAddr = 0;
    // xfer.pui32Buffer = (uint32_t*)data;
    // xfer.bContinue = false;

    // status = am_hal_mspi_blocking_transfer(panel->mspi_handle, &xfer, 10000);

    // am_util_stdio_printf("MSPI DATA transfer completed, status=%d\n", status);

    // Deselect.
    // am_hal_gpio_state_write(AM_BSP_GPIO_MSPI1_CE0, AM_HAL_GPIO_OUTPUT_SET);

    // Give control back to MSPI controller.
    // am_hal_gpio_pinconfig(AM_BSP_GPIO_MSPI1_CE0, g_AM_BSP_GPIO_MSPI1_CE0);

    // if (sh8601_use_spi_mode() != SH8601_OK) {
    //     am_util_stdio_printf("Failed to switch back to SPI mode!");
    //     return SH8601_ERR_HAL_ERROR;
    // }

    // cmd_buffer[0] = LCD_OPCODE_WRITE_COLOR;  // 0x32
    // cmd_buffer[1] = 0x00;
    // cmd_buffer[2] = 0x00;
    // cmd_buffer[3] = 0x00;

    // am_hal_mspi_pio_transfer_t end_xfer = {0};

    // end_xfer.ui32NumBytes = 4;
    // end_xfer.bScrambling = false;
    // end_xfer.eDirection = AM_HAL_MSPI_TX;
    // end_xfer.bSendInstr = false;
    // end_xfer.ui16DeviceInstr = 0x00; 
    // end_xfer.bSendAddr = false;  // No address for data
    // end_xfer.ui32DeviceAddr = 0;
    // end_xfer.pui32Buffer = (uint32_t*)cmd_buffer;
    // end_xfer.bContinue = false;   

    // status = am_hal_mspi_blocking_transfer(panel->mspi_handle, &end_xfer, 10000);

	// if (status != SH8601_OK) {
	// 	  am_util_stdio_printf("Failed to end writing data!");
	// 	  return SH8601_ERR_HAL_ERROR;
	// }

    return status;
}

static void gpio_set_level(uint32_t pin, bool level)
{
    if (level) {
        am_hal_gpio_state_write(pin, AM_HAL_GPIO_OUTPUT_SET);
    } else {
        am_hal_gpio_state_write(pin, AM_HAL_GPIO_OUTPUT_CLEAR);
    }
}

// Callback implementations for DMA transfers

// Simple completion callback (like in aps12808l.c)
void sh8601_dma_callback(void *pCallbackCtxt, uint32_t status)
{
    // Set the DMA complete flag
    *(volatile uint32_t *)pCallbackCtxt = status;
    // Give control back to MSPI controller.
    // Deselect.
    am_hal_gpio_state_write(AM_BSP_GPIO_MSPI1_CE0, AM_HAL_GPIO_OUTPUT_SET);
    am_hal_gpio_pinconfig(AM_BSP_GPIO_MSPI1_CE0, g_AM_BSP_GPIO_MSPI1_CE0);
}

// More sophisticated callback with context
typedef struct {
    volatile bool transfer_complete;
    uint32_t transfer_status;
    void (*user_callback)(void* user_data, uint32_t status);
    void* user_data;
} sh8601_callback_context_t;

void sh8601_advanced_callback(void *pCallbackCtxt, uint32_t status)
{
    sh8601_callback_context_t* ctx = (sh8601_callback_context_t*)pCallbackCtxt;
    
    // Update status
    ctx->transfer_status = status;
    ctx->transfer_complete = true;
    
    // Call user callback if provided
    if (ctx->user_callback) {
        ctx->user_callback(ctx->user_data, status);
    }
}

// Nonblocking version of data write
static am_hal_status_e mspi_write_data_nonblocking(sh8601_panel_t* panel, 
                                                   const uint8_t* data, 
                                                   uint32_t len,
                                                   am_hal_mspi_callback_t callback,
                                                   void* callback_context)
{
    // Take manual control of CE.
    am_hal_gpio_pincfg_t gpio_output_config =
    {
        .GP.cfg_b.uFuncSel            = 3,    //
        .GP.cfg_b.eDriveStrength      = AM_HAL_GPIO_PIN_DRIVESTRENGTH_0P1X,
        .GP.cfg_b.ePullup             = AM_HAL_GPIO_PIN_PULLUP_NONE,
        .GP.cfg_b.ePowerSw            = AM_HAL_GPIO_PIN_POWERSW_NONE,
        .GP.cfg_b.eGPInput            = AM_HAL_GPIO_PIN_INPUT_NONE, // ???
        .GP.cfg_b.eGPOutCfg          	= AM_HAL_GPIO_PIN_OUTCFG_PUSHPULL, // ????
        .GP.cfg_b.eIntDir             = AM_HAL_GPIO_PIN_INTDIR_NONE, // ?????
    };
	am_hal_gpio_pinconfig(AM_BSP_GPIO_MSPI1_CE0, gpio_output_config);
    // Select.
    am_hal_gpio_state_write(AM_BSP_GPIO_MSPI1_CE0, AM_HAL_GPIO_OUTPUT_CLEAR);

    // Send write command.
    sh8601_use_spi_mode();

    am_hal_mspi_pio_transfer_t xfer = {0};
    am_hal_status_e s;

    xfer.ui32NumBytes = 0;
    xfer.bScrambling = false;
    xfer.eDirection = AM_HAL_MSPI_TX;
    xfer.bSendInstr = true;
    xfer.ui16DeviceInstr = 0x32;
    xfer.bSendAddr = true;
    xfer.ui32DeviceAddr = 0x002c00;
    xfer.pui32Buffer = NULL;
    xfer.bContinue = false;

    s = am_hal_mspi_blocking_transfer(panel->mspi_handle, &xfer, 10000);

    sh8601_use_qspi_mode();
    // Just for debug.
    // mspi_write_data(g_panel, data, len - 1);
    // mspi_write_data(g_panel, data, 1);

    // memset(cmd_buffer, 0xff, 4*len);

    am_hal_mspi_dma_transfer_t transaction = {0};
    am_hal_status_e status;
    
    // Configure DMA transaction (based on aps12808l.c pattern)
    transaction.ui8Priority = 1;                    // High priority
    transaction.eDirection = AM_HAL_MSPI_TX;        // Transmit
    // transaction.ui32TransferCount = len;            // Number of bytes
    transaction.ui32TransferCount = len;            // Number of bytes
    transaction.ui32DeviceAddress = 0;       // RAM write address
    transaction.ui32SRAMAddress = (uint32_t)data;   // Source buffer
    // transaction.ui32SRAMAddress = (uint32_t)cmd_buffer;   // Source buffer
    transaction.ui32PauseCondition = 0;             // No pause
    transaction.ui32StatusSetClr = 0;               // No status changes
// #if defined(AM_PART_APOLLO4)
//     transaction.eDeviceNum = AM_HAL_MSPI_DEVICE0;
// #endif
    
    // Start nonblocking DMA transfer
    status = am_hal_mspi_nonblocking_transfer(panel->mspi_handle, &transaction, 
                                             AM_HAL_MSPI_TRANS_DMA,
                                             callback, 
                                             callback_context);
    // Give control back to MSPI controller.
    // Deselect.
    // am_hal_gpio_state_write(AM_BSP_GPIO_MSPI1_CE0, AM_HAL_GPIO_OUTPUT_SET);
    // am_hal_gpio_pinconfig(AM_BSP_GPIO_MSPI1_CE0, g_AM_BSP_GPIO_MSPI1_CE0);
    
    return status;
}

// High priority version of command write
static am_hal_status_e mspi_write_cmd_highprio(sh8601_panel_t* panel, 
                                               uint8_t cmd, 
                                               const uint8_t* data, 
                                               uint16_t len,
                                               am_hal_mspi_callback_t callback,
                                               void* callback_context)
{
    // Switch to SPI mode for commands
    sh8601_use_spi_mode();
    
    am_hal_mspi_dma_transfer_t transaction = {0};
    am_hal_status_e status;
    
    // Prepare command buffer
    uint8_t cmd_buffer[16];
    uint32_t addr = (0x00 << 16) | (cmd << 8) | 0x00;
    
    if (len > 0 && data != NULL) {
        memcpy(cmd_buffer, data, len);
    }
    
    // Configure DMA transaction
    transaction.ui8Priority = 1;                    // High priority
    transaction.eDirection = AM_HAL_MSPI_TX;        // Transmit
    transaction.ui32TransferCount = len;            // Number of bytes
    transaction.ui32DeviceAddress = addr;           // Device address
    transaction.ui32SRAMAddress = (uint32_t)cmd_buffer; // Source buffer
    transaction.ui32PauseCondition = 0;             // No pause
    transaction.ui32StatusSetClr = 0;               // No status changes
#if defined(AM_PART_APOLLO4)
    transaction.eDeviceNum = AM_HAL_MSPI_DEVICE0;
#endif
    
    // Start high priority DMA transfer
    status = am_hal_mspi_highprio_transfer(panel->mspi_handle, &transaction, 
                                          AM_HAL_MSPI_TRANS_DMA,
                                          callback, 
                                          callback_context);
    
    // Switch back to QSPI mode
    sh8601_use_qspi_mode();
    
    return status;
}

// High priority version of data write
static am_hal_status_e mspi_write_data_highprio(sh8601_panel_t* panel, 
                                                const uint8_t* data, 
                                                uint32_t len,
                                                am_hal_mspi_callback_t callback,
                                                void* callback_context)
{
    am_hal_mspi_dma_transfer_t transaction = {0};
    am_hal_status_e status;
    
    // Configure DMA transaction
    transaction.ui8Priority = 1;                    // High priority
    transaction.eDirection = AM_HAL_MSPI_TX;        // Transmit
    transaction.ui32TransferCount = len;            // Number of bytes
    transaction.ui32DeviceAddress = 0x002c00;       // RAM write address
    transaction.ui32SRAMAddress = (uint32_t)data;   // Source buffer
    transaction.ui32PauseCondition = 0;             // No pause
    transaction.ui32StatusSetClr = 0;               // No status changes
#if defined(AM_PART_APOLLO4)
    transaction.eDeviceNum = AM_HAL_MSPI_DEVICE0;
#endif
    
    // Start high priority DMA transfer
    status = am_hal_mspi_highprio_transfer(panel->mspi_handle, &transaction, 
                                          AM_HAL_MSPI_TRANS_DMA,
                                          callback, 
                                          callback_context);
    
    return status;
}

// Optimized individual draw functions for maximum performance

// Optimized blocking bitmap draw function
sh8601_err_t sh8601_draw_bitmap(uint16_t x_start, uint16_t y_start, uint16_t x_end, uint16_t y_end, const void* color_data)
{
    // Validate parameters
    SH8601_VALIDATE_PARAMS(x_start, x_end, y_start, y_end, color_data);
    
    // Set display addresses
    SH8601_SET_COLUMN_ADDRESS(x_start, x_end);
    SH8601_SET_ROW_ADDRESS(y_start, y_end);
    
    // Calculate data length and transfer
    uint32_t len = SH8601_CALCULATE_DATA_LEN(x_start, x_end, y_start, y_end);
    
    if (mspi_write_data(g_panel, (const uint8_t*)color_data, len) != AM_HAL_STATUS_SUCCESS) {
        return SH8601_ERR_HAL_ERROR;
    }
    
    return SH8601_OK;
}

// Optimized nonblocking bitmap draw function
sh8601_err_t sh8601_draw_bitmap_nonblocking(uint16_t x_start, uint16_t y_start, 
                                           uint16_t x_end, uint16_t y_end, 
                                           const void* color_data,
                                           am_hal_mspi_callback_t callback,
                                           void* callback_context)
{
    // Validate parameters
    SH8601_VALIDATE_PARAMS(x_start, x_end, y_start, y_end, color_data);
    
    // Set display addresses
    SH8601_SET_COLUMN_ADDRESS(x_start, x_end);
    SH8601_SET_ROW_ADDRESS(y_start, y_end);
    
    // Calculate data length and transfer
    uint32_t len = SH8601_CALCULATE_DATA_LEN(x_start, x_end, y_start, y_end);
    
    if (mspi_write_data_nonblocking(g_panel, (const uint8_t*)color_data, len, 
                                   callback, callback_context) != AM_HAL_STATUS_SUCCESS) {
        return SH8601_ERR_HAL_ERROR;
    }
    
    return SH8601_OK;
}

// Optimized high priority bitmap draw function
sh8601_err_t sh8601_draw_bitmap_highprio(uint16_t x_start, uint16_t y_start, 
                                        uint16_t x_end, uint16_t y_end, 
                                        const void* color_data,
                                        am_hal_mspi_callback_t callback,
                                        void* callback_context)
{
    // Validate parameters
    SH8601_VALIDATE_PARAMS(x_start, x_end, y_start, y_end, color_data);
    
    // Set display addresses
    SH8601_SET_COLUMN_ADDRESS(x_start, x_end);
    SH8601_SET_ROW_ADDRESS(y_start, y_end);
    
    // Calculate data length and transfer
    uint32_t len = SH8601_CALCULATE_DATA_LEN(x_start, x_end, y_start, y_end);
    
    if (mspi_write_data_highprio(g_panel, (const uint8_t*)color_data, len, 
                                callback, callback_context) != AM_HAL_STATUS_SUCCESS) {
        return SH8601_ERR_HAL_ERROR;
    }
    
    return SH8601_OK;
}

/*
 * Usage Examples for Nonblocking DMA Transfers:
 * 
 * Example 1: Simple blocking wait
 * void example_simple_blocking(void)
 * {
 *     volatile uint32_t dma_status = 0xFFFFFFFF;
 *     
 *     // Start nonblocking transfer
 *     sh8601_draw_bitmap_nonblocking(0, 0, 100, 100, pixel_data, 
 *                                   sh8601_dma_callback, &dma_status);
 *     
 *     // Wait for completion
 *     while (dma_status == 0xFFFFFFFF) {
 *         am_util_delay_us(1);
 *     }
 *     
 *     if (dma_status == AM_HAL_STATUS_SUCCESS) {
 *         am_util_stdio_printf("Transfer completed successfully\n");
 *     } else {
 *         am_util_stdio_printf("Transfer failed with status: 0x%08X\n", dma_status);
 *     }
 * }
 * 
 * Example 2: Advanced callback with user data
 * void my_display_callback(void* user_data, uint32_t status)
 * {
 *     am_util_stdio_printf("Display update completed with status: 0x%08X\n", status);
 *     // Update UI, trigger next frame, etc.
 * }
 * 
 * void example_advanced_callback(void)
 * {
 *     sh8601_callback_context_t callback_ctx = {
 *         .transfer_complete = false,
 *         .transfer_status = 0,
 *         .user_callback = my_display_callback,
 *         .user_data = NULL
 *     };
 *     
 *     // Start nonblocking transfer
 *     sh8601_draw_bitmap_nonblocking(0, 0, 100, 100, pixel_data, 
 *                                   sh8601_advanced_callback, &callback_ctx);
 *     
 *     // Do other work while transfer is in progress
 *     // The callback will be called when complete
 * }
 * 
 * Example 3: High priority transfer (interrupts other transfers)
 * void example_high_priority(void)
 * {
 *     volatile uint32_t dma_status = 0xFFFFFFFF;
 *     
 *     // This will interrupt any ongoing regular transfers
 *     sh8601_draw_bitmap_highprio(0, 0, 100, 100, pixel_data, 
 *                                sh8601_dma_callback, &dma_status);
 *     
 *     // Wait for completion
 *     while (dma_status == 0xFFFFFFFF) {
 *         am_util_delay_us(1);
 *     }
 * }
 */