/*
 * SH8601 LCD Panel Driver for Ambiq Apollo4
 * Header file
 */

#ifndef SH8601_APOLLO4_H
#define SH8601_APOLLO4_H

#include <stdint.h>
#include <stdbool.h>

#include "am_hal_mspi.h"

// Error codes
typedef enum {
    SH8601_OK = 0,
    SH8601_ERR_INVALID_ARG = -1,
    SH8601_ERR_NO_MEM = -2,
    SH8601_ERR_NOT_SUPPORTED = -3,
    SH8601_ERR_HAL_ERROR = -4,
    SH8601_ERR_NOT_INIT = -5,
    SH8601_ERR_ALREADY_INIT = -6
} sh8601_err_t;

// Transfer type enumeration
typedef enum {
    SH8601_TRANSFER_BLOCKING = 0,
    SH8601_TRANSFER_NONBLOCKING,
    SH8601_TRANSFER_HIGHPRIO
} sh8601_transfer_type_t;

// RGB color order
typedef enum {
    SH8601_RGB_ORDER_RGB = 0,
    SH8601_RGB_ORDER_BGR = 1
} sh8601_rgb_order_t;

// Initialization command structure
typedef struct {
    uint8_t cmd;                    // Command byte
    const uint8_t* data;           // Command data
    uint16_t data_bytes;           // Number of data bytes
    uint16_t delay_ms;             // Delay after command (ms)
} sh8601_init_cmd_t;

// Configuration structure
typedef struct {
    // Hardware pins
    int reset_pin;                  // Reset GPIO pin (-1 to disable)
    int cs_pin;                     // Chip select pin (-1 for auto)
    
    // MSPI module
    uint32_t mspi_module;           // MSPI module number (0-2)
    
    // Display configuration
    uint8_t bits_per_pixel;        // 16, 18, or 24 bits per pixel
    sh8601_rgb_order_t rgb_order;  // RGB or BGR color order
    
    // Protocol configuration
    bool reset_active_high;        // Reset pin polarity
    
    // Custom initialization commands (optional)
    const sh8601_init_cmd_t* init_cmds;
    uint16_t init_cmds_size;
} sh8601_config_t;

// Function prototypes

/**
 * @brief Initialize the SH8601 driver
 * 
 * @param config Configuration structure
 * @return sh8601_err_t Error code
 */
sh8601_err_t sh8601_init(const sh8601_config_t* config);

/**
 * @brief Reset the display (hardware or software)
 * 
 * @return sh8601_err_t Error code
 */
sh8601_err_t sh8601_reset(void);

/**
 * @brief Initialize the display panel with commands
 * 
 * @return sh8601_err_t Error code
 */
sh8601_err_t sh8601_panel_init(void);

/**
 * @brief Draw a bitmap to the display
 * 
 * @param x_start Starting X coordinate
 * @param y_start Starting Y coordinate
 * @param x_end Ending X coordinate (exclusive)
 * @param y_end Ending Y coordinate (exclusive)
 * @param color_data Pointer to pixel data
 * @return sh8601_err_t Error code
 */
sh8601_err_t sh8601_draw_bitmap(uint16_t x_start, uint16_t y_start, uint16_t x_end, uint16_t y_end, const void* color_data);

/**
 * @brief Invert display colors
 * 
 * @param invert True to invert colors, false for normal
 * @return sh8601_err_t Error code
 */
sh8601_err_t sh8601_invert_color(bool invert);


sh8601_err_t sh8601_all_pixels_on_off(bool on);

sh8601_err_t sh8601_set_brightness(uint8_t brightness);

/**
 * @brief Mirror display horizontally
 * 
 * @param mirror True to enable X mirroring
 * @return sh8601_err_t Error code
 */
sh8601_err_t sh8601_mirror_x(bool mirror);

/**
 * @brief Set display coordinate gaps/offsets
 * 
 * @param x_gap X coordinate offset
 * @param y_gap Y coordinate offset
 * @return sh8601_err_t Error code
 */
sh8601_err_t sh8601_set_gap(int x_gap, int y_gap);

/**
 * @brief Turn display on or off
 * 
 * @param on True to turn display on, false for off
 * @return sh8601_err_t Error code
 */
sh8601_err_t sh8601_display_on_off(bool on);

/**
 * @brief Deinitialize the driver and free resources
 * 
 * @return sh8601_err_t Error code
 */
sh8601_err_t sh8601_deinit(void);

/**
 * @brief Draw a bitmap to the display using blocking transfer (Performance Optimized)
 * 
 * This function is optimized for maximum performance with minimal overhead.
 * Uses inline macros for common operations to reduce function call overhead.
 * Blocks until the entire bitmap transfer is complete.
 * 
 * @param x_start Starting X coordinate
 * @param y_start Starting Y coordinate
 * @param x_end Ending X coordinate (exclusive)
 * @param y_end Ending Y coordinate (exclusive)
 * @param color_data Pointer to pixel data
 * @return sh8601_err_t Error code
 */
sh8601_err_t sh8601_draw_bitmap(uint16_t x_start, uint16_t y_start, uint16_t x_end, uint16_t y_end, const void* color_data);

/**
 * @brief Draw a bitmap to the display using nonblocking DMA transfer (Performance Optimized)
 * 
 * This function is optimized for maximum performance with minimal overhead.
 * Uses inline macros for common operations to reduce function call overhead.
 * The transfer will complete asynchronously and call the provided callback when done.
 * 
 * @param x_start Starting X coordinate
 * @param y_start Starting Y coordinate
 * @param x_end Ending X coordinate (exclusive)
 * @param y_end Ending Y coordinate (exclusive)
 * @param color_data Pointer to pixel data
 * @param callback Callback function to call when transfer completes
 * @param callback_context Context data for callback
 * @return sh8601_err_t Error code
 */
sh8601_err_t sh8601_draw_bitmap_nonblocking(uint16_t x_start, uint16_t y_start, 
                                           uint16_t x_end, uint16_t y_end, 
                                           const void* color_data,
                                           am_hal_mspi_callback_t callback,
                                           void* callback_context);

/**
 * @brief Draw a bitmap to the display using high priority DMA transfer (Performance Optimized)
 * 
 * This function is optimized for maximum performance with minimal overhead.
 * Uses inline macros for common operations to reduce function call overhead.
 * High priority transfers can interrupt ongoing regular transfers.
 * 
 * @param x_start Starting X coordinate
 * @param y_start Starting Y coordinate
 * @param x_end Ending X coordinate (exclusive)
 * @param y_end Ending Y coordinate (exclusive)
 * @param color_data Pointer to pixel data
 * @param callback Callback function to call when transfer completes
 * @param callback_context Context data for callback
 * @return sh8601_err_t Error code
 */
sh8601_err_t sh8601_draw_bitmap_highprio(uint16_t x_start, uint16_t y_start, 
                                        uint16_t x_end, uint16_t y_end, 
                                        const void* color_data,
                                        am_hal_mspi_callback_t callback,
                                        void* callback_context);
																				
void sh8601_dma_callback(void *pCallbackCtxt, uint32_t status);

// Convenience macros
#define SH8601_INIT_DEFAULT() { \
    .reset_pin = -1, \
    .cs_pin = -1, \
    .mspi_module = 0, \
    .bits_per_pixel = 16, \
    .rgb_order = SH8601_RGB_ORDER_RGB, \
    .reset_active_high = false, \
    .init_cmds = NULL, \
    .init_cmds_size = 0 \
}

#endif // SH8601_APOLLO4_H