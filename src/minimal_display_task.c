#ifdef USE_MINIMAL_DISPLAY_TASK

#include "minimal_display_task.h"

#include "am_mcu_apollo.h"
#include "am_bsp.h"
#include "am_util.h"

#include "lvgl.h"

#include "FreeRTOS.h"
#include "task.h"

#include "nema_hal.h"
#include "nema_math.h"
#include "nema_core.h"
#include "nema_regs.h"
#include "nema_utils.h"
#include "nema_event.h"
#include "nema_graphics.h"
#include "nema_programHW.h"

#include "lv_gpu_ambiq_nema.h"

// Drivers.
#include "minimal_mem.h"
#include "sh8601_apollo4.h"

//*****************************************************************************
//
// Configuration
//
//*****************************************************************************
#define MY_DISPLAY_WIDTH        150
#define MY_DISPLAY_HEIGHT       150
#define MY_DISPLAY_COLOR_DEPTH  16      // RGB565

#define MY_PIXEL_SIZE       2
#define MY_COLOR_FORMAT     RGB565

#define MY_FRAMEBUFFER_SIZE     (MY_DISPLAY_WIDTH * MY_DISPLAY_HEIGHT * MY_PIXEL_SIZE)

//*****************************************************************************
//
// Global Variables
//
//*****************************************************************************
static lv_disp_draw_buf_t g_disp_buf;
static lv_disp_drv_t g_disp_drv;
static lv_color_t* g_framebuffer1 = NULL;
static lv_color_t* g_framebuffer2 = NULL;

TaskHandle_t DisplayTaskHandle;

//*****************************************************************************
//
// Minimal Flush Callback - Blocking Version
//
//*****************************************************************************
void minimal_blocking_flush_cb(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p)
{
    uint32_t width = area->x2 - area->x1 + 1;
    uint32_t height = area->y2 - area->y1 + 1;

    volatile uint32_t dma_status = 0xFFFFFFFF;
    sh8601_draw_bitmap_nonblocking(area->x1, area->y1, area->x2, area->y2, (uint8_t*)color_p, sh8601_dma_callback, (void*)&dma_status);
    while (dma_status == 0xFFFFFFFF) {
        am_util_delay_us(1);
    }
    
    if (dma_status != AM_HAL_STATUS_SUCCESS) {
        am_util_stdio_printf("Transfer failed with status: 0x%08X\n", dma_status);
        return;
    }
    
    // Inform LVGL that flushing is complete.
    lv_disp_flush_ready(disp_drv);
}

int minimal_display_setup(void)
{
    int ret;

    // Allocate framebuffers in PSRAM
    g_framebuffer1 = (lv_color_t*)minimal_psram_malloc(MY_FRAMEBUFFER_SIZE);
    if (g_framebuffer1 == NULL) {
        am_util_stdio_printf("Framebuffer 1 allocation failed!\n");
        return -1;
    }
    
    // Clear framebuffers
    memset(g_framebuffer1, 0, MY_FRAMEBUFFER_SIZE);
    
    // Initialize LVGL draw buffer
    // Use single bufferin for now and can give a second framebuffer later.
    lv_disp_draw_buf_init(&g_disp_buf, g_framebuffer1, NULL, 
                              MY_DISPLAY_WIDTH * MY_DISPLAY_HEIGHT);
    
    // Initialize display driver
    lv_disp_drv_init(&g_disp_drv);
    g_disp_drv.hor_res = MY_DISPLAY_WIDTH;
    g_disp_drv.ver_res = MY_DISPLAY_HEIGHT;
    g_disp_drv.draw_buf = &g_disp_buf;
    
    // Choose your flush callback implementation:
    g_disp_drv.flush_cb = minimal_blocking_flush_cb;    // Blocking version
    // g_disp_drv.flush_cb = my_nonblocking_flush_cb;   // Polling version
    // g_disp_drv.flush_cb = my_interrupt_flush_cb;     // Interrupt version
    
    // Optional callbacks for better performance and compatibility
    g_disp_drv.gpu_wait_cb = lv_gpu_ambiq_nema_wait;            // GPU synchronization
    // g_disp_drv.rounder_cb = my_rounder_cb;              // Coordinate alignment
    
    // Display mode settings
    g_disp_drv.full_refresh = 1;                        // 0 = partial refresh (more efficient)
    g_disp_drv.direct_mode = 0;                         // 0 = normal mode, 1 = direct mode
    
    // User data for advanced operations
    g_disp_drv.user_data = (void*)&g_framebuffer1;      // Reference to framebuffer
    
    // Register the display
    lv_disp_t* disp = lv_disp_drv_register(&g_disp_drv);
    if (disp == NULL) {
        am_util_stdio_printf("Display registration failed!\n");
        return -1;
    }
    
    am_util_stdio_printf("Minimal display setup done successfully\n");
    return 0;
}

void DisplayTask(void *pvParameters) {
    am_util_stdio_printf("Display task start!\n");

    int ret = minimal_display_setup();
    if (ret != 0) {
        am_util_stdio_printf("Minimal display setup failed!\n");
    }

    vTaskDelete(NULL);
}

#endif // USE_MINIMAL_DISPLAY_TASK
