# Minimal LVGL Integration Guide

## Overview
This guide shows how to integrate LVGL with your Apollo4 board using just a flush callback - no complex task architecture needed.

## What You Get
- ✅ Simple flush callback implementation
- ✅ PSRAM framebuffer allocation  
- ✅ Three transfer options (blocking, polling, interrupt)
- ✅ Minimal driver interface
- ✅ Easy to understand and modify

## What You Need to Implement

### 1. Basic Driver Functions (Required)

```c
// PSRAM functions
int my_psram_init(void);                    // Initialize PSRAM controller
void* my_psram_malloc(uint32_t size);       // Allocate framebuffer memory

// Display functions  
int my_display_init(uint32_t width, uint32_t height, color_format_t format);
int my_display_set_window(uint32_t x, uint32_t y, uint32_t width, uint32_t height);
```

### 2. Choose ONE Transfer Method

**Option A: Blocking (Easiest)**
```c
int my_display_write_pixels(uint8_t* data, uint32_t length);
```

**Option B: Non-blocking with Polling**
```c
int my_display_start_dma_transfer(uint8_t* data, uint32_t length);
bool my_display_is_transfer_complete(void);
```

**Option C: Interrupt-driven (Best Performance)**
```c
int my_display_start_dma_transfer_with_interrupt(uint8_t* data, uint32_t length);
void my_display_clear_dma_interrupt(void);
```

## Step-by-Step Integration

### Step 1: Add Files to Your Project

```
your_project/
├── src/
│   ├── minimal_display_port.c          # Main LVGL integration
│   ├── minimal_driver_example.c        # Your driver implementation
│   └── minimal_driver_interface.h      # Driver interface
├── main.c
└── Makefile
```

### Step 2: Update Your Makefile

```makefile
# Add source files
SRC += minimal_display_port.c
SRC += minimal_driver_example.c

# Add LVGL
SRC += $(LVGL_DIR)/src/core/*.c
SRC += $(LVGL_DIR)/src/hal/*.c
# ... other LVGL sources

# Add includes
INCLUDES += -I./src
INCLUDES += -I$(LVGL_DIR)
```

### Step 3: Configure LVGL

In `lv_conf.h`:
```c
#define LV_COLOR_DEPTH          16      // RGB565
#define LV_MEM_CUSTOM           1       // Use custom malloc
#define LV_MEM_CUSTOM_ALLOC     my_psram_malloc
#define LV_MEM_CUSTOM_FREE      my_psram_free
```

### Step 4: Initialize in Your Main

```c
#include "lvgl.h"
#include "minimal_display_port.h"

int main(void)
{
    // Initialize Apollo4
    am_hal_clkgen_control(AM_HAL_CLKGEN_CONTROL_SYSCLK_MAX, 0);
    am_bsp_low_power_init();
    am_hal_cachectrl_config(&am_hal_cachectrl_defaults);
    am_hal_cachectrl_enable();
    
    // Initialize LVGL
    lv_init();
    
    // Initialize display port
    if (my_minimal_display_init() != 0) {
        am_util_stdio_printf("Display init failed!\n");
        while(1);
    }
    
    // Create your UI
    lv_obj_t* label = lv_label_create(lv_scr_act());
    lv_label_set_text(label, "Hello Apollo4!");
    lv_obj_center(label);
    
    // Main loop
    while (1) {
        lv_timer_handler();     // Handle LVGL tasks
        am_util_delay_ms(5);    // 5ms delay = ~200 FPS max
    }
}
```

## Implementation Examples

### Example 1: Simple Blocking Implementation

```c
void my_minimal_flush_cb(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p)
{
    uint32_t width = area->x2 - area->x1 + 1;
    uint32_t height = area->y2 - area->y1 + 1;
    
    // Set display window
    my_display_set_window(area->x1, area->y1, width, height);
    
    // Write pixels (blocking)
    my_display_write_pixels((uint8_t*)color_p, width * height * 2);
    
    // Tell LVGL we're done
    lv_disp_flush_ready(disp_drv);
}
```

### Example 2: Non-blocking with State Machine

```c
typedef enum {
    DISPLAY_IDLE,
    DISPLAY_TRANSFERRING
} display_state_t;

static display_state_t g_display_state = DISPLAY_IDLE;
static lv_disp_drv_t* g_pending_drv = NULL;

void my_nonblocking_flush_cb(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p)
{
    // Check if previous transfer finished
    if (g_display_state == DISPLAY_TRANSFERRING) {
        if (my_display_is_transfer_complete()) {
            lv_disp_flush_ready(g_pending_drv);
            g_display_state = DISPLAY_IDLE;
        } else {
            return; // Still busy, try again later
        }
    }
    
    // Start new transfer
    uint32_t width = area->x2 - area->x1 + 1;
    uint32_t height = area->y2 - area->y1 + 1;
    
    my_display_set_window(area->x1, area->y1, width, height);
    my_display_start_dma_transfer((uint8_t*)color_p, width * height * 2);
    
    g_display_state = DISPLAY_TRANSFERRING;
    g_pending_drv = disp_drv;
}
```

### Example 3: Interrupt-driven

```c
static volatile lv_disp_drv_t* g_pending_drv = NULL;

void my_interrupt_flush_cb(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p)
{
    uint32_t width = area->x2 - area->x1 + 1;
    uint32_t height = area->y2 - area->y1 + 1;
    
    my_display_set_window(area->x1, area->y1, width, height);
    
    g_pending_drv = disp_drv;
    my_display_start_dma_transfer_with_interrupt((uint8_t*)color_p, width * height * 2);
    
    // Don't call lv_disp_flush_ready() - ISR will do it
}

// In your DMA ISR
void my_display_dma_isr(void)
{
    if (g_pending_drv) {
        lv_disp_flush_ready((lv_disp_drv_t*)g_pending_drv);
        g_pending_drv = NULL;
    }
}
```

## Performance Tips

### 1. Memory Alignment
```c
// Align framebuffers to cache lines (32 bytes)
void* my_psram_malloc(uint32_t size)
{
    size = (size + 31) & ~31;  // Round up to 32-byte boundary
    // ... allocate aligned memory
}
```

### 2. Cache Management
```c
void my_minimal_flush_cb(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p)
{
    // Flush cache before DMA transfer
    am_hal_cachectrl_cache_flush();
    
    // ... do transfer
    
    lv_disp_flush_ready(disp_drv);
}
```

### 3. Partial Refresh
```c
// Enable partial refresh for better performance
g_disp_drv.full_refresh = 0;
```

## Troubleshooting

### Display Shows Garbage
- Check color format (RGB565 vs RGB888)
- Verify byte order (some displays need byte swapping)
- Check display initialization commands

### Performance Issues  
- Use DMA instead of CPU transfers
- Enable cache and ensure proper alignment
- Use partial refresh instead of full screen

### Memory Issues
- Check PSRAM initialization
- Verify framebuffer allocation
- Monitor memory usage

## Advantages of Minimal Approach

✅ **Simple**: Easy to understand and debug  
✅ **Fast to implement**: Get running quickly  
✅ **Flexible**: Easy to modify for your hardware  
✅ **No RTOS dependency**: Works with or without FreeRTOS  

## When to Use Full Task-Based Approach

Consider the full implementation if you need:
- Maximum performance with complex UI
- Power optimization with display sleep
- Multiple display interfaces
- Advanced error recovery

## Files Provided

- `minimal_display_port.c` - Main LVGL integration with 3 flush callback options
- `minimal_driver_interface.h` - Simple driver interface you need to implement  
- `minimal_driver_example.c` - Example implementation for Apollo4
- `MINIMAL_INTEGRATION_GUIDE.md` - This guide

Start with the blocking implementation, then upgrade to interrupt-driven for better performance!
