//*****************************************************************************
//
//! @file minimal_mem.h
//!
//! @brief Minimal memory allocator for LVGL integration
//!
//*****************************************************************************

#ifndef MINIMAL_MEM_H
#define MINIMAL_MEM_H

#include <stdint.h>
#include <stdbool.h>

void* minimal_psram_malloc(size_t size);
void minimal_psram_free(void* ptr);
void* minimal_psram_realloc(void* ptr, size_t new_size);

#endif // MINIMAL_MEM_H
