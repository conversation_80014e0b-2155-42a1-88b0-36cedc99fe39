/*
 * SH8601 LCD Driver Usage Example
 * Demonstrates how to use the corrected SH8601 driver
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "am_mcu_apollo.h"
#include "am_bsp.h"
#include "am_util.h"
#include "sh8601_apollo4.h"
#include "sh8601_example.h"
#include "AMOLED.h"

#define FB_CMD_BYTES 16
#define FB_WIDTH_PIXELS 120
#define FB_HEIGHT_PIXELS 120
#define FB_BYTES_PER_PIXEL 2
#define FB_SIZE_BYTES (FB_CMD_BYTES + FB_WIDTH_PIXELS * FB_HEIGHT_PIXELS * FB_BYTES_PER_PIXEL)

#define PSRAM_BASE   0x14000000UL
#define TEST_ADDR    (PSRAM_BASE + 0x2000)   // pick an offset

static uint8_t* g_framebuffer =  (uint8_t *)TEST_ADDR;
// __attribute__((aligned(4))) static uint8_t g_framebuffer[FB_SIZE_BYTES];


// Example configuration for SH8601 display
static const sh8601_config_t sh8601_config = {
    .reset_pin = 1,                    // Reset pin (GPIO 1)
    .cs_pin = AM_BSP_GPIO_MSPI1_CE0,   // Chip select pin (from BSP)
    .mspi_module = 1,                  // Use MSPI1 module
    .bits_per_pixel = 16,              // 16-bit RGB565 color
    .rgb_order = SH8601_RGB_ORDER_RGB, // RGB color order
    .reset_active_high = false,        // Reset is active low
    .init_cmds = NULL,                 // Use default init sequence
    .init_cmds_size = 0
};

// Debug: Print configuration
static void print_config(void) {
    am_util_stdio_printf("SH8601 Config:\n");
    am_util_stdio_printf("  Reset pin: %d\n", sh8601_config.reset_pin);
    am_util_stdio_printf("  CS pin: %d (0x%x)\n", sh8601_config.cs_pin, sh8601_config.cs_pin);
    am_util_stdio_printf("  MSPI module: %d\n", sh8601_config.mspi_module);
    am_util_stdio_printf("  Bits per pixel: %d\n", sh8601_config.bits_per_pixel);
    am_util_stdio_printf("  RGB order: %s\n", sh8601_config.rgb_order == SH8601_RGB_ORDER_RGB ? "RGB" : "BGR");
    am_util_stdio_printf("  Reset active high: %s\n", sh8601_config.reset_active_high ? "Yes" : "No");
}

// Example function to initialize the SH8601 display
sh8601_err_t example_sh8601_init(void)
{
    sh8601_err_t err;
    
    am_util_stdio_printf("Initializing SH8601 display...\n");
    
    // Debug: Print configuration
    print_config();
    
    // Initialize the driver
    err = sh8601_init(&sh8601_config);
    if (err != SH8601_OK) {
        am_util_stdio_printf("Failed to initialize SH8601 driver: %d\n", err);
        return err;
    }

    err = sh8601_panel_init();
    if (err != SH8601_OK) {
        am_util_stdio_printf("Failed to initialize display panel: %d\n", err);
        return err;
    }
    
    // // Reset the display
    // am_util_stdio_printf("Step 2: Resetting display...\n");
    // err = sh8601_reset();
    // if (err != SH8601_OK) {
    //     am_util_stdio_printf("Failed to reset display: %d\n", err);
    //     return err;
    // }
    // am_util_stdio_printf("Step 2: Display reset SUCCESS\n");
    
    // // Initialize the panel with commands
    // am_util_stdio_printf("Step 3: Initializing panel...\n");
    // err = sh8601_panel_init();
    // if (err != SH8601_OK) {
    //     am_util_stdio_printf("Failed to initialize panel: %d\n", err);
    //     return err;
    // }
    // am_util_stdio_printf("Step 3: Panel initialization SUCCESS\n");
    
    // // Turn on the display
    // am_util_stdio_printf("Step 4: Turning on display...\n");
    // err = sh8601_display_on_off(true);
    // if (err != SH8601_OK) {
    //     am_util_stdio_printf("Failed to turn on display: %d\n", err);
    //     return err;
    // }
    // am_util_stdio_printf("Step 4: Display turned on SUCCESS\n");
    
    am_util_stdio_printf("SH8601 display initialized successfully!\n");

    // We send instruction+address+data as 4-wire data payload. The command
    // region in the framebuffer contains the effective instruction+address on
    // D0 (given how data are scrambled on 4 wires).
    memset(g_framebuffer, 0, FB_SIZE_BYTES);

    //          0x32        0x00        0x2c        0x00    
    // D0: 0011 0010 | 0000 0000 | 0010 1100 | 0000 0000
    // D1: 0000 0000 | 0000 0000 | 0000 0000 | 0000 0000
    // D2: 0000 0000 | 0000 0000 | 0000 0000 | 0000 0000
    // D3: 0000 0000 | 0000 0000 | 0000 0000 | 0000 0000

    g_framebuffer[0] = 0x00; 
    g_framebuffer[1] = 0x11;
    g_framebuffer[2] = 0x00;
    g_framebuffer[3] = 0x10;
 
    g_framebuffer[4] = 0x00;
    g_framebuffer[5] = 0x00;
    g_framebuffer[6] = 0x00;
    g_framebuffer[7] = 0x00;
 
    g_framebuffer[8] = 0x00;
    g_framebuffer[9] = 0x10;
    g_framebuffer[10] = 0x11;
    g_framebuffer[11] = 0x00;
 
    g_framebuffer[12] = 0x00;
    g_framebuffer[13] = 0x00;
    g_framebuffer[14] = 0x00;
    g_framebuffer[15] = 0x00;

    return SH8601_OK;
}

// Example function to draw a simple test pattern
sh8601_err_t example_draw_test_pattern(int x_start, int y_start, uint16_t color)
{
    sh8601_err_t err;

    static uint16_t g_fb_curr_color = 0;
    if (color != g_fb_curr_color){
        // No need to refill the same color.
        // memset((uint16_t*)(g_framebuffer + FB_CMD_BYTES), color, FB_WIDTH_PIXELS * FB_HEIGHT_PIXELS);
				uint16_t *fb = (uint16_t*)(g_framebuffer + FB_CMD_BYTES);
				for (size_t i = 0; i < FB_WIDTH_PIXELS * FB_HEIGHT_PIXELS; i++) {
					fb[i] = color;
				}
				g_fb_curr_color = color;
    }

    // err = sh8601_draw_bitmap(x_start, y_start, x_start + FB_WIDTH_PIXELS, y_start + FB_HEIGHT_PIXELS, g_framebuffer);
    // if (err != SH8601_OK) {
    //     am_util_stdio_printf("Failed to draw bitmap: %d\n", err);
    // }
    // return err;
    volatile uint32_t dma_status = 0xFFFFFFFF;
    sh8601_draw_bitmap_nonblocking(x_start, y_start, x_start + FB_WIDTH_PIXELS, y_start + FB_HEIGHT_PIXELS, g_framebuffer + FB_CMD_BYTES, sh8601_dma_callback, (void *)&dma_status);
    while (dma_status == 0xFFFFFFFF) {
        am_util_delay_us(1);
    }
    
    if (dma_status != AM_HAL_STATUS_SUCCESS) {
        am_util_stdio_printf("Transfer failed with status: 0x%08X\n", dma_status);
        return SH8601_ERR_HAL_ERROR;
    }
    return SH8601_OK;
}

// Example function to clean up
void example_sh8601_cleanup(void)
{
    am_util_stdio_printf("Cleaning up SH8601 driver...\n");
    
    // Turn off display
    sh8601_display_on_off(false);
    
    // Deinitialize driver
    sh8601_deinit();
    
    am_util_stdio_printf("SH8601 driver cleanup completed!\n");
}
