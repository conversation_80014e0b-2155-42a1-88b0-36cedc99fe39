// --------------------------------------------------------------------
// Copyright (c) 2025 by happystone Inc.,Seattle,USA.
// All rights reserved.
// --------------------------------------------------------------------
//
// Permission:
//
//   The user is granted permission to use and modify this code
//   for all boards based on the Ambiq Apollo4 MCU developed by Happystone Inc.
//   Any other use of this code-including but not limited to selling,
//   duplicating, or modifying any part of it-is strictly prohibited.
//
// Disclaimer:
//
//   This C/C++ source code is intended as a design reference
//   which illustrates how these types of functions can be implemented.
//   It is the user's responsibility to verify their design for
//   consistency and functionality through the use of formal
//   verification methods.  happystone Inc. provides no warranty regarding the use
//   or functionality of this code.
//
// --------------------------------------------------------------------
//
//                     happystone Inc.
//                     Seattle
//
//
//
//
//
// --------------------------------------------------------------------


#ifndef I2C_H
#define I2C_H

#include "am_mcu_apollo.h"
#include "am_bsp.h"
#include "am_util.h"
#include "am_hal_global.h"
#include "am_hal_gpio.h"

#include <string.h>

#define I2C0_MODULE     0  // ?? IOM0
#define I2C1_MODULE     1  // ?? IOM1
#define I2C2_MODULE     2  // ?? IOM2
#define I2C3_MODULE     3  // ?? IOM3
#define I2C7_MODULE     7  // ?? IOM7


#define I2C_SPEED      AM_HAL_IOM_100KHZ

#define I2C0_SCL_PIN    5
#define I2C0_SDA_PIN    6

#define I2C1_SCL_PIN    8
#define I2C1_SDA_PIN    9

#define I2C2_SCL_PIN    25
#define I2C2_SDA_PIN    26

#define I2C3_SCL_PIN    31
#define I2C3_SDA_PIN    32

#define I2C7_SCL_PIN    22
#define I2C7_SDA_PIN    23



extern void *g_pIOMHandle_0;//for I2C0
extern void *g_pIOMHandle_1;//for I2C1
extern void *g_pIOMHandle_2;//for I2C2
extern void *g_pIOMHandle_3;//for I2C3
extern void *g_pIOMHandle_7;//for I2C7

void init_i2c(uint32_t I2C_MODULE,void **g_pIOMHandle, uint32_t I2C_SCL_PIN,uint32_t I2C_SDA_PIN);
void i2c_write_byte(void **g_pIOMHandle, uint8_t I2C_SLAVE_ADDR,uint8_t reg, uint8_t value);
uint8_t i2c_read_byte(void **g_pIOMHandle, uint8_t I2C_SLAVE_ADDR,uint8_t reg);
uint8_t I2C_read_test(uint32_t I2C_MODULE,void **g_pIOMHandle, uint32_t I2C_SCL_PIN,uint32_t I2C_SDA_PIN,uint8_t I2C_SLAVE_ADDR,uint8_t reg, uint8_t read_value);

int32_t i2c_write_bytes(void **g_pIOMHandle, uint8_t I2C_SLAVE_ADDR, uint8_t reg, uint8_t *data, uint32_t len);
int32_t i2c_read_bytes(void **g_pIOMHandle, uint8_t I2C_SLAVE_ADDR, uint8_t reg, uint8_t *data, uint32_t len);

int32_t i2c_read_bytes_no_reg(void **g_pIOMHandle, uint8_t I2C_SLAVE_ADDR, uint8_t *data, uint32_t len);
int32_t i2c_write_bytes_no_reg(void **g_pIOMHandle, uint8_t I2C_SLAVE_ADDR, uint8_t *data, uint32_t len);

int32_t i2c7_write_bytes_no_reg(uint8_t I2C_SLAVE_ADDR, uint8_t *data, uint32_t len);
int32_t i2c7_read_bytes_no_reg(uint8_t I2C_SLAVE_ADDR, uint8_t *data, uint32_t len);


#endif // I2C_H