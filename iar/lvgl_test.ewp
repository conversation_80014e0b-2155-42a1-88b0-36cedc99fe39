<?xml version="1.0" encoding="UTF-8"?>
<project>
    <fileVersion>4</fileVersion>
    <configuration>
        <name>Debug</name>
        <toolchain>
            <name>ARM</name>
        </toolchain>
        <debug>1</debug>
        <settings>
            <name>General</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>36</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>BrowseInfoPath</name>
                    <state>Debug\BrowseInfo</state>
                </option>
                <option>
                    <name>ExePath</name>
                    <state>bin</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>bin</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>bin</state>
                </option>
                <option>
                    <name>GEndianMode</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Input description</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>Output description</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGCoreOrChip</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GRuntimeLibSelect</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>GRuntimeLibSelectSlave</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>RTDescription</name>
                    <state>Use the normal configuration of the C/C++ runtime library. No locale interface, C locale, no file descriptor support, no multibytes in printf and scanf, and no hex floats in strtod.</state>
                </option>
                <option>
                    <name>OGProductVersion</name>
                    <state>7.40.2.8567</state>
                </option>
                <option>
                    <name>OGLastSavedByProductVersion</name>
                    <state></state>
                </option>
                <option>
                    <name>OGChipSelectEditMenu</name>
                    <state>AMAP42KP-KBR	AmbiqMicro AMAP42KP-KBR</state>
                </option>
                <option>
                    <name>GenLowLevelInterface</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GEndianModeBE</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGBufferedTerminalOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenStdoutInterface</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RTConfigPath2</name>
                    <state>$TOOLKIT_DIR$\inc\c\DLib_Config_Normal.h</state>
                </option>
                <option>
                    <name>GBECoreSlave</name>
                    <version>33</version>
                    <state>39</state>
                </option>
                <option>
                    <name>OGUseCmsis</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGUseCmsisDspLib</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibThreads</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CoreVariant</name>
                    <version>33</version>
                    <state>39</state>
                </option>
                <option>
                    <name>GFPUDeviceSlave</name>
                    <state>AMAP42KP-KBR	AmbiqMicro AMAP42KP-KBR</state>
                </option>
                <option>
                    <name>FPU2</name>
                    <version>0</version>
                    <state>4</state>
                </option>
                <option>
                    <name>NrRegs</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>NEON</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GFPUCoreSlave2</name>
                    <version>33</version>
                    <state>39</state>
                </option>
                <option>
                    <name>OGCMSISPackSelectDevice</name>
                </option>
                <option>
                    <name>OgLibHeap</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGLibAdditionalLocale</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenLocaleTags</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLocaleDisplayOnly</name>
                    <state></state>
                </option>
                <option>
                    <name>DSPExtension</name>
                    <state>1</state>
                </option>
                <option>
                    <name>TrustZone</name>
                    <state>0</state>
                </option>
                <option>
                    <name>TrustZoneModes</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGAarch64Abi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OG_32_64Device</name>
                    <state>0</state>
                </option>
                <option>
                    <name>BuildFilesPath</name>
                    <state>Debug</state>
                </option>
                <option>
                    <name>PointerAuthentication</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FPU64</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OG_32_64DeviceCoreSlave</name>
                    <version>33</version>
                    <state>39</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICCARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>38</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>CCDefines</name>
                    <state>iar</state>
          <state>LV_AMBIQ_FB_USE_RGB565=1</state>
          <state>LD_LIBRARY_PATH=../../../../../third_party/ThinkSi/NemaGFX_SDK/lib</state>
          <state>NEMAGFX_SDK_PATH=../../../../../third_party/ThinkSi/NemaGFX_SDK</state>
          <state>NEMA_CUSTOM_MALLOC_INCLUDE="lv_ambiq_nema_hal.h"</state>
          <state>LV_AMBIQ_FB_RESY=390</state>
          <state>NEMA_PLATFORM=apollo4p_nemagfx</state>
          <state>LV_LVGL_H_INCLUDE_SIMPLE</state>
          <state>AM_PART_APOLLO4P</state>
          <state>LV_AMBIQ_FB_REFRESH_TWO</state>
          <state>AM_PACKAGE_BGA</state>
          <state>LV_CONF_INCLUDE_SIMPLE</state>
          <state>LV_AMBIQ_FB_RESX=390</state>
          <state>WAIT_IRQ_BINARY_SEMAPHORE=1</state>
          <state>AM_FREERTOS</state>
          <state>LV_AMBIQ_FB_USE_RGB888=0</state>
          <state>apollo4p_evb_disp_shield_rev2</state>
          <state>AM_UTIL_FAULTISR_PRINT</state>
          <state>NEMA_USE_CUSTOM_MALLOC</state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state>Pa050</state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>CCObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCAllowList</name>
                    <version>1</version>
                    <state>00000000</state>
                </option>
                <option>
                    <name>CCDebugInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IEndianMode</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IExtraOptionsCheck</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IExtraOptions</name>
                    <state>--no_path_in_file_macros</state>
                </option>
                <option>
                    <name>CCLangConformance</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCSignedPlainChar</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>CCLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>CCIncludePath2</name>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\include\tsi\NemaGFX</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\CMSIS\ARM\Include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\include\tsi\NemaVG</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\mcu\apollo4p</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\ambiq_support</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\include\tsi\NemaDC</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\CMSIS\AmbiqMicro\Include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\FreeRTOSv10.5.1\Source\include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\FreeRTOSv10.5.1\Source\portable\IAR\AMapollo4</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\config\apollo4p_nemagfx</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\gpu</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\common\mem</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\NemaGFX</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\NemaDC</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\devices</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\include\tsi\common</state>
          <state>$PROJ_DIR$\..\src</state>
          <state>$PROJ_DIR$\..\..\..\..\bsp</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\utils</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\mcu\apollo4p\hal</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src</state>
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCodeSection</name>
                    <state>.text</state>
                </option>
                <option>
                    <name>IProcessorMode2</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptLevel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptStrategy</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptLevelSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndRopi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndRwpi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndNoDynInit</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptimizationNoSizeConstraints</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCNoLiteralPool</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptStrategySlave</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CCGuardCalls</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccExceptions2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccRTTI2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OICompilerExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCStackProtection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPointerAutentiction</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCBranchTargetIdentification</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>AARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>12</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>AObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AEndian</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ACaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnWhat</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnOne</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange1</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange2</name>
                    <state></state>
                </option>
                <option>
                    <name>ADebug</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AltRegisterNames</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ADefines</name>
                    <state></state>
                </option>
                <option>
                    <name>AList</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AListHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AListing</name>
                    <state>1</state>
                </option>
                <option>
                    <name>Includes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacDefs</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacExps</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacExec</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OnlyAssed</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MultiLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLengthCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLength</name>
                    <state>80</state>
                </option>
                <option>
                    <name>TabSpacing</name>
                    <state>8</state>
                </option>
                <option>
                    <name>AXRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDefines</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefInternal</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDual</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AOutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>ALimitErrorsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ALimitErrorsEdit</name>
                    <state>100</state>
                </option>
                <option>
                    <name>AIgnoreStdInclude</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AUserIncludes</name>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\include\tsi\NemaGFX</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\CMSIS\ARM\Include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\include\tsi\NemaVG</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\mcu\apollo4p</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\ambiq_support</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\include\tsi\NemaDC</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\CMSIS\AmbiqMicro\Include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\FreeRTOSv10.5.1\Source\include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\FreeRTOSv10.5.1\Source\portable\IAR\AMapollo4</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\config\apollo4p_nemagfx</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\gpu</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\common\mem</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\NemaGFX</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\NemaDC</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\devices</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\include\tsi\common</state>
          <state>$PROJ_DIR$\..\src</state>
          <state>$PROJ_DIR$\..\..\..\..\bsp</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\utils</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\mcu\apollo4p\hal</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src</state>
                </option>
                <option>
                    <name>AExtraOptionsCheckV2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AExtraOptionsV2</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmNoLiteralPool</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>A_32_64Device</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>OBJCOPY</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>OOCOutputFormat</name>
                    <version>3</version>
                    <state>3</state>
                </option>
                <option>
                    <name>OCOutputOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OOCOutputFile</name>
                    <state>lvgl_test.bin</state>
                </option>
                <option>
                    <name>OOCCommandLineProducer</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCObjCopyEnable</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <hasPrio>0</hasPrio>
                <buildSequence>inputOutputBased</buildSequence>
            </data>
        </settings>
        <settings>
            <name>ILINK</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>27</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IlinkLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkInputFileSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOutputFile</name>
                    <state>lvgl_test.out</state>
                </option>
                <option>
                    <name>IlinkDebugInfoEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkKeepSymbols</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkConfigDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkMapFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogInitialization</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogModule</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogSection</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogVeneer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkIcfFile</name>
                    <state>$PROJ_DIR$\linker_script.icf</state>
                </option>
                <option>
                    <name>IlinkIcfFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkSuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsRem</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLowLevelInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAutoLibEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAdditionalLibs</name>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\config\apollo4p_nemagfx\iar\bin\lib_nema_apollo4p_nemagfx.a</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\ambiq_support\gpu_lib_apollo4\iar\bin\lvgl_ambiq_porting.a</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\mcu\apollo4p\hal\mcu\iar\bin\libam_hal.a</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\CMSIS\ARM\Lib\ARM\iar_cortexM4lf_math.a</state>
          <state>$PROJ_DIR$\..\..\..\..\bsp\iar\bin\libam_bsp.a</state>
                </option>
                <option>
                    <name>IlinkOverrideProgramEntryLabel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabel</name>
                    <state>__iar_program_start</state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>FillerStart</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>FillerEnd</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkBE8Slave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkBufferedTerminalOutput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkStdoutInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcFullSize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIElfToolPostProcess</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogAutoLibSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogRedirSymbols</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogUnusedFragments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcReverseByteOrder</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcUseAsInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptInline</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsAllow</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsForce</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptMergeDuplSections</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptUseVfe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptForceVfe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackAnalysisEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackControlFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkStackCallGraphFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CrcAlgorithm</name>
                    <version>1</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkThreadsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogCallGraph</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile_AltDefault</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLocaleSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkTrustzoneImportLibraryOut</name>
                    <state>###Unitialized###</state>
                </option>
                <option>
                    <name>OILinkExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLogCrtRoutineSelection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogFragmentInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInlining</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogMerging</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkDemangle</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkWrapperFileEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkWrapperFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkFpuProcessor</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IARCHIVE</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IarchiveInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>IarchiveOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IarchiveOutput</name>
                    <state>###Unitialized###</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>2</archiveVersion>
            <data />
        </settings>
    </configuration>
    <configuration>
        <name>Release</name>
        <toolchain>
            <name>ARM</name>
        </toolchain>
        <debug>0</debug>
        <settings>
            <name>General</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>36</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>BrowseInfoPath</name>
                    <state>Release\BrowseInfo</state>
                </option>
                <option>
                    <name>ExePath</name>
                    <state>bin</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>bin</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>bin</state>
                </option>
                <option>
                    <name>GEndianMode</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Input description</name>
                    <state>Automatic choice of formatter.</state>
                </option>
                <option>
                    <name>Output description</name>
                    <state>Automatic choice of formatter.</state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGCoreOrChip</name>
                    <state>2</state>
                </option>
                <option>
                    <name>GRuntimeLibSelect</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>GRuntimeLibSelectSlave</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>RTDescription</name>
                    <state>Use the normal configuration of the C/C++ runtime library. No locale interface, C locale, no file descriptor support, no multibytes in printf and scanf, and no hex floats in strtod.</state>
                </option>
                <option>
                    <name>OGProductVersion</name>
                    <state>7.40.2.8567</state>
                </option>
                <option>
                    <name>OGLastSavedByProductVersion</name>
                    <state></state>
                </option>
                <option>
                    <name>OGChipSelectEditMenu</name>
                    <state>AMAP42KP-KBR AmbiqMicro AMAP42KP-KBR</state>
                </option>
                <option>
                    <name>GenLowLevelInterface</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GEndianModeBE</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGBufferedTerminalOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenStdoutInterface</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RTConfigPath2</name>
                    <state>$TOOLKIT_DIR$\INC\c\DLib_Config_Normal.h</state>
                </option>
                <option>
                    <name>GBECoreSlave</name>
                    <version>33</version>
                    <state>39</state>
                </option>
                <option>
                    <name>OGUseCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGUseCmsisDspLib</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibThreads</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CoreVariant</name>
                    <version>33</version>
                    <state>39</state>
                </option>
                <option>
                    <name>GFPUDeviceSlave</name>
                    <state>AMAP42KP-KBR AmbiqMicro AMAP42KP-KBR</state>
                </option>
                <option>
                    <name>FPU2</name>
                    <version>0</version>
                    <state>4</state>
                </option>
                <option>
                    <name>NrRegs</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>NEON</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GFPUCoreSlave2</name>
                    <version>33</version>
                    <state>39</state>
                </option>
                <option>
                    <name>OGCMSISPackSelectDevice</name>
                </option>
                <option>
                    <name>OgLibHeap</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGLibAdditionalLocale</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenLocaleTags</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLocaleDisplayOnly</name>
                    <state></state>
                </option>
                <option>
                    <name>DSPExtension</name>
                    <state>0</state>
                </option>
                <option>
                    <name>TrustZone</name>
                    <state>0</state>
                </option>
                <option>
                    <name>TrustZoneModes</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGAarch64Abi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OG_32_64Device</name>
                    <state>0</state>
                </option>
                <option>
                    <name>BuildFilesPath</name>
                    <state>Release</state>
                </option>
                <option>
                    <name>PointerAuthentication</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FPU64</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OG_32_64DeviceCoreSlave</name>
                    <version>33</version>
                    <state>39</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICCARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>38</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>CCDefines</name>
                    <state>NDEBUG</state>
                    <state>iar</state>
          <state>LV_AMBIQ_FB_USE_RGB565=1</state>
          <state>LD_LIBRARY_PATH=../../../../../third_party/ThinkSi/NemaGFX_SDK/lib</state>
          <state>NEMAGFX_SDK_PATH=../../../../../third_party/ThinkSi/NemaGFX_SDK</state>
          <state>NEMA_CUSTOM_MALLOC_INCLUDE="lv_ambiq_nema_hal.h"</state>
          <state>LV_AMBIQ_FB_RESY=390</state>
          <state>NEMA_PLATFORM=apollo4p_nemagfx</state>
          <state>LV_LVGL_H_INCLUDE_SIMPLE</state>
          <state>AM_PART_APOLLO4P</state>
          <state>LV_AMBIQ_FB_REFRESH_TWO</state>
          <state>AM_PACKAGE_BGA</state>
          <state>LV_CONF_INCLUDE_SIMPLE</state>
          <state>LV_AMBIQ_FB_RESX=390</state>
          <state>WAIT_IRQ_BINARY_SEMAPHORE=1</state>
          <state>AM_FREERTOS</state>
          <state>LV_AMBIQ_FB_USE_RGB888=0</state>
          <state>apollo4p_evb_disp_shield_rev2</state>
          <state>AM_UTIL_FAULTISR_PRINT</state>
          <state>NEMA_USE_CUSTOM_MALLOC</state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>CCObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCAllowList</name>
                    <version>1</version>
                    <state>00000000</state>
                </option>
                <option>
                    <name>CCDebugInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IEndianMode</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>CCLangConformance</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCSignedPlainChar</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>CCLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>CCIncludePath2</name>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\include\tsi\NemaGFX</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\CMSIS\ARM\Include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\include\tsi\NemaVG</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\mcu\apollo4p</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\ambiq_support</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\include\tsi\NemaDC</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\CMSIS\AmbiqMicro\Include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\FreeRTOSv10.5.1\Source\include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\FreeRTOSv10.5.1\Source\portable\IAR\AMapollo4</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\config\apollo4p_nemagfx</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\gpu</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\common\mem</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\NemaGFX</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\NemaDC</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\devices</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\NemaGFX_SDK\include\tsi\common</state>
          <state>$PROJ_DIR$\..\src</state>
          <state>$PROJ_DIR$\..\..\..\..\bsp</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\utils</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\mcu\apollo4p\hal</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src</state>
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCodeSection</name>
                    <state>.text</state>
                </option>
                <option>
                    <name>IProcessorMode2</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptLevel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptStrategy</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptLevelSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndRopi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndRwpi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndNoDynInit</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptimizationNoSizeConstraints</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCNoLiteralPool</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptStrategySlave</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CCGuardCalls</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccExceptions2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccRTTI2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OICompilerExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCStackProtection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPointerAutentiction</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCBranchTargetIdentification</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>AARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>12</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>AObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AEndian</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ACaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnWhat</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnOne</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange1</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange2</name>
                    <state></state>
                </option>
                <option>
                    <name>ADebug</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AltRegisterNames</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ADefines</name>
                    <state></state>
                </option>
                <option>
                    <name>AList</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AListHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AListing</name>
                    <state>1</state>
                </option>
                <option>
                    <name>Includes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacDefs</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacExps</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacExec</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OnlyAssed</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MultiLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLengthCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLength</name>
                    <state>80</state>
                </option>
                <option>
                    <name>TabSpacing</name>
                    <state>8</state>
                </option>
                <option>
                    <name>AXRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDefines</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefInternal</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDual</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AOutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>ALimitErrorsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ALimitErrorsEdit</name>
                    <state>100</state>
                </option>
                <option>
                    <name>AIgnoreStdInclude</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AUserIncludes</name>
                    <state></state>
                </option>
                <option>
                    <name>AExtraOptionsCheckV2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AExtraOptionsV2</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmNoLiteralPool</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>A_32_64Device</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>OBJCOPY</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>OOCOutputFormat</name>
                    <version>3</version>
                    <state>3</state>
                </option>
                <option>
                    <name>OCOutputOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OOCOutputFile</name>
                    <state>lvgl_test.bin</state>
                </option>
                <option>
                    <name>OOCCommandLineProducer</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCObjCopyEnable</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <hasPrio>0</hasPrio>
                <buildSequence>inputOutputBased</buildSequence>
            </data>
        </settings>
        <settings>
            <name>ILINK</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>27</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IlinkLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkInputFileSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOutputFile</name>
                    <state>lvgl_test.out</state>
                </option>
                <option>
                    <name>IlinkDebugInfoEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkKeepSymbols</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkConfigDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkMapFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInitialization</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogModule</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogSection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogVeneer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile</name>
                    <state>$TOOLKIT_DIR$\config\linker\AmbiqMicro\apollo4_2048.icf</state>
                </option>
                <option>
                    <name>IlinkIcfFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkSuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsRem</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLowLevelInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAutoLibEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAdditionalLibs</name>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\config\apollo4p_nemagfx\iar\bin\lib_nema_apollo4p_nemagfx.a</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\ambiq_support\gpu_lib_apollo4\iar\bin\lvgl_ambiq_porting.a</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\mcu\apollo4p\hal\mcu\iar\bin\libam_hal.a</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\CMSIS\ARM\Lib\ARM\iar_cortexM4lf_math.a</state>
          <state>$PROJ_DIR$\..\..\..\..\bsp\iar\bin\libam_bsp.a</state>
                </option>
                <option>
                    <name>IlinkOverrideProgramEntryLabel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabel</name>
                    <state>__iar_program_start</state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>FillerStart</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>FillerEnd</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkBE8Slave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkBufferedTerminalOutput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkStdoutInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcFullSize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIElfToolPostProcess</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogAutoLibSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogRedirSymbols</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogUnusedFragments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcReverseByteOrder</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcUseAsInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptInline</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsAllow</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsForce</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptMergeDuplSections</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptUseVfe</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptForceVfe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackAnalysisEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackControlFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkStackCallGraphFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CrcAlgorithm</name>
                    <version>1</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkThreadsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogCallGraph</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile_AltDefault</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLocaleSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkTrustzoneImportLibraryOut</name>
                    <state>###Unitialized###</state>
                </option>
                <option>
                    <name>OILinkExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLogCrtRoutineSelection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogFragmentInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInlining</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogMerging</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkDemangle</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkWrapperFileEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkWrapperFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkFpuProcessor</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IARCHIVE</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IarchiveInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>IarchiveOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IarchiveOutput</name>
                    <state>###Unitialized###</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>2</archiveVersion>
            <data />
        </settings>
    </configuration>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\meter\lv_meter.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\keyboard\lv_keyboard.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\libs\fsdrv\lv_fs_fatfs.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\libs\fsdrv\lv_fs_posix.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\libs\fsdrv\lv_fs_stdio.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\libs\fsdrv\lv_fs_win32.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\animimg\lv_animimg.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\btn\lv_btn.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\slider\lv_slider.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\list\lv_list.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\spinner\lv_spinner.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\btnmatrix\lv_btnmatrix.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\calendar\lv_calendar.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\calendar\lv_calendar_header_arrow.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\calendar\lv_calendar_header_dropdown.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\table\lv_table.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\FreeRTOSv10.5.1\Source\portable\IAR\AMapollo4\port.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\FreeRTOSv10.5.1\Source\portable\IAR\AMapollo4\portasm.s</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\hal\lv_hal_disp.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\hal\lv_hal_indev.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\hal\lv_hal_tick.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\libs\sjpg\lv_sjpg.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\libs\sjpg\tjpgd.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\span\lv_span.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\tileview\lv_tileview.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\img\lv_img.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\roller\lv_roller.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\libs\png\lodepng.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\libs\png\lv_png.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\src\am_resources.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\src\gui_task.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\src\lvgl_test.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\src\rtos.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\tabview\lv_tabview.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\textarea\lv_textarea.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\led\lv_led.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\draw\lv_draw_arc.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\draw\lv_draw_blend.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\draw\lv_draw_img.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\draw\lv_draw_label.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\draw\lv_draw_line.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\draw\lv_draw_mask.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\draw\lv_draw_rect.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\draw\lv_draw_triangle.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\draw\lv_img_buf.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\draw\lv_img_cache.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\draw\lv_img_decoder.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\chart\lv_chart.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\objx_templ\lv_objx_templ.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\line\lv_line.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\checkbox\lv_checkbox.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\layouts\flex\lv_flex.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\libs\rlottie\lv_rlottie.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\msgbox\lv_msgbox.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\switch\lv_switch.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\others\snapshot\lv_snapshot.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\themes\default\lv_theme_default.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\bar\lv_bar.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\libs\bmp\lv_bmp.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_dejavu_16_persian_hebrew.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_fmt_txt.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_loader.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_10.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_12.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_12_subpx.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_14.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_16.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_18.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_20.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_22.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_24.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_26.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_28.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_28_compressed.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_30.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_32.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_34.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_36.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_38.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_40.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_42.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_44.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_46.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_48.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_montserrat_8.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_simsun_16_cjk.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_unscii_16.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\font\lv_font_unscii_8.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\colorwheel\lv_colorwheel.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\themes\mono\lv_theme_mono.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\spinbox\lv_spinbox.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\config\apollo4p_nemagfx\nema_dc_hal.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\config\apollo4p_nemagfx\nema_event.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\config\apollo4p_nemagfx\nema_hal.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\ThinkSi\config\apollo4p_nemagfx\nema_utils.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\win\lv_win.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\layouts\grid\lv_grid.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\themes\basic\lv_theme_basic.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\dropdown\lv_dropdown.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\arc\lv_arc.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\libs\qrcode\lv_qrcode.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\libs\qrcode\qrcodegen.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\label\lv_label.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\canvas\lv_canvas.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_anim.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_anim_timeline.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_area.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_async.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_bidi.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_color.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_fs.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_gc.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_ll.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_log.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_math.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_mem.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_printf.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_style.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_style_gen.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_templ.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_timer.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_tlsf.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_txt.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_txt_ap.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\misc\lv_utils.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\libs\gif\gifdec.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\libs\gif\lv_gif.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\widgets\imgbtn\lv_imgbtn.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\core\lv_disp.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\core\lv_event.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\core\lv_group.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\core\lv_indev.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\core\lv_indev_scroll.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\core\lv_obj.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\core\lv_obj_class.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\core\lv_obj_draw.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\core\lv_obj_pos.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\core\lv_obj_scroll.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\core\lv_obj_style.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\core\lv_obj_style_gen.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\core\lv_obj_tree.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\core\lv_refr.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\core\lv_theme.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\libs\freetype\lv_freetype.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\FreeRTOSv10.5.1\Source\portable\MemMang\heap_4.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\ambiq_support\display_task_cpu_only.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\ambiq_support\display_task_fake.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\ambiq_support\display_task_one_and_partial_fb.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\ambiq_support\display_task_one_fb.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\ambiq_support\display_task_two_fb.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\ambiq_support\lv_ambiq_decoder.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\ambiq_support\lv_ambiq_font_align.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\ambiq_support\lv_ambiq_fs.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\ambiq_support\lv_ambiq_misc.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\ambiq_support\lv_ambiq_nema_hal.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\ambiq_support\lv_ambiq_touch.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\devices\am_devices_display_generic.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\devices\am_devices_tma525.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\utils\am_util_delay.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\utils\am_util_faultisr.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\utils\am_util_id.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\utils\am_util_stdio.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\utils\am_util_syscalls.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\FreeRTOSv10.5.1\Source\event_groups.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\FreeRTOSv10.5.1\Source\list.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\FreeRTOSv10.5.1\Source\queue.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\FreeRTOSv10.5.1\Source\tasks.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\FreeRTOSv10.5.1\Source\timers.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\third_party\LVGL\lvgl\src\gpu\lv_gpu_ambiq_nema.c</name>
  </file>
    <file>
        <name>$PROJ_DIR$\startup_iar.c</name>
    </file>
</project>

