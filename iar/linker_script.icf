//*****************************************************************************
//
// linker_script.icf
//
// IAR linker Configuration File
//
//*****************************************************************************

//
// Define a memory section that covers the entire 4 GB addressable space of the
// processor. (32-bit can address up to 4GB)
//
define memory mem with size = 4G;

//
// Define regions for the various types of internal memory.
//
define region MCU_MRAM    = mem:[from 0x00018000 to 0x00200000];
define region MCU_TCM     = mem:[from 0x10000000 to 0x10060000];
define region SHARED_SRAM = mem:[from 0x10060000 to 0x10160000];

//
// Define blocks for logical groups of data.
//
define block HEAP with alignment = 0x8, size = 0x00000000 { };
define block CSTACK with alignment = 0x8, size = 2048 { };

define block FLASHBASE with fixed order
{
    readonly section .intvec,
    readonly section .patch
};

//
// Set section properties.
//
initialize by copy { readwrite };
initialize by copy { section SHARED_RW };
do not initialize { section .noinit };

//
// Place code sections in memory regions.
//
place at start of MCU_MRAM { block FLASHBASE };
place in MCU_MRAM { readonly };
place at start of MCU_TCM { block CSTACK, section .noinit };
place in MCU_TCM { block HEAP, readwrite };
place at start of SHARED_SRAM { section RESOURCE_TABLE };
place in SHARED_SRAM { section SHARED_RW };
