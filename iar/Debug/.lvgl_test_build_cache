<?xml version="1.0" encoding="UTF-8"?>
<BuildDb>
    <Tool>
        <Name>compiler</Name>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\libs\freetype\lv_freetype.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_freetype.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\chart\lv_chart.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_chart.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\core\lv_obj.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_obj.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\libs\sjpg\tjpgd.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\tjpgd.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\animimg\lv_animimg.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_animimg.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\src\gui_task.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\gui_task.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\libs\fsdrv\lv_fs_win32.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_fs_win32.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\startup_iar.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\startup_iar.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\libs\fsdrv\lv_fs_posix.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_fs_posix.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\switch\lv_switch.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_switch.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\FreeRTOSv10.5.1\Source\tasks.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\tasks.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\ambiq_support\lv_ambiq_font_align.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_ambiq_font_align.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\FreeRTOSv10.5.1\Source\queue.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\queue.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\calendar\lv_calendar_header_dropdown.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_calendar_header_dropdown.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\hal\lv_hal_disp.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_hal_disp.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_txt.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_txt.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\meter\lv_meter.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_meter.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\FreeRTOSv10.5.1\Source\timers.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\timers.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\gpu\lv_gpu_ambiq_nema.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_gpu_ambiq_nema.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\libs\fsdrv\lv_fs_fatfs.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_fs_fatfs.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\ambiq_support\lv_ambiq_touch.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_ambiq_touch.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\span\lv_span.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_span.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\libs\fsdrv\lv_fs_stdio.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_fs_stdio.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\draw\lv_draw_label.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_draw_label.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\keyboard\lv_keyboard.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_keyboard.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\draw\lv_draw_arc.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_draw_arc.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\textarea\lv_textarea.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_textarea.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\table\lv_table.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_table.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_8.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_8.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\hal\lv_hal_tick.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_hal_tick.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\libs\sjpg\lv_sjpg.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_sjpg.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_48.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_48.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\tileview\lv_tileview.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_tileview.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\draw\lv_draw_img.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_draw_img.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\hal\lv_hal_indev.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_hal_indev.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\list\lv_list.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_list.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\FreeRTOSv10.5.1\Source\portable\IAR\AMapollo4\port.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\port.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\btn\lv_btn.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_btn.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\slider\lv_slider.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_slider.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\spinner\lv_spinner.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_spinner.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\calendar\lv_calendar_header_arrow.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_calendar_header_arrow.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\others\snapshot\lv_snapshot.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_snapshot.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\roller\lv_roller.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_roller.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\btnmatrix\lv_btnmatrix.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_btnmatrix.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\draw\lv_draw_blend.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_draw_blend.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\calendar\lv_calendar.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_calendar.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\src\lvgl_test.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lvgl_test.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\libs\png\lv_png.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_png.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_10.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_10.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_28_compressed.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_28_compressed.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\src\am_resources.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\am_resources.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\libs\png\lodepng.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lodepng.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\src\rtos.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\rtos.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\tabview\lv_tabview.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_tabview.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\img\lv_img.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_img.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_24.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_24.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\libs\gif\lv_gif.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_gif.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\led\lv_led.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_led.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_28.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_28.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_34.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_34.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_22.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_22.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\draw\lv_draw_line.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_draw_line.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\line\lv_line.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_line.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_16.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_16.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_30.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_30.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\msgbox\lv_msgbox.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_msgbox.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_dejavu_16_persian_hebrew.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_dejavu_16_persian_hebrew.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_44.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_44.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_46.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_46.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\themes\mono\lv_theme_mono.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_theme_mono.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_unscii_16.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_unscii_16.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\win\lv_win.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_win.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\draw\lv_draw_rect.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_draw_rect.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\themes\basic\lv_theme_basic.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_theme_basic.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\draw\lv_img_decoder.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_img_decoder.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_12_subpx.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_12_subpx.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_unscii_8.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_unscii_8.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\core\lv_event.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_event.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_18.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_18.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_20.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_20.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_38.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_38.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\utils\am_util_stdio.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\am_util_stdio.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_12.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_12.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\ThinkSi\config\apollo4p_nemagfx\nema_dc_hal.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\nema_dc_hal.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\ThinkSi\config\apollo4p_nemagfx\nema_event.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\nema_event.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\layouts\grid\lv_grid.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_grid.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\draw\lv_draw_triangle.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_draw_triangle.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\objx_templ\lv_objx_templ.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_objx_templ.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\layouts\flex\lv_flex.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_flex.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\libs\rlottie\lv_rlottie.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_rlottie.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\bar\lv_bar.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_bar.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\draw\lv_img_cache.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_img_cache.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\core\lv_theme.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_theme.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\themes\default\lv_theme_default.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_theme_default.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\checkbox\lv_checkbox.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_checkbox.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_utils.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_utils.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_14.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_14.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_loader.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_loader.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_26.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_26.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_fmt_txt.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_fmt_txt.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_mem.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_mem.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_32.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_32.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_36.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_36.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_40.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_40.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_montserrat_42.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_montserrat_42.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\font\lv_font_simsun_16_cjk.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_font_simsun_16_cjk.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\spinbox\lv_spinbox.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_spinbox.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\ThinkSi\config\apollo4p_nemagfx\nema_hal.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\nema_hal.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\dropdown\lv_dropdown.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_dropdown.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\arc\lv_arc.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_arc.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\draw\lv_draw_mask.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_draw_mask.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\libs\bmp\lv_bmp.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_bmp.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\draw\lv_img_buf.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_img_buf.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\colorwheel\lv_colorwheel.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_colorwheel.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\ThinkSi\config\apollo4p_nemagfx\nema_utils.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\nema_utils.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\libs\qrcode\lv_qrcode.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_qrcode.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_anim.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_anim.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\FreeRTOSv10.5.1\Source\list.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\list.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\label\lv_label.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_label.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\libs\qrcode\qrcodegen.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\qrcodegen.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_style_gen.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_style_gen.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\core\lv_obj_style_gen.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_obj_style_gen.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_templ.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_templ.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\FreeRTOSv10.5.1\Source\event_groups.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\event_groups.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_async.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_async.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_log.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_log.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_txt_ap.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_txt_ap.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_style.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_style.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_timer.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_timer.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\canvas\lv_canvas.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_canvas.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_color.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_color.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_fs.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_fs.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\core\lv_indev_scroll.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_indev_scroll.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\core\lv_obj_class.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_obj_class.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\core\lv_indev.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_indev.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_printf.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_printf.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\libs\gif\gifdec.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\gifdec.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_math.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_math.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\utils\am_util_id.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\am_util_id.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_bidi.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_bidi.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_area.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_area.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_tlsf.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_tlsf.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_ll.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_ll.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\widgets\imgbtn\lv_imgbtn.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_imgbtn.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\core\lv_group.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_group.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\core\lv_obj_draw.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_obj_draw.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\core\lv_obj_pos.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_obj_pos.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_anim_timeline.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_anim_timeline.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\misc\lv_gc.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_gc.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\core\lv_obj_scroll.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_obj_scroll.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\core\lv_disp.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_disp.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\core\lv_obj_style.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_obj_style.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\utils\am_util_syscalls.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\am_util_syscalls.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\ambiq_support\display_task_one_fb.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\display_task_one_fb.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\ambiq_support\display_task_cpu_only.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\display_task_cpu_only.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\ambiq_support\lv_ambiq_misc.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_ambiq_misc.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\ambiq_support\display_task_fake.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\display_task_fake.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\utils\am_util_delay.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\am_util_delay.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\ambiq_support\display_task_one_and_partial_fb.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\display_task_one_and_partial_fb.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\ambiq_support\display_task_two_fb.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\display_task_two_fb.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\ambiq_support\lv_ambiq_fs.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_ambiq_fs.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\ambiq_support\lv_ambiq_nema_hal.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_ambiq_nema_hal.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\devices\am_devices_display_generic.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\am_devices_display_generic.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\devices\am_devices_tma525.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\am_devices_tma525.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\core\lv_obj_tree.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_obj_tree.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\ambiq_support\lv_ambiq_decoder.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_ambiq_decoder.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\LVGL\lvgl\src\core\lv_refr.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lv_refr.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\utils\am_util_faultisr.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\am_util_faultisr.lst</Path>
            </Output>
        </Parent>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\FreeRTOSv10.5.1\Source\portable\MemMang\heap_4.c</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\heap_4.lst</Path>
            </Output>
        </Parent>
    </Tool>
    <Tool>
        <Name>linker</Name>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lvgl_test.out</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lvgl_test.map</Path>
            </Output>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\lvgl_test.log</Path>
            </Output>
        </Parent>
    </Tool>
    <Tool>
        <Name>assembler</Name>
        <Parent>
            <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\third_party\FreeRTOSv10.5.1\Source\portable\IAR\AMapollo4\portasm.s</Path>
            <Output>
                <Path>C:\jenkins-agent\workspace\ambiqsuite4_rel\workspace\boards\apollo4p_evb_disp_shield_rev2\examples\graphics\lvgl_test\iar\bin\portasm.lst</Path>
            </Output>
        </Parent>
    </Tool>
</BuildDb>
