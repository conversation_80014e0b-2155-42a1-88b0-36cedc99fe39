{
    "version": "0.2.0",
    "configurations": [
        {
            "cwd": "${fileDirname}",
            "executable": "../gcc/bin/${fileBasenameNoExtension}.axf",
            "name": "Debug Microcontroller",
            "request": "launch",
            "type": "cortex-debug",
            "servertype": "jlink",
            // "serverpath": "C:/Program Files/SEGGER/JLink/JLinkGDBServerCL.exe", // Your installation path may vary
            "serverpath": "C:/Program Files/SEGGER/JLink_V862/JLinkGDBServerCL.exe",
            // "armToolchainPath": "C:/Program Files (x86)/Arm GNU Toolchain arm-none-eabi/13.2 Rel1/bin", // Your installation path may vary
            "armToolchainPath": "C:/Program Files (x86)/Arm GNU Toolchain arm-none-eabi/14.3 rel1/bin/", // Your installation path may vary
            "device": "AMAP42KP-KBR",  // For Apollo4p device selection
			 //"device": "AMAP42KK-KBR", // For Apollo4b device selection
			 //"device": "AMAP42KL-KBR", // For Apollo4l device selection
            "interface": "swd",
            "serialNumber": "", // If using external JLink, enter the serial number or nickname here
            "runToEntryPoint": "main",
            "svdFile": "${workspaceRoot}/../../../../../pack/SVD/apollo4p.svd", // For Apollo4p device selection
			//"svdFile": "${workspaceRoot}/../../../../../pack/SVD/apollo4b.svd", // For Apollo4b device selection
			//"svdFile": "${workspaceRoot}/../../../../../pack/SVD/apollo4l.svd", // For Apollo4l device selection
            "showDevDebugOutput": "both",
            "swoConfig": {
                "enabled": true,
                "decoders": [                    
                    { "type": "console", "label": "ITM", "port": 0, "encoding": "ascii" }
                ],
               // "cpuFrequency": 95105000, // This is usually measured, not specified
                "swoFrequency": 1000000,
                "source": "probe"
            },        }
    ]
}